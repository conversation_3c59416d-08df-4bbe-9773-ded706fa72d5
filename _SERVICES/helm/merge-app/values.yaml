imageName:
imageTag:

configmap: bentomergeservice
config:
  BENTO_DATABASE_NAME:
  BENTO_DATABASE_PASSWORD:
  BENTO_DATABASE_PORT:
  BENTO_DATABASE_SSL_FLAG:
  BENTO_DATABASE_URL:
  BENTO_DATABASE_USER:
  BENTO_DOCS_DATABASE_NAME:
  BENTO_DOCUMENTS_URL:
  CURRENT_ENV: "production"
  DATA_ENDPOINT:
  ICG_APIKEY:
  ICG_APIKEY_DREAM:
  ICG_GATEWAYLIVEMODE:
  ICG_SITEID:
  ICG_SITEID_DREAM:
  ICG_SITEKEY:
  ICG_SITEKEY_DREAM:
  MERGE_ENDPOINT:
  STRIPE_PK:
  STRIPE_SK:
  CSG_FORTE_API_ACCESS_ID: "f518bddbad0ff95d91a988675a765582"
  CSG_FORTE_SECURE_KEY: "76d62a1c850878be84bc1f51c5fff08a"
  CSG_FORTE_LOCATION_ID: "358310"
  CSG_FORTE_ENVIRONMENT: "sandbox"
  CSG_FORTE_API_ACCESS_ID_PROD: "********************************"
  CSG_FORTE_SECURE_KEY_PROD: "0a7c07b44ca61a6ca6f87ac776117e06"
  CSG_FORTE_LOCATION_ID_PROD: "358310"
  CSG_FORTE_ENVIRONMENT_PROD: "production"
  FINIX_APK_TEST:
  FINIX_APK_LIVE:

service:
  portName: http
  port: 8084
  type: ClusterIP

ingress:
  issuer: letsencrypt-prod-merge
  issuerSecret: tls-secret-merge
  issuerEmail: <EMAIL>
