kind: ConfigMap
apiVersion: v1
metadata:
  name: {{ .Values.configmap }}
  labels:
    app.kubernetes.io/instance: {{ .Chart.Name }}
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: {{ .Chart.Name }}
    app.kubernetes.io/version: {{ .Chart.Version }}
    helm.sh/chart: {{ .Chart.Name }}
data:
  BENTO_DATABASE_NAME: {{ .Values.config.BENTO_DATABASE_NAME | quote }}
  BENTO_DATABASE_PASSWORD: {{ .Values.config.BENTO_DATABASE_PASSWORD | quote}}
  BENTO_DATABASE_PORT: {{ .Values.config.BENTO_DATABASE_PORT | quote }}
  BENTO_DATABASE_SSL_FLAG: {{ .Values.config.BENTO_DATABASE_SSL_FLAG | quote }}
  BENTO_DATABASE_USER: {{ .Values.config.BENTO_DATABASE_USER | quote }}
  BENTO_DATABASE_URL: {{ .Values.config.BENTO_DATABASE_URL | quote }}
  BENTO_DOCS_DATABASE_NAME: {{ .Values.config.BENTO_DOCS_DATABASE_NAME | quote }}
  BENTO_DOCUMENTS_URL: {{ .Values.config.BENTO_DOCUMENTS_URL | quote }}
  DATA_ENDPOINT: {{ .Values.config.DATA_ENDPOINT | quote }}
  ICG_APIKEY: {{ .Values.config.ICG_APIKEY | quote }}
  ICG_GATEWAYLIVEMODE: {{ .Values.config.ICG_GATEWAYLIVEMODE | quote }}
  ICG_SITEID: {{ .Values.config.ICG_SITEID  | quote}}
  ICG_SITEKEY: {{ .Values.config.ICG_SITEKEY | quote }}
  MERGE_ENDPOINT: {{ .Values.config.MERGE_ENDPOINT | quote }}
  STRIPE_PK: {{ .Values.config.STRIPE_PK | quote }}
  STRIPE_SK: {{ .Values.config.STRIPE_SK | quote }}
  CSG_FORTE_API_ACCESS_ID: {{ .Values.config.CSG_FORTE_API_ACCESS_ID | quote }}
  CSG_FORTE_SECURE_KEY: {{ .Values.config.CSG_FORTE_SECURE_KEY | quote }}
  CSG_FORTE_LOCATION_ID: {{ .Values.config.CSG_FORTE_LOCATION_ID | quote }}
  CSG_FORTE_ENVIRONMENT: {{ .Values.config.CSG_FORTE_ENVIRONMENT | quote }}
  CSG_FORTE_API_ACCESS_ID_PROD: {{ .Values.config.CSG_FORTE_API_ACCESS_ID_PROD | quote }}
  CSG_FORTE_SECURE_KEY_PROD: {{ .Values.config.CSG_FORTE_SECURE_KEY_PROD | quote }}
  CSG_FORTE_LOCATION_ID_PROD: {{ .Values.config.CSG_FORTE_LOCATION_ID_PROD | quote }}
  CSG_FORTE_ENVIRONMENT_PROD: {{ .Values.config.CSG_FORTE_ENVIRONMENT_PROD | quote }}
  CURRENT_ENV: {{ .Values.config.CURRENT_ENV | quote }}
  ICG_SITEID_DREAM: {{ .Values.config.ICG_SITEID_DREAM | quote }}
  ICG_SITEKEY_DREAM: {{ .Values.config.ICG_SITEKEY_DREAM | quote }}
  ICG_APIKEY_DREAM: {{ .Values.config.ICG_APIKEY_DREAM | quote }}
  FINIX_APK_TEST: {{ .Values.config.FINIX_APK_TEST | quote }}
  FINIX_APK_LIVE: {{ .Values.config.FINIX_APK_LIVE | quote }}
