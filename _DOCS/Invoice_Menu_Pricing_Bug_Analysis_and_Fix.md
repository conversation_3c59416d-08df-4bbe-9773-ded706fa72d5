# Invoice Menu Pricing Bug: Analysis and Resolution Guide

## Executive Summary

**Bug Classification**: Critical Data Integrity Issue  
**Root Cause**: Incorrect parameter usage in `setMenuPricingRecord.php` causing menu ID/proposal ID confusion  
**Impact**: Creates blank menus with empty sections, breaking invoice pricing calculations  
**Detection Pattern**: Empty `sections: []` array in recently created menus with pricing breakdowns showing `total: 0`

## Technical Analysis

### The Bug Mechanism

The issue occurs in `_SRC/pagoda/rules/actions/setMenuPricingRecord.php` at lines 795-798:

```php
$proposal = $objs->getById('proposals', intval($obj['id']));
$menu = $objs->getById(
    'inventory_menu'
    , intval($obj['id'])  // ❌ BUG: Using proposal ID instead of menu ID
    , [/* menu query structure */]
);
```

**Correct implementation should be:**
```php
$menu = $objs->getById(
    'inventory_menu'
    , intval($proposal['menu']['id'])  // ✅ Use menu ID from proposal
    , [/* menu query structure */]
);
```

### Data Flow Corruption

1. **Trigger**: Invoice pricing calculation runs via `get-menu-line-item-pricing` notification
2. **Failure Point**: `setMenuPricingRecord.php` attempts to fetch menu using proposal ID
3. **Fallback Behavior**: System creates new blank menu when original menu not found
4. **Cascade Effect**: New pricing breakdown created pointing to blank menu
5. **Result**: Original menu with sections becomes orphaned

### Symptoms and Detection

**Primary Indicators:**
- Menu with empty `sections: []` array
- Recent `date_created` timestamp (when bug triggered)
- Associated pricing breakdown with `total: 0`
- Multiple pricing breakdowns for same project/space

**Data Signature:**
```javascript
// Broken menu characteristics
{
    "id": [recent_id],
    "sections": [],  // Empty!
    "date_created": "[recent_timestamp]",
    "related": [proposal_id]
}

// Associated broken pricing breakdown
{
    "id": [recent_id],
    "menu": [broken_menu_id],
    "total": 0,  // Zero total!
    "active": "No"
}
```

## Diagnostic Methodology

### Step 1: Identify Affected Records

```javascript
// Find pricing breakdowns with zero totals
databaseConnection.obj.getWhere('inventory_menu_pricing_breakdown', {
    total: 0,
    childObjs: 1
}, function(results) {
    console.log('Potentially affected pricing breakdowns:', results);
});
```

### Step 2: Locate Original Menu

```javascript
// For each affected project, find all related pricing breakdowns
databaseConnection.obj.getWhere('inventory_menu_pricing_breakdown', {
    space: [project_id],
    childObjs: 1
}, function(results) {
    // Look for breakdown with actual total > 0 and active: "Yes"
    const original = results.find(r => r.total > 0 && r.active === "Yes");
    const broken = results.find(r => r.total === 0);
    console.log('Original menu ID:', original?.menu);
    console.log('Broken menu ID:', broken?.menu);
});
```

### Step 3: Verify Original Menu Integrity

```javascript
// Confirm original menu has sections
databaseConnection.obj.getById('inventory_menu', [original_menu_id], function(result) {
    console.log('Original menu sections count:', result.sections?.length || 0);
}, 3);
```

## Resolution Protocol

### Immediate Fix Script

```javascript
/**
 * Invoice Menu Pricing Bug Fix
 * Repairs proposal-menu linkage and archives corrupted records
 */
function fixInvoiceMenuPricingBug(projectId) {
    console.log(`Starting fix for project ${projectId}`);
    
    // Step 1: Find all pricing breakdowns for this project
    databaseConnection.obj.getWhere('inventory_menu_pricing_breakdown', {
        space: projectId,
        childObjs: 1
    }, function(breakdowns) {
        
        // Step 2: Identify original vs broken records
        const original = breakdowns.find(b => b.total > 0 && b.active === "Yes");
        const broken = breakdowns.find(b => b.total === 0);
        
        if (!original || !broken) {
            console.error('Could not identify original and broken records');
            return;
        }
        
        console.log(`Original menu ID: ${original.menu}`);
        console.log(`Broken menu ID: ${broken.menu}`);
        
        // Step 3: Get proposal ID from project
        databaseConnection.obj.getById('groups', projectId, function(project) {
            const proposalId = project.proposal.id || project.proposal;
            
            // Step 4: Update proposal to point to original menu
            databaseConnection.obj.update('proposals', {
                id: proposalId,
                menu: original.menu
            }, function(result) {
                console.log('✅ Proposal updated to original menu');
                
                // Step 5: Archive broken menu
                databaseConnection.obj.update('inventory_menu', {
                    id: broken.menu,
                    active: 'Archived'
                }, function() {
                    console.log('✅ Broken menu archived');
                    
                    // Step 6: Archive broken pricing breakdown
                    databaseConnection.obj.update('inventory_menu_pricing_breakdown', {
                        id: broken.id,
                        active: 'Archived'
                    }, function() {
                        console.log('✅ Broken pricing breakdown archived');
                        console.log('🎉 Fix completed successfully!');
                    });
                });
            });
        }, 1);
    });
}

// Usage: fixInvoiceMenuPricingBug(19814601);
```

### Manual Fix Steps (Alternative)

If automated script fails, follow these manual steps:

1. **Identify Records:**
   ```javascript
   // Find project's pricing breakdowns
   databaseConnection.obj.getWhere('inventory_menu_pricing_breakdown', {
       space: [PROJECT_ID], childObjs: 1
   }, console.log);
   ```

2. **Update Proposal:**
   ```javascript
   databaseConnection.obj.update('proposals', {
       id: [PROPOSAL_ID],
       menu: [ORIGINAL_MENU_ID]
   }, console.log);
   ```

3. **Archive Broken Records:**
   ```javascript
   // Archive broken menu
   databaseConnection.obj.update('inventory_menu', {
       id: [BROKEN_MENU_ID], active: 'Archived'
   }, console.log);

   // Archive broken pricing breakdown
   databaseConnection.obj.update('inventory_menu_pricing_breakdown', {
       id: [BROKEN_BREAKDOWN_ID], active: 'Archived'
   }, console.log);
   ```

## Prevention Strategies

### Code Review Checklist

When reviewing pricing-related code, verify:
- [ ] Menu ID sourced from `proposal['menu']['id']`, not `obj['id']`
- [ ] Proper error handling when menu not found
- [ ] No automatic creation of blank menus without explicit user action
- [ ] Pricing calculations validate menu has sections before proceeding

### Monitoring Queries

**Weekly Health Check:**
```javascript
// Find menus with empty sections created in last 7 days
databaseConnection.obj.getWhere('inventory_menu', {
    date_created: { type: 'greater_than', value: new Date(Date.now() - 7*24*60*60*1000).toISOString() },
    childObjs: 1
}, function(results) {
    const emptySections = results.filter(m => !m.sections || m.sections.length === 0);
    if (emptySections.length > 0) {
        console.warn('⚠️ Found menus with empty sections:', emptySections);
    }
});
```

**Pricing Breakdown Integrity Check:**
```javascript
// Find pricing breakdowns with zero totals
databaseConnection.obj.getWhere('inventory_menu_pricing_breakdown', {
    total: 0, active: 'No', childObjs: 1
}, function(results) {
    if (results.length > 0) {
        console.warn('⚠️ Found zero-total pricing breakdowns:', results);
    }
});
```

## Case Study: Newton.Banning Wedding

**Affected Records:**
- Project ID: `19814601`
- Proposal ID: `19814595`
- Original Menu ID: `19814594` (with sections, total: $85,791.72)
- Broken Menu ID: `20663177` (empty sections, total: $0)
- Original Pricing Breakdown: `19815347` (active: "Yes")
- Broken Pricing Breakdown: `20663179` (active: "No")

**Timeline:**
- 2024-12-08: Original menu and pricing created
- 2025-09-25: Bug triggered, blank menu created
- 2025-09-29: Issue discovered and resolved

**Resolution Applied:**
```javascript
fixInvoiceMenuPricingBug(19814601);
```

## Related Documentation

- **Code Location**: `_SRC/pagoda/rules/actions/setMenuPricingRecord.php` (lines 795-798)
- **Notification Trigger**: `get-menu-line-item-pricing` in `_SRC/notify/_components/_bizdev/inventory.js`
- **Database Schema**: `_SRC/blueprints/proposals.json`, `_SRC/blueprints/inventory_menu.json`

## Appendix: Database Schema Relationships

```
groups (projects)
├── proposal (proposals.id)
    ├── menu (inventory_menu.id)
    │   └── sections[] (menu sections with line items)
    └── pricing_breakdown (inventory_menu_pricing_breakdown)
        ├── menu (points to inventory_menu.id)
        ├── total (calculated from menu sections)
        └── breakdown{} (detailed pricing data)
```

**Critical Constraint**: `proposals.menu` MUST point to `inventory_menu.id` with populated `sections[]` array for pricing calculations to work correctly.
