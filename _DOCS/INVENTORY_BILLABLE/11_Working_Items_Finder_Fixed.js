// =================================================================================================
// WORKING ITEMS FINDER - FIXED VERSION - Find ANY records with populated items arrays
// =================================================================================================

/**
 * Find records that actually have working items arrays (FIXED VERSION)
 */
function findWorkingItemsStructureFixed() {
    console.log('🔍 SEARCHING FOR ANY RECORDS WITH POPULATED ITEMS ARRAYS');
    console.log('This will help us understand the proper data structure');
    console.log('='.repeat(60));
    
    databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombinations) {
        
        console.log(`📊 ANALYZING ${allCombinations.length} total combinations...`);
        
        // Find records with populated items arrays
        const withItems = allCombinations.filter(rec => rec.items && rec.items.length > 0);
        const withoutItems = allCombinations.filter(rec => !rec.items || rec.items.length === 0);
        
        console.log(`\n📊 ITEMS ARRAY ANALYSIS:`);
        console.log(`   Records WITH items: ${withItems.length}`);
        console.log(`   Records WITHOUT items: ${withoutItems.length}`);
        console.log(`   Percentage with items: ${((withItems.length / allCombinations.length) * 100).toFixed(1)}%`);
        
        if (withItems.length > 0) {
            console.log(`\n✅ FOUND WORKING RECORDS! Analyzing structure...`);
            
            // Analyze the working records
            const duplicatesWithItems = withItems.filter(rec => rec.name.includes('(*)'));
            const originalsWithItems = withItems.filter(rec => !rec.name.includes('(*)'));
            
            console.log(`\n📋 WORKING RECORDS BREAKDOWN:`);
            console.log(`   Working duplicates (*): ${duplicatesWithItems.length}`);
            console.log(`   Working originals: ${originalsWithItems.length}`);
            
            // Show examples of working structure
            console.log(`\n📝 WORKING STRUCTURE EXAMPLES:`);
            
            if (duplicatesWithItems.length > 0) {
                console.log(`\n🔧 WORKING DUPLICATE EXAMPLES:`);
                duplicatesWithItems.slice(0, 3).forEach((rec, i) => {
                    console.log(`\n${i + 1}. ID ${rec.id}: "${rec.name}"`);
                    console.log(`   Items: ${rec.items.length}`);
                    console.log(`   Hidden: ${rec.is_hidden_from_menu_selections === 1 ? 'YES' : 'NO'}`);
                    console.log(`   Category: ${rec.category}`);
                    
                    // Show first item structure
                    if (rec.items[0]) {
                        console.log(`   First item structure:`);
                        console.log(`     inventory_group: ${rec.items[0].inventory_group || 'MISSING'}`);
                        console.log(`     qty: ${JSON.stringify(rec.items[0].qty || {})}`);
                        console.log(`     choices: ${rec.items[0].choices?.length || 0}`);
                    }
                });
                
                console.log(`\n🎯 PERFECT! We have working duplicates to use as templates!`);
                console.log(`🚀 NEXT: analyzeWorkingDuplicate(${duplicatesWithItems[0].id})`);
            }
            
            if (originalsWithItems.length > 0) {
                console.log(`\n📋 WORKING ORIGINAL EXAMPLES:`);
                originalsWithItems.slice(0, 3).forEach((rec, i) => {
                    console.log(`\n${i + 1}. ID ${rec.id}: "${rec.name}"`);
                    console.log(`   Items: ${rec.items.length}`);
                    console.log(`   Hidden: ${rec.is_hidden_from_menu_selections === 1 ? 'YES' : 'NO'}`);
                    console.log(`   Category: ${rec.category}`);
                });
            }
            
            // Look for successful duplication pairs
            console.log(`\n🔍 LOOKING FOR SUCCESSFUL DUPLICATION PAIRS...`);
            let successfulPairs = [];
            
            duplicatesWithItems.slice(0, 10).forEach(duplicate => {
                const baseName = duplicate.name.replace(' (*)', '');
                const original = allCombinations.find(orig => orig.name === baseName);
                
                if (original) {
                    successfulPairs.push({
                        duplicateId: duplicate.id,
                        duplicateName: duplicate.name,
                        duplicateItems: duplicate.items.length,
                        originalId: original.id,
                        originalName: original.name,
                        originalItems: original.items?.length || 0,
                        bothWorking: original.items && original.items.length > 0
                    });
                }
            });
            
            if (successfulPairs.length > 0) {
                console.log(`\n🎉 FOUND ${successfulPairs.length} SUCCESSFUL DUPLICATION PAIRS:`);
                successfulPairs.forEach((pair, i) => {
                    console.log(`\n${i + 1}. ${pair.bothWorking ? '✅' : '⚠️'} PAIR:`);
                    console.log(`   Duplicate: ID ${pair.duplicateId} - "${pair.duplicateName}" (${pair.duplicateItems} items)`);
                    console.log(`   Original: ID ${pair.originalId} - "${pair.originalName}" (${pair.originalItems} items)`);
                });
                
                if (successfulPairs.some(p => p.bothWorking)) {
                    console.log(`\n💡 INSIGHT: Some duplications DID work correctly!`);
                    console.log(`🚀 NEXT: analyzeSuccessfulPair() to understand working pattern`);
                }
            }
            
            // Check for patterns in working vs broken - FIXED DATE HANDLING
            console.log(`\n📊 PATTERN ANALYSIS:`);
            
            // Check categories
            const workingCategories = [...new Set(withItems.map(rec => rec.category))];
            const brokenCategories = [...new Set(withoutItems.map(rec => rec.category))];
            
            console.log(`\n📁 CATEGORY PATTERNS:`);
            console.log(`   Categories with working items: ${workingCategories.length}`);
            console.log(`   Categories with broken items: ${brokenCategories.length}`);
            
            // Check dates - FIXED
            console.log(`\n📅 DATE PATTERNS:`);
            if (withItems.length > 0) {
                const workingDates = withItems.map(rec => new Date(rec.date_created)).filter(d => !isNaN(d));
                if (workingDates.length > 0) {
                    const minWorkingDate = new Date(Math.min(...workingDates));
                    const maxWorkingDate = new Date(Math.max(...workingDates));
                    console.log(`   Working items date range: ${minWorkingDate.toDateString()} to ${maxWorkingDate.toDateString()}`);
                }
            }
            
            if (withoutItems.length > 0) {
                const brokenDates = withoutItems.map(rec => new Date(rec.date_created)).filter(d => !isNaN(d));
                if (brokenDates.length > 0) {
                    const minBrokenDate = new Date(Math.min(...brokenDates));
                    const maxBrokenDate = new Date(Math.max(...brokenDates));
                    console.log(`   Broken items date range: ${minBrokenDate.toDateString()} to ${maxBrokenDate.toDateString()}`);
                }
            }
            
        } else {
            console.log(`\n❌ CRITICAL: NO RECORDS HAVE POPULATED ITEMS ARRAYS!`);
            console.log(`\nThis suggests either:`);
            console.log(`   1. System-wide data corruption`);
            console.log(`   2. Items stored in different location`);
            console.log(`   3. Database structure changed`);
            console.log(`\n🔍 NEXT: investigateDataStructure()`);
        }
    });
}

/**
 * Analyze a specific working duplicate
 */
function analyzeWorkingDuplicate(workingId) {
    console.log(`🔬 ANALYZING WORKING DUPLICATE: ${workingId}`);
    console.log('='.repeat(50));
    
    databaseConnection.obj.getById('inventory_billable_combinations', workingId, function(working) {
        if (!working) {
            console.log(`❌ Record ${workingId} not found`);
            return;
        }
        
        console.log(`📋 WORKING RECORD ANALYSIS:`);
        console.log(`   ID: ${working.id}`);
        console.log(`   Name: "${working.name}"`);
        console.log(`   Items: ${working.items?.length || 0}`);
        console.log(`   Category: ${working.category}`);
        console.log(`   Hidden: ${working.is_hidden_from_menu_selections === 1 ? 'YES' : 'NO'}`);
        console.log(`   Data source ID: ${working.data_source_id || 'None'}`);
        
        if (working.items && working.items.length > 0) {
            console.log(`\n📝 COMPLETE ITEMS STRUCTURE:`);
            working.items.forEach((item, i) => {
                console.log(`\n   Item ${i + 1}:`);
                console.log(`     ID: ${item.id || 'None'}`);
                console.log(`     inventory_group: ${item.inventory_group || 'MISSING'}`);
                console.log(`     name: ${item.name || 'None'}`);
                console.log(`     qty: ${JSON.stringify(item.qty || {})}`);
                console.log(`     choices: ${item.choices?.length || 0} choice options`);
                console.log(`     multiplier: ${item.multiplier || 'None'}`);
                console.log(`     divisor: ${item.divisor || 'None'}`);
                
                if (item.choices && item.choices.length > 0) {
                    console.log(`     Choice details:`);
                    item.choices.slice(0, 2).forEach((choice, j) => {
                        console.log(`       Choice ${j + 1}: inventory_group ${choice.inventory_group}, price ${choice.additional_price || 0}`);
                    });
                }
            });
            
            console.log(`\n🎯 THIS IS THE STRUCTURE WE NEED TO RECREATE!`);
            console.log(`\n🔧 REPAIR STRATEGY:`);
            console.log(`   1. Use this working structure as template`);
            console.log(`   2. Copy items array to broken duplicates`);
            console.log(`   3. Deep duplicate referenced inventory groups`);
            console.log(`   4. Update group references in copied items`);
            
            console.log(`\n🚀 NEXT: createRepairTemplate(${working.id})`);
        }
    });
}

/**
 * Create a repair template from working record
 */
function createRepairTemplate(workingId) {
    console.log(`📋 CREATING REPAIR TEMPLATE FROM WORKING RECORD ${workingId}`);
    
    databaseConnection.obj.getById('inventory_billable_combinations', workingId, function(working) {
        if (!working || !working.items || working.items.length === 0) {
            console.log(`❌ Record ${workingId} is not suitable for template`);
            return;
        }
        
        const template = {
            sourceId: working.id,
            sourceName: working.name,
            itemsStructure: working.items,
            groupsToCheck: [],
            repairInstructions: []
        };
        
        // Extract inventory groups that need to be checked/duplicated
        working.items.forEach(item => {
            if (item.inventory_group) {
                template.groupsToCheck.push(item.inventory_group);
            }
            if (item.choices) {
                item.choices.forEach(choice => {
                    if (choice.inventory_group) {
                        template.groupsToCheck.push(choice.inventory_group);
                    }
                });
            }
        });
        
        template.groupsToCheck = [...new Set(template.groupsToCheck)]; // Remove duplicates
        
        console.log(`\n📋 REPAIR TEMPLATE CREATED:`);
        console.log(`   Source: ID ${template.sourceId} - "${template.sourceName}"`);
        console.log(`   Items count: ${template.itemsStructure.length}`);
        console.log(`   Groups to check/duplicate: ${template.groupsToCheck.length}`);
        console.log(`   Groups: ${template.groupsToCheck.join(', ')}`);
        
        console.log(`\n🎯 THIS TEMPLATE CAN REPAIR BROKEN RECORDS!`);
        console.log(`\n🚀 Ready to develop full repair script using this structure`);
        
        // Store globally for reference
        window.repairTemplate = template;
        console.log(`\n💾 Template stored in: window.repairTemplate`);
    });
}

console.log('✅ WORKING ITEMS FINDER FIXED VERSION LOADED');
console.log('📋 Available commands:');
console.log('   findWorkingItemsStructureFixed() - Find records with populated items (FIXED)');
console.log('   analyzeWorkingDuplicate(id) - Analyze specific working record');
console.log('   createRepairTemplate(id) - Create repair template from working record');
console.log('');
console.log('🚀 START HERE: findWorkingItemsStructureFixed()');
