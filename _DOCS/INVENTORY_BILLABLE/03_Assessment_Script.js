// =================================================================================================
// INVENTORY BILLABLE DAMAGE ASSESSMENT SCRIPT
// =================================================================================================
// This script provides a comprehensive analysis of the current state of duplicated inventory records
// Run this in the browser console to assess the scope of the duplication issues

/**
 * Comprehensive assessment of inventory duplication damage
 */
async function runFullInventoryAssessment() {
    console.log('🔍 STARTING COMPREHENSIVE INVENTORY ASSESSMENT');
    console.log('='.repeat(80));
    
    const assessment = {
        combinations: { total: 0, duplicates: 0, broken: 0, orphaned: 0 },
        groups: { total: 0, duplicates: 0, referenced: 0, orphaned: 0 },
        categories: { total: 0, duplicates: 0 },
        brokenRecords: [],
        repairCandidates: [],
        summary: {}
    };

    // PHASE 1: Assess Combinations
    console.log('\n📋 PHASE 1: ANALYZING INVENTORY_BILLABLE_COMBINATIONS');
    await new Promise(resolve => {
        databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombinations) {
            assessment.combinations.total = allCombinations.length;
            
            const duplicates = allCombinations.filter(rec => rec.name.includes('(*)'));
            assessment.combinations.duplicates = duplicates.length;
            
            const broken = duplicates.filter(rec => !rec.items || rec.items.length === 0);
            assessment.combinations.broken = broken.length;
            
            const orphaned = duplicates.filter(rec => !rec.data_source_id);
            assessment.combinations.orphaned = orphaned.length;
            
            console.log(`   Total combinations: ${assessment.combinations.total}`);
            console.log(`   Duplicated (*): ${assessment.combinations.duplicates}`);
            console.log(`   With broken items: ${assessment.combinations.broken}`);
            console.log(`   Missing data_source_id: ${assessment.combinations.orphaned}`);
            
            // Analyze broken records in detail
            console.log('\n   🔧 BROKEN RECORD ANALYSIS:');
            broken.forEach(rec => {
                const originalName = rec.name.replace(' (*)', '');
                const original = allCombinations.find(orig => orig.name === originalName);
                
                const brokenRecord = {
                    id: rec.id,
                    name: rec.name,
                    originalFound: !!original,
                    originalId: original?.id,
                    originalItemsCount: original?.items?.length || 0,
                    hasDataSource: !!rec.data_source_id
                };
                
                assessment.brokenRecords.push(brokenRecord);
                
                console.log(`     - ID ${rec.id}: "${rec.name}"`);
                console.log(`       Original found: ${brokenRecord.originalFound ? '✅' : '❌'}`);
                if (original) {
                    console.log(`       Original ID: ${original.id} (${original.items?.length || 0} items)`);
                }
                console.log(`       Has data_source_id: ${brokenRecord.hasDataSource ? '✅' : '❌'}`);
            });
            
            resolve();
        });
    });

    // PHASE 2: Assess Groups
    console.log('\n🔗 PHASE 2: ANALYZING INVENTORY_BILLABLE_GROUPS');
    await new Promise(resolve => {
        databaseConnection.obj.getWhere('inventory_billable_groups', {}, function(allGroups) {
            assessment.groups.total = allGroups.length;
            
            const duplicates = allGroups.filter(rec => rec.name.includes('(*)'));
            assessment.groups.duplicates = duplicates.length;
            
            const withDataSource = allGroups.filter(rec => rec.data_source_id);
            const orphaned = duplicates.filter(rec => !rec.data_source_id);
            assessment.groups.orphaned = orphaned.length;
            
            console.log(`   Total groups: ${assessment.groups.total}`);
            console.log(`   Duplicated (*): ${assessment.groups.duplicates}`);
            console.log(`   With data_source_id: ${withDataSource.length}`);
            console.log(`   Orphaned duplicates: ${assessment.groups.orphaned}`);
            
            // Build reference map
            const referencedGroups = new Set();
            databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(combinations) {
                combinations.forEach(combo => {
                    if (combo.items) {
                        combo.items.forEach(item => {
                            if (item.inventory_group) {
                                referencedGroups.add(item.inventory_group);
                            }
                            if (item.choices) {
                                item.choices.forEach(choice => {
                                    if (choice.inventory_group) {
                                        referencedGroups.add(choice.inventory_group);
                                    }
                                });
                            }
                        });
                    }
                });
                
                assessment.groups.referenced = referencedGroups.size;
                console.log(`   Referenced by combinations: ${assessment.groups.referenced}`);
                
                resolve();
            });
        });
    });

    // PHASE 3: Assess Categories
    console.log('\n📁 PHASE 3: ANALYZING COMBINATION CATEGORIES');
    await new Promise(resolve => {
        databaseConnection.obj.getWhere('inventory_billable_combination_categories', {}, function(allCategories) {
            assessment.categories.total = allCategories.length;
            
            const duplicates = allCategories.filter(rec => rec.name.includes('(*)'));
            assessment.categories.duplicates = duplicates.length;
            
            console.log(`   Total categories: ${assessment.categories.total}`);
            console.log(`   Duplicated (*): ${assessment.categories.duplicates}`);
            
            resolve();
        });
    });

    // PHASE 4: Build Repair Strategy
    console.log('\n🔨 PHASE 4: BUILDING REPAIR STRATEGY');
    
    // Find specific test case
    const testCase = assessment.brokenRecords.find(rec => rec.name.includes('Vendor Meals'));
    if (testCase) {
        console.log(`\n   🎯 TEST CASE IDENTIFIED:`);
        console.log(`     ID: ${testCase.id}`);
        console.log(`     Name: "${testCase.name}"`);
        console.log(`     Original ID: ${testCase.originalId}`);
        console.log(`     Repair Strategy: ${testCase.originalFound ? 'Deep copy from original' : 'Manual reconstruction'}`);
    }

    // Generate repair candidates
    assessment.brokenRecords.forEach(broken => {
        if (broken.originalFound && broken.originalItemsCount > 0) {
            assessment.repairCandidates.push({
                brokenId: broken.id,
                brokenName: broken.name,
                originalId: broken.originalId,
                strategy: 'deep_copy_and_repair',
                priority: broken.name.includes('Event Experience') ? 'HIGH' : 'MEDIUM'
            });
        }
    });

    // FINAL SUMMARY
    console.log('\n📊 ASSESSMENT SUMMARY');
    console.log('='.repeat(80));
    console.log(`🔴 CRITICAL ISSUES:`);
    console.log(`   Broken combinations: ${assessment.combinations.broken}`);
    console.log(`   Orphaned groups: ${assessment.groups.orphaned}`);
    console.log(`   Total repair candidates: ${assessment.repairCandidates.length}`);
    
    console.log(`\n📈 OVERALL STATS:`);
    console.log(`   Total objects analyzed: ${assessment.combinations.total + assessment.groups.total + assessment.categories.total}`);
    console.log(`   Total duplicated objects: ${assessment.combinations.duplicates + assessment.groups.duplicates + assessment.categories.duplicates}`);
    console.log(`   Duplication success rate: ${Math.round((1 - assessment.combinations.broken / assessment.combinations.duplicates) * 100)}%`);
    
    console.log(`\n🎯 HIGH PRIORITY REPAIRS:`);
    const highPriority = assessment.repairCandidates.filter(candidate => candidate.priority === 'HIGH');
    highPriority.forEach(candidate => {
        console.log(`   - ${candidate.brokenName} (ID: ${candidate.brokenId})`);
    });
    
    console.log(`\n✅ NEXT STEPS:`);
    console.log(`   1. Run targeted repair on high priority items`);
    console.log(`   2. Implement deep duplication process for remaining items`);
    console.log(`   3. Test BEO rendering after repairs`);
    console.log(`   4. Document process for future duplications`);
    
    // Store results globally for reference
    window.inventoryAssessment = assessment;
    console.log(`\n💾 Results stored in: window.inventoryAssessment`);
    
    return assessment;
}

/**
 * Quick analysis of a specific broken record
 */
function analyzeSpecificRecord(recordId) {
    console.log(`🔍 ANALYZING SPECIFIC RECORD: ${recordId}`);
    
    databaseConnection.obj.getById('inventory_billable_combinations', recordId, function(record) {
        if (!record) {
            console.log(`❌ Record ${recordId} not found`);
            return;
        }
        
        console.log(`📋 RECORD DETAILS:`);
        console.log(`   ID: ${record.id}`);
        console.log(`   Name: "${record.name}"`);
        console.log(`   Items count: ${record.items?.length || 0}`);
        console.log(`   Data source ID: ${record.data_source_id || 'MISSING'}`);
        console.log(`   Category: ${record.category}`);
        
        if (record.items && record.items.length > 0) {
            console.log(`\n🔗 ITEMS ANALYSIS:`);
            record.items.forEach((item, index) => {
                console.log(`   Item ${index + 1}:`);
                console.log(`     inventory_group: ${item.inventory_group || 'MISSING'}`);
                console.log(`     choices count: ${item.choices?.length || 0}`);
            });
        } else {
            console.log(`\n❌ NO ITEMS FOUND - This is the core issue`);
        }
        
        // Try to find original
        const originalName = record.name.replace(' (*)', '');
        databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(all) {
            const original = all.find(rec => rec.name === originalName);
            if (original) {
                console.log(`\n🎯 ORIGINAL FOUND:`);
                console.log(`   Original ID: ${original.id}`);
                console.log(`   Original items: ${original.items?.length || 0}`);
                console.log(`   REPAIR STRATEGY: Copy items from original and duplicate referenced groups`);
            } else {
                console.log(`\n❌ ORIGINAL NOT FOUND`);
                console.log(`   REPAIR STRATEGY: Manual reconstruction required`);
            }
        });
    });
}

/**
 * Test the specific broken record from the problem description
 */
function analyzeVendorMealsRecord() {
    analyzeSpecificRecord(20591444);
}

// =================================================================================================
// EXECUTION COMMANDS
// =================================================================================================

console.log('🚀 INVENTORY ASSESSMENT TOOLS LOADED');
console.log('Available commands:');
console.log('  runFullInventoryAssessment() - Complete system analysis');
console.log('  analyzeSpecificRecord(id) - Analyze specific record');
console.log('  analyzeVendorMealsRecord() - Analyze the test case (ID: 20591444)');
