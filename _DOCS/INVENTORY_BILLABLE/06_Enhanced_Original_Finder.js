// =================================================================================================
// ENHANCED ORIGINAL FINDER - Better search logic for finding source records
// =================================================================================================

/**
 * Enhanced function to find original records with better search logic
 */
function findOriginalForBrokenRecord(brokenId) {
    console.log(`🔍 ENHANCED SEARCH for original of ID: ${brokenId}`);
    
    databaseConnection.obj.getById('inventory_billable_combinations', brokenId, function(brokenRecord) {
        if (!brokenRecord) {
            console.log(`❌ Broken record ${brokenId} not found`);
            return;
        }
        
        console.log(`📋 ANALYZING: "${brokenRecord.name}"`);
        
        // Extract the base name (remove ' (*)')
        const baseName = brokenRecord.name.replace(' (*)', '');
        console.log(`🔍 Looking for original with base name: "${baseName}"`);
        
        // Get ALL combinations to search through
        databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombinations) {
            console.log(`📊 Searching through ${allCombinations.length} total combinations...`);
            
            // Method 1: Exact match (most likely)
            const exactMatch = allCombinations.find(rec => rec.name === baseName);
            
            // Method 2: Contains search (in case of slight variations)
            const containsMatches = allCombinations.filter(rec => 
                rec.name.includes(baseName) && !rec.name.includes('(*)')
            );
            
            // Method 3: Partial name search (first few words)
            const nameWords = baseName.split(' ').slice(0, 4).join(' '); // First 4 words
            const partialMatches = allCombinations.filter(rec => 
                rec.name.includes(nameWords) && !rec.name.includes('(*)')
            );
            
            console.log(`\n🎯 SEARCH RESULTS:`);
            
            if (exactMatch) {
                console.log(`✅ EXACT MATCH FOUND:`);
                console.log(`   ID: ${exactMatch.id}`);
                console.log(`   Name: "${exactMatch.name}"`);
                console.log(`   Items: ${exactMatch.items?.length || 0}`);
                console.log(`   Data source: ${exactMatch.data_source_id || 'None'}`);
                
                if (exactMatch.items && exactMatch.items.length > 0) {
                    console.log(`\n📝 ORIGINAL STRUCTURE PREVIEW:`);
                    exactMatch.items.slice(0, 3).forEach((item, i) => {
                        console.log(`   Item ${i + 1}: inventory_group ${item.inventory_group}`);
                    });
                    if (exactMatch.items.length > 3) {
                        console.log(`   ... and ${exactMatch.items.length - 3} more items`);
                    }
                    
                    console.log(`\n🔧 REPAIR STRATEGY:`);
                    console.log(`   1. Copy items array from ID ${exactMatch.id}`);
                    console.log(`   2. Deep duplicate referenced inventory groups`);
                    console.log(`   3. Update broken record ${brokenId} with new references`);
                } else {
                    console.log(`⚠️  Original also has empty items array - may need deeper investigation`);
                }
            } else {
                console.log(`❌ No exact match found for "${baseName}"`);
            }
            
            if (containsMatches.length > 0) {
                console.log(`\n🔍 CONTAINS MATCHES (${containsMatches.length}):`);
                containsMatches.slice(0, 5).forEach(match => {
                    console.log(`   - ID ${match.id}: "${match.name}" (${match.items?.length || 0} items)`);
                });
            }
            
            if (partialMatches.length > 0) {
                console.log(`\n🔍 PARTIAL MATCHES (${partialMatches.length}):`);
                partialMatches.slice(0, 5).forEach(match => {
                    console.log(`   - ID ${match.id}: "${match.name}" (${match.items?.length || 0} items)`);
                });
            }
            
            if (!exactMatch && containsMatches.length === 0 && partialMatches.length === 0) {
                console.log(`\n❌ NO MATCHES FOUND - This may indicate:`);
                console.log(`   1. Original was deleted or renamed`);
                console.log(`   2. Name pattern is different than expected`);
                console.log(`   3. Manual search needed`);
                console.log(`\n🔍 SUGGESTION: Run manualNameSearch("${nameWords}") for broader search`);
            }
        });
    });
}

/**
 * Manual search function for broader name matching
 */
function manualNameSearch(searchTerm) {
    console.log(`🔍 MANUAL SEARCH for: "${searchTerm}"`);
    
    databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombinations) {
        const matches = allCombinations.filter(rec => {
            const name = rec.name.toLowerCase();
            const search = searchTerm.toLowerCase();
            return name.includes(search) && !name.includes('(*)');
        });
        
        console.log(`📊 Found ${matches.length} potential matches:`);
        matches.forEach(match => {
            console.log(`   - ID ${match.id}: "${match.name}" (${match.items?.length || 0} items)`);
        });
        
        if (matches.length > 0) {
            console.log(`\n💡 To analyze a specific match, run: analyzeOriginalCandidate(ID)`);
        }
    });
}

/**
 * Analyze a potential original candidate
 */
function analyzeOriginalCandidate(candidateId) {
    console.log(`🔬 ANALYZING CANDIDATE: ${candidateId}`);
    
    databaseConnection.obj.getById('inventory_billable_combinations', candidateId, function(candidate) {
        if (!candidate) {
            console.log(`❌ Candidate ${candidateId} not found`);
            return;
        }
        
        console.log(`📋 CANDIDATE ANALYSIS:`);
        console.log(`   ID: ${candidate.id}`);
        console.log(`   Name: "${candidate.name}"`);
        console.log(`   Items array length: ${candidate.items?.length || 0}`);
        console.log(`   Category: ${candidate.category}`);
        console.log(`   Created: ${candidate.date_created}`);
        
        if (candidate.items && candidate.items.length > 0) {
            console.log(`\n📝 ITEMS STRUCTURE:`);
            candidate.items.forEach((item, i) => {
                console.log(`   Item ${i + 1}:`);
                console.log(`     inventory_group: ${item.inventory_group}`);
                console.log(`     qty: ${JSON.stringify(item.qty || {})}`);
                console.log(`     choices: ${item.choices?.length || 0} choice options`);
            });
            
            console.log(`\n✅ VIABLE ORIGINAL - Has complete structure for duplication`);
        } else {
            console.log(`\n❌ NOT VIABLE - Also has empty items array`);
        }
    });
}

// Quick test functions
function testVendorMealsEnhanced() {
    console.log('🧪 ENHANCED VENDOR MEALS TEST');
    findOriginalForBrokenRecord(20591444);
}

function searchForVendorMeals() {
    console.log('🔍 MANUAL VENDOR MEALS SEARCH');
    manualNameSearch('Included in Event Experience');
}

// Load enhanced search tools
console.log('✅ ENHANCED SEARCH TOOLS LOADED');
console.log('📋 Available commands:');
console.log('   testVendorMealsEnhanced() - Better search for vendor meals original');
console.log('   findOriginalForBrokenRecord(id) - Enhanced search for any broken record');
console.log('   manualNameSearch("term") - Broad text search');
console.log('   analyzeOriginalCandidate(id) - Analyze potential original');
console.log('   searchForVendorMeals() - Specific vendor meals search');
console.log('');
console.log('🚀 START HERE: testVendorMealsEnhanced()');
