# Bento databaseConnection.obj API Documentation

## Overview
The `databaseConnection.obj` is Bento's browser-based database wrapper that provides CRUD operations for the object storage system. It operates differently from the server-side `sb.data.db.obj` API.

## Key Differences from sb.data.db.obj

| Operation | sb.data.db.obj | databaseConnection.obj |
|-----------|----------------|------------------------|
| Create | `sb.data.db.obj.create(type, data, callback)` | `databaseConnection.obj.create(type, data, callback)` |
| Update | `sb.data.db.obj.update(type, id, data, callback)` | `databaseConnection.obj.update(type, dataWithId, callback)` |
| Get By ID | `sb.data.db.obj.getById(type, id, callback, fields)` | `databaseConnection.obj.getById(type, id, callback)` |
| Get Where | `sb.data.db.obj.getWhere(type, query, callback, fields)` | `databaseConnection.obj.getWhere(type, query, callback)` |

## Critical Differences

### Update Operation
- **sb.data.db.obj**: Requires 3 arguments: `(type, id, data, callback)`
- **databaseConnection.obj**: Requires 2 arguments: `(type, dataWithId, callback)`

The key difference is that `databaseConnection.obj.update()` expects the data object to already contain the `id` field, while `sb.data.db.obj.update()` passes the ID as a separate parameter.

### Field Selection
- **sb.data.db.obj**: Supports field selection parameter for optimized queries
- **databaseConnection.obj**: Does not support field selection - always returns full objects

## API Reference

### databaseConnection.obj.create(type, data, callback)
Creates a new object in the database.

```javascript
const newRecord = {
    name: "Test Item",
    category: 12345,
    items: []
};

databaseConnection.obj.create('inventory_billable_combinations', newRecord, function(created) {
    console.log('Created record with ID:', created.id);
});
```

### databaseConnection.obj.update(type, dataWithId, callback)
Updates an existing object. **Important**: Data must include the `id` field.

```javascript
// ✅ CORRECT - data includes id
const updatedRecord = {
    id: 20591444,
    name: "Updated Name (*)",
    items: [/* updated items */]
};

databaseConnection.obj.update('inventory_billable_combinations', updatedRecord, function(result) {
    console.log('Updated record:', result.id);
});

// ❌ INCORRECT - missing id will cause error
const badUpdate = {
    name: "Updated Name (*)",
    items: []
};
databaseConnection.obj.update('inventory_billable_combinations', badUpdate, callback); // ERROR
```

### databaseConnection.obj.getById(type, id, callback)
Retrieves a single object by ID.

```javascript
databaseConnection.obj.getById('inventory_billable_combinations', 20591444, function(record) {
    if (record) {
        console.log('Found record:', record.name);
    } else {
        console.log('Record not found');
    }
});
```

### databaseConnection.obj.getWhere(type, queryObject, callback)
Retrieves objects matching query conditions.

```javascript
// Find all duplicated records
databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allRecords) {
    const duplicates = allRecords.filter(rec => rec.name.includes('(*)'));
    console.log(`Found ${duplicates.length} duplicated records`);
});

// Find records by specific field
databaseConnection.obj.getWhere('inventory_billable_groups', {
    data_source_id: 18240191
}, function(duplicatedGroups) {
    console.log('Found duplicated groups:', duplicatedGroups.length);
});
```

### databaseConnection.obj.erase(id, callback)
Soft deletes an object by ID (sets is_deleted = 1).

```javascript
databaseConnection.obj.erase(20591444, function(result) {
    console.log('Record deleted:', result);
});
```

## Usage Patterns

### Safe Update Pattern
Always clone the record before modifying to avoid reference issues:

```javascript
databaseConnection.obj.getById('inventory_billable_combinations', recordId, function(original) {
    const updated = _.clone(original);  // Use lodash clone
    updated.name = "New Name (*)";
    updated.items = [/* new items */];
    
    databaseConnection.obj.update('inventory_billable_combinations', updated, function(result) {
        console.log('Successfully updated:', result.id);
    });
});
```

### Batch Operations with Delays
To avoid overwhelming the server, add delays between operations:

```javascript
async function processRecords(records) {
    for (let i = 0; i < records.length; i++) {
        const record = records[i];
        
        // Process record
        await new Promise(resolve => {
            databaseConnection.obj.update('inventory_billable_combinations', record, function(result) {
                console.log(`Processed ${i + 1}/${records.length}: ${result.id}`);
                resolve();
            });
        });
        
        // Add delay between operations
        await new Promise(delay => setTimeout(delay, 100));
    }
}
```

### Finding Related Records
Common pattern for finding related inventory groups:

```javascript
function findRelatedGroups(combinationId, callback) {
    databaseConnection.obj.getById('inventory_billable_combinations', combinationId, function(combination) {
        if (!combination.items) {
            callback([]);
            return;
        }
        
        const groupIds = combination.items.map(item => item.inventory_group).filter(Boolean);
        const groups = [];
        let processed = 0;
        
        groupIds.forEach(groupId => {
            databaseConnection.obj.getById('inventory_billable_groups', groupId, function(group) {
                if (group) groups.push(group);
                processed++;
                if (processed === groupIds.length) {
                    callback(groups);
                }
            });
        });
    });
}
```

## Error Handling

### Common Errors
1. **Missing ID in update**: Ensure data object contains `id` field
2. **Callback not fired**: Always check if record exists before processing
3. **Rate limiting**: Add delays between consecutive operations
4. **Circular references**: Use `_.clone()` to avoid reference issues

### Debug Pattern
```javascript
function debugOperation(type, operation, data, callback) {
    console.log(`🔍 ${operation.toUpperCase()} ${type}:`, data);
    
    databaseConnection.obj[operation](type, data, function(result) {
        if (result) {
            console.log(`✅ ${operation} successful:`, result.id || result.length);
        } else {
            console.error(`❌ ${operation} failed`);
        }
        callback(result);
    });
}
```

## Best Practices

1. **Always clone records** before modifying them
2. **Include delays** in batch operations (100-200ms)
3. **Check for existence** before assuming records exist
4. **Use meaningful variable names** for debugging
5. **Log operations** for troubleshooting
6. **Handle empty arrays** and null values gracefully
