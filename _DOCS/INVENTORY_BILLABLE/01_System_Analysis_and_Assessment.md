# Inventory Billable System Analysis & Assessment

## Overview
This document provides a comprehensive analysis of the Bento inventory system's data structure, the current issues with duplication, and a systematic approach to resolution.

## System Architecture Analysis

### Database Structure
Based on blueprint analysis, the inventory system uses a three-tier hierarchy:

1. **inventory_billable_combination_categories** (Top Level)
   - Container categories for organizing combinations
   - Example: "Food - 2025 Pricing (*)"

2. **inventory_billable_combinations** (Middle Level)  
   - Main menu items/packages
   - Contains `items` array with references to inventory_billable_groups
   - Example: "Included in Event Experience | Vendor Meals (*)"

3. **inventory_billable_groups** (Bottom Level)
   - Component building blocks and choice lists
   - Contains nested `items` array and `choices` array
   - Can reference other inventory_billable_groups via `inventory_group` field

### Critical Data Fields
- **items**: Array containing inventory_group references and configurations
- **choices**: Nested array within items containing additional inventory_group references
- **inventory_group**: Reference field pointing to inventory_billable_groups IDs
- **data_source_id**: Field intended to track original source during duplication
- **name**: Duplicated items append "(*)" to distinguish from originals

## Problem Analysis

### Root Cause: Shallow Copy Issue
The duplication process performs shallow copies rather than deep recursive copies, resulting in:

1. **Broken Reference Chain**: New combination records reference old inventory_billable_groups
2. **Empty Items Arrays**: Duplicated combinations lose their item configurations
3. **BEO Rendering Failures**: Empty/broken references cause BEO templates to render incorrectly

### Affected Data Structure
```json
{
  "id": 20591444,
  "name": "Included in Event Experience | Vendor Meals (*)",
  "items": [],  // ❌ Should contain item configurations
  "data_source": 0,  // ❌ Should reference original ID
  "data_source_id": 0  // ❌ Should reference original ID
}
```

### Expected Data Structure
```json
{
  "id": 20591444,
  "name": "Included in Event Experience | Vendor Meals (*)",
  "items": [
    {
      "id": 1,
      "inventory_group": 20435097,  // ✅ Should reference NEW duplicated group
      "qty": {"quantity": 1, "unit_type": "servings"},
      "choices": []
    }
  ],
  "data_source_id": 18240191  // ✅ Should reference original combination ID
}
```

## Assessment Requirements

To properly scope the repair work, we need to:

### 1. Identify All Affected Records
- Count total combinations with "(*)" in name
- Identify which have empty `items` arrays
- Map original → duplicate relationships

### 2. Assess Reference Integrity
- Check which inventory_billable_groups need duplication
- Verify nested choice reference chains
- Identify orphaned references

### 3. BEO System Impact
- Understand how BEO service reads inventory data
- Identify which categories should appear in Food vs Alcohol BEOs
- Map rendering failures to data issues

## Next Steps

1. **Run Database Assessment Script** (below)
2. **Create Reference Mapping**  
3. **Develop Repair Strategy**
4. **Document BEO Integration Requirements**

## Database Assessment Script

Run this in browser console to assess current state:

```javascript
// PHASE 1: ASSESSMENT - Run this to understand the scope
async function assessInventoryDamage() {
    console.log('=== INVENTORY DAMAGE ASSESSMENT ===');
    
    // Get all combinations with (*)
    databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombinations) {
        const duplicates = allCombinations.filter(rec => rec.name.includes('(*)'));
        const emptyItems = duplicates.filter(rec => !rec.items || rec.items.length === 0);
        const missingDataSource = duplicates.filter(rec => !rec.data_source_id);
        
        console.log(`📊 ASSESSMENT RESULTS:`);
        console.log(`   Total combinations: ${allCombinations.length}`);
        console.log(`   Duplicated combinations (*): ${duplicates.length}`);
        console.log(`   With empty items arrays: ${emptyItems.length}`);
        console.log(`   Missing data_source_id: ${missingDataSource.length}`);
        
        console.log(`\n🔍 BROKEN COMBINATIONS:`);
        emptyItems.forEach(rec => {
            console.log(`   - ID ${rec.id}: "${rec.name}"`);
        });
        
        // Find potential originals
        console.log(`\n🔗 FINDING ORIGINALS:`);
        duplicates.forEach(dup => {
            const originalName = dup.name.replace(' (*)', '');
            const original = allCombinations.find(rec => rec.name === originalName);
            if (original) {
                console.log(`   ✅ ${dup.id} (${dup.name}) -> ${original.id} (${original.name})`);
                console.log(`      Original has ${original.items?.length || 0} items`);
            } else {
                console.log(`   ❌ ${dup.id} (${dup.name}) -> NO ORIGINAL FOUND`);
            }
        });
    });
}

// Run assessment
assessInventoryDamage();
```
