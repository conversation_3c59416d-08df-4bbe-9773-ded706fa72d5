// =================================================================================================
// DEEP STRUCTURE ANALYZER - Fetch complete structure with inventory groups
// =================================================================================================

/**
 * Analyze working duplicate with full inventory group details
 */
function analyzeWorkingDuplicateDeep(workingId) {
    console.log(`🔬 DEEP ANALYSIS OF WORKING DUPLICATE: ${workingId}`);
    console.log('Fetching all referenced inventory groups...');
    console.log('='.repeat(60));
    
    databaseConnection.obj.getById('inventory_billable_combinations', workingId, function(working) {
        if (!working) {
            console.log(`❌ Record ${workingId} not found`);
            return;
        }
        
        console.log(`📋 WORKING COMBINATION:`);
        console.log(`   ID: ${working.id}`);
        console.log(`   Name: "${working.name}"`);
        console.log(`   Items: ${working.items?.length || 0}`);
        console.log(`   Category: ${working.category}`);
        console.log(`   Hidden: ${working.is_hidden_from_menu_selections === 1 ? 'YES' : 'NO'}`);
        
        if (!working.items || working.items.length === 0) {
            console.log(`❌ No items to analyze`);
            return;
        }
        
        // Collect all inventory group IDs to fetch
        let groupIds = [];
        working.items.forEach(item => {
            if (item.inventory_group) {
                groupIds.push(item.inventory_group);
            }
            if (item.choices) {
                item.choices.forEach(choice => {
                    if (choice.inventory_group) {
                        groupIds.push(choice.inventory_group);
                    }
                });
            }
        });
        
        groupIds = [...new Set(groupIds)]; // Remove duplicates
        console.log(`\n🔍 Fetching ${groupIds.length} inventory groups: ${groupIds.join(', ')}`);
        
        // Fetch all inventory groups
        let fetchedGroups = {};
        let fetchCount = 0;
        
        if (groupIds.length === 0) {
            console.log(`⚠️  No inventory groups to fetch`);
            displayCompleteStructure(working, {});
            return;
        }
        
        groupIds.forEach(groupId => {
            databaseConnection.obj.getById('inventory_billable_groups', groupId, function(group) {
                fetchedGroups[groupId] = group;
                fetchCount++;
                
                if (fetchCount === groupIds.length) {
                    // All groups fetched, display complete structure
                    displayCompleteStructure(working, fetchedGroups);
                }
            });
        });
    });
}

/**
 * Display complete structure with inventory group details
 */
function displayCompleteStructure(combination, groups) {
    console.log(`\n📝 COMPLETE STRUCTURE WITH INVENTORY GROUPS:`);
    console.log('='.repeat(60));
    
    combination.items.forEach((item, i) => {
        console.log(`\n🔧 ITEM ${i + 1}:`);
        console.log(`   Item ID: ${item.id || 'None'}`);
        console.log(`   Item name: "${item.name || 'None'}"`);
        console.log(`   Quantity: ${JSON.stringify(item.qty || {})}`);
        console.log(`   Multiplier: ${item.multiplier || 1}`);
        console.log(`   Divisor: ${item.divisor || 1}`);
        
        // Show inventory group details
        if (item.inventory_group) {
            const group = groups[item.inventory_group];
            if (group) {
                console.log(`\n   📦 INVENTORY GROUP ${item.inventory_group}:`);
                console.log(`      Group name: "${group.name}"`);
                console.log(`      Group category: ${group.category || 'None'}`);
                console.log(`      Group items: ${group.items?.length || 0}`);
                console.log(`      Group description: "${group.description || 'None'}"`);
                console.log(`      Group price: ${group.price || 0} cents`);
                console.log(`      Group price_per_person: ${group.price_per_person || 0} cents`);
                
                // Show nested items within the group
                if (group.items && group.items.length > 0) {
                    console.log(`\n      📋 GROUP'S NESTED ITEMS:`);
                    group.items.forEach((groupItem, j) => {
                        console.log(`         ${j + 1}. inventory_group: ${groupItem.inventory_group || 'None'}`);
                        console.log(`            name: "${groupItem.name || 'None'}"`);
                        console.log(`            qty: ${JSON.stringify(groupItem.qty || {})}`);
                        console.log(`            choices: ${groupItem.choices?.length || 0}`);
                    });
                }
            } else {
                console.log(`\n   ❌ INVENTORY GROUP ${item.inventory_group}: NOT FOUND`);
            }
        }
        
        // Show choices
        if (item.choices && item.choices.length > 0) {
            console.log(`\n   🎯 CHOICE OPTIONS (${item.choices.length}):`);
            item.choices.forEach((choice, j) => {
                console.log(`      Choice ${j + 1}:`);
                console.log(`         inventory_group: ${choice.inventory_group || 'None'}`);
                console.log(`         additional_price: ${choice.additional_price || 0} cents`);
                console.log(`         name: "${choice.name || 'None'}"`);
                
                if (choice.inventory_group && groups[choice.inventory_group]) {
                    const choiceGroup = groups[choice.inventory_group];
                    console.log(`         → Group name: "${choiceGroup.name}"`);
                    console.log(`         → Group items: ${choiceGroup.items?.length || 0}`);
                }
            });
        }
    });
    
    console.log(`\n🎯 ANALYSIS COMPLETE!`);
    console.log(`\n💡 KEY INSIGHTS:`);
    console.log(`   - This structure shows exactly what broken records need`);
    console.log(`   - inventory_group references point to billable_groups records`);
    console.log(`   - Groups can have nested items and choices`);
    console.log(`   - This is the template for repair!`);
    
    console.log(`\n🚀 NEXT STEPS:`);
    console.log(`   1. Use this structure as repair template`);
    console.log(`   2. For broken records: copy items array from working original`);
    console.log(`   3. Deep duplicate all referenced inventory groups`);
    console.log(`   4. Update inventory_group IDs in copied items array`);
    
    // Store structure globally for reference
    window.workingStructure = {
        combination: combination,
        groups: groups,
        analysis: 'complete'
    };
    console.log(`\n💾 Structure stored in: window.workingStructure`);
}

/**
 * Compare working vs broken record to see differences
 */
function compareWorkingVsBroken(workingId, brokenId) {
    console.log(`🔬 COMPARING WORKING VS BROKEN RECORDS`);
    console.log(`   Working ID: ${workingId}`);
    console.log(`   Broken ID: ${brokenId}`);
    console.log('='.repeat(50));
    
    databaseConnection.obj.getById('inventory_billable_combinations', workingId, function(working) {
        databaseConnection.obj.getById('inventory_billable_combinations', brokenId, function(broken) {
            
            console.log(`📊 COMPARISON RESULTS:`);
            console.log(`\nWORKING RECORD:`);
            console.log(`   ID: ${working.id}`);
            console.log(`   Name: "${working.name}"`);
            console.log(`   Items: ${working.items?.length || 0}`);
            console.log(`   Category: ${working.category}`);
            console.log(`   Data source: ${working.data_source_id || 'None'}`);
            
            console.log(`\nBROKEN RECORD:`);
            console.log(`   ID: ${broken.id}`);
            console.log(`   Name: "${broken.name}"`);
            console.log(`   Items: ${broken.items?.length || 0}`);
            console.log(`   Category: ${broken.category}`);
            console.log(`   Data source: ${broken.data_source_id || 'None'}`);
            
            console.log(`\n🔍 KEY DIFFERENCES:`);
            console.log(`   Items array: ${working.items?.length || 0} vs ${broken.items?.length || 0}`);
            console.log(`   Same category: ${working.category === broken.category ? 'YES' : 'NO'}`);
            
            if (working.items && working.items.length > 0) {
                console.log(`\n✅ WORKING ITEMS STRUCTURE CAN BE COPIED TO BROKEN RECORD`);
                console.log(`🔧 Repair process: Copy ${working.items.length} items from working to broken`);
            }
        });
    });
}

/**
 * Quick test with a known working record
 */
function quickDeepAnalysisTest() {
    console.log('🧪 QUICK DEEP ANALYSIS TEST');
    console.log('Using working duplicate: "Ultimate Bar Selection - Cellar\'s Reserve Wine Selection (*)"');
    analyzeWorkingDuplicateDeep(20456674);
}

console.log('✅ DEEP STRUCTURE ANALYZER LOADED');
console.log('📋 Available commands:');
console.log('   analyzeWorkingDuplicateDeep(id) - Deep analysis with inventory groups');
console.log('   compareWorkingVsBroken(workingId, brokenId) - Compare records');
console.log('   quickDeepAnalysisTest() - Test with known working record');
console.log('');
console.log('🚀 START HERE: quickDeepAnalysisTest()');
