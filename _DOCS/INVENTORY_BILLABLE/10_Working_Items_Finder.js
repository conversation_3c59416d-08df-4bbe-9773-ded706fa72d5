// =================================================================================================
// WORKING ITEMS FINDER - Find ANY records with populated items arrays
// =================================================================================================

/**
 * Find records that actually have working items arrays
 */
function findWorkingItemsStructure() {
    console.log('🔍 SEARCHING FOR ANY RECORDS WITH POPULATED ITEMS ARRAYS');
    console.log('This will help us understand the proper data structure');
    console.log('='.repeat(60));
    
    databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombinations) {
        
        console.log(`📊 ANALYZING ${allCombinations.length} total combinations...`);
        
        // Find records with populated items arrays
        const withItems = allCombinations.filter(rec => rec.items && rec.items.length > 0);
        const withoutItems = allCombinations.filter(rec => !rec.items || rec.items.length === 0);
        
        console.log(`\n📊 ITEMS ARRAY ANALYSIS:`);
        console.log(`   Records WITH items: ${withItems.length}`);
        console.log(`   Records WITHOUT items: ${withoutItems.length}`);
        console.log(`   Percentage with items: ${((withItems.length / allCombinations.length) * 100).toFixed(1)}%`);
        
        if (withItems.length > 0) {
            console.log(`\n✅ FOUND WORKING RECORDS! Analyzing structure...`);
            
            // Analyze the working records
            const duplicatesWithItems = withItems.filter(rec => rec.name.includes('(*)'));
            const originalsWithItems = withItems.filter(rec => !rec.name.includes('(*)'));
            
            console.log(`\n📋 WORKING RECORDS BREAKDOWN:`);
            console.log(`   Working duplicates (*): ${duplicatesWithItems.length}`);
            console.log(`   Working originals: ${originalsWithItems.length}`);
            
            // Show examples of working structure
            console.log(`\n📝 WORKING STRUCTURE EXAMPLES:`);
            
            if (duplicatesWithItems.length > 0) {
                console.log(`\n🔧 WORKING DUPLICATE EXAMPLES:`);
                duplicatesWithItems.slice(0, 3).forEach((rec, i) => {
                    console.log(`\n${i + 1}. ID ${rec.id}: "${rec.name}"`);
                    console.log(`   Items: ${rec.items.length}`);
                    console.log(`   Hidden: ${rec.is_hidden_from_menu_selections === 1 ? 'YES' : 'NO'}`);
                    console.log(`   Category: ${rec.category}`);
                    
                    // Show first item structure
                    if (rec.items[0]) {
                        console.log(`   First item structure:`);
                        console.log(`     inventory_group: ${rec.items[0].inventory_group || 'MISSING'}`);
                        console.log(`     qty: ${JSON.stringify(rec.items[0].qty || {})}`);
                        console.log(`     choices: ${rec.items[0].choices?.length || 0}`);
                    }
                });
            }
            
            if (originalsWithItems.length > 0) {
                console.log(`\n📋 WORKING ORIGINAL EXAMPLES:`);
                originalsWithItems.slice(0, 3).forEach((rec, i) => {
                    console.log(`\n${i + 1}. ID ${rec.id}: "${rec.name}"`);
                    console.log(`   Items: ${rec.items.length}`);
                    console.log(`   Hidden: ${rec.is_hidden_from_menu_selections === 1 ? 'YES' : 'NO'}`);
                    console.log(`   Category: ${rec.category}`);
                });
            }
            
            // Look for successful duplication pairs
            console.log(`\n🔍 LOOKING FOR SUCCESSFUL DUPLICATION PAIRS...`);
            let successfulPairs = [];
            
            duplicatesWithItems.slice(0, 10).forEach(duplicate => {
                const baseName = duplicate.name.replace(' (*)', '');
                const original = allCombinations.find(orig => orig.name === baseName);
                
                if (original) {
                    successfulPairs.push({
                        duplicateId: duplicate.id,
                        duplicateName: duplicate.name,
                        duplicateItems: duplicate.items.length,
                        originalId: original.id,
                        originalName: original.name,
                        originalItems: original.items?.length || 0,
                        bothWorking: original.items && original.items.length > 0
                    });
                }
            });
            
            if (successfulPairs.length > 0) {
                console.log(`\n🎉 FOUND ${successfulPairs.length} SUCCESSFUL DUPLICATION PAIRS:`);
                successfulPairs.forEach((pair, i) => {
                    console.log(`\n${i + 1}. ${pair.bothWorking ? '✅' : '⚠️'} PAIR:`);
                    console.log(`   Duplicate: ID ${pair.duplicateId} - "${pair.duplicateName}" (${pair.duplicateItems} items)`);
                    console.log(`   Original: ID ${pair.originalId} - "${pair.originalName}" (${pair.originalItems} items)`);
                });
                
                if (successfulPairs.some(p => p.bothWorking)) {
                    console.log(`\n💡 INSIGHT: Some duplications DID work correctly!`);
                    console.log(`🚀 NEXT: analyzeSuccessfulPair() to understand working pattern`);
                }
            }
            
            // Check for patterns in working vs broken
            console.log(`\n📊 PATTERN ANALYSIS:`);
            
            // Check categories
            const workingCategories = [...new Set(withItems.map(rec => rec.category))];
            const brokenCategories = [...new Set(withoutItems.map(rec => rec.category))];
            
            console.log(`\n📁 CATEGORY PATTERNS:`);
            console.log(`   Categories with working items: ${workingCategories.length}`);
            console.log(`   Categories with broken items: ${brokenCategories.length}`);
            
            // Check dates
            const workingDates = withItems.map(rec => new Date(rec.date_created));
            const brokenDates = withoutItems.map(rec => new Date(rec.date_created));
            
            console.log(`\n📅 DATE PATTERNS:`);
            console.log(`   Working items date range: ${Math.min(...workingDates).toDateString()} to ${Math.max(...workingDates).toDateString()}`);
            console.log(`   Broken items date range: ${Math.min(...brokenDates).toDateString()} to ${Math.max(...brokenDates).toDateString()}`);
            
        } else {
            console.log(`\n❌ CRITICAL: NO RECORDS HAVE POPULATED ITEMS ARRAYS!`);
            console.log(`\nThis suggests either:`);
            console.log(`   1. System-wide data corruption`);
            console.log(`   2. Items stored in different location`);
            console.log(`   3. Database structure changed`);
            console.log(`\n🔍 NEXT: investigateDataStructure()`);
        }
    });
}

/**
 * Analyze a successful duplication pair to understand working pattern
 */
function analyzeSuccessfulPair() {
    console.log('🔬 ANALYZING SUCCESSFUL DUPLICATION PAIRS');
    
    databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombinations) {
        const workingDuplicates = allCombinations.filter(rec => 
            rec.name.includes('(*)') && rec.items && rec.items.length > 0
        );
        
        if (workingDuplicates.length === 0) {
            console.log('❌ No working duplicates found');
            return;
        }
        
        const testDuplicate = workingDuplicates[0];
        const baseName = testDuplicate.name.replace(' (*)', '');
        const original = allCombinations.find(rec => rec.name === baseName);
        
        console.log(`\n🧪 ANALYZING WORKING PAIR:`);
        console.log(`Duplicate: ID ${testDuplicate.id} - "${testDuplicate.name}"`);
        console.log(`Original: ID ${original?.id || 'NOT FOUND'} - "${original?.name || 'NOT FOUND'}"`);
        
        if (original) {
            console.log(`\n📋 DETAILED COMPARISON:`);
            console.log(`\nDUPLICATE STRUCTURE:`);
            console.log(`   Items: ${testDuplicate.items.length}`);
            console.log(`   Data source ID: ${testDuplicate.data_source_id || 'None'}`);
            
            testDuplicate.items.forEach((item, i) => {
                console.log(`   Item ${i + 1}:`);
                console.log(`     inventory_group: ${item.inventory_group}`);
                console.log(`     qty: ${JSON.stringify(item.qty)}`);
                console.log(`     choices: ${item.choices?.length || 0}`);
            });
            
            console.log(`\nORIGINAL STRUCTURE:`);
            console.log(`   Items: ${original.items?.length || 0}`);
            console.log(`   Data source ID: ${original.data_source_id || 'None'}`);
            
            if (original.items) {
                original.items.forEach((item, i) => {
                    console.log(`   Item ${i + 1}:`);
                    console.log(`     inventory_group: ${item.inventory_group}`);
                    console.log(`     qty: ${JSON.stringify(item.qty)}`);
                    console.log(`     choices: ${item.choices?.length || 0}`);
                });
            }
            
            console.log(`\n💡 This working pair can serve as template for repairs!`);
        }
    });
}

/**
 * Investigate if data might be stored elsewhere
 */
function investigateDataStructure() {
    console.log('🔍 INVESTIGATING ALTERNATIVE DATA STORAGE');
    
    // Check if there's a different object type that might contain items
    console.log('\n📊 Checking for related object types...');
    console.log('This might take a moment...');
    
    // Look for inventory_menu_line_item connections
    databaseConnection.obj.getWhere('inventory_menu_line_item', {}, function(lineItems) {
        console.log(`\n📋 Found ${lineItems.length} inventory_menu_line_item records`);
        
        if (lineItems.length > 0) {
            const sampleLineItem = lineItems[0];
            console.log(`\n📝 Sample line item structure:`);
            console.log(JSON.stringify(sampleLineItem, null, 2));
            
            console.log(`\n💡 Items might be stored in menu line items instead of combinations`);
        }
    });
}

console.log('✅ WORKING ITEMS FINDER LOADED');
console.log('📋 Available commands:');
console.log('   findWorkingItemsStructure() - Find records with populated items');
console.log('   analyzeSuccessfulPair() - Study working duplication pairs');  
console.log('   investigateDataStructure() - Check alternative data storage');
console.log('');
console.log('🚀 START HERE: findWorkingItemsStructure()');
