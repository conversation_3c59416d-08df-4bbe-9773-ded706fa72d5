// =================================================================================================
// HIDDEN ORIGINALS FINDER - Search including hidden menu items
// =================================================================================================

/**
 * Search for originals including hidden menu items
 */
function findHiddenOriginals() {
    console.log('🔍 SEARCHING FOR HIDDEN ORIGINALS');
    console.log('Including records with is_hidden_from_menu_selections = 1');
    console.log('='.repeat(60));
    
    databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombinations) {
        
        console.log(`📊 DATASET OVERVIEW:`);
        console.log(`   Total combinations: ${allCombinations.length}`);
        
        // Separate by visibility and duplication status
        const duplicates = allCombinations.filter(rec => rec.name.includes('(*)'));
        const nonDuplicates = allCombinations.filter(rec => !rec.name.includes('(*)'));
        const hidden = allCombinations.filter(rec => rec.is_hidden_from_menu_selections === 1);
        const visible = allCombinations.filter(rec => rec.is_hidden_from_menu_selections !== 1);
        
        console.log(`   With (*): ${duplicates.length}`);
        console.log(`   Without (*): ${nonDuplicates.length}`);
        console.log(`   Hidden from menu: ${hidden.length}`);
        console.log(`   Visible in menu: ${visible.length}`);
        
        // Test the specific "Vendor Meals" case with hidden filter
        console.log(`\n🎯 TESTING VENDOR MEALS WITH HIDDEN FILTER:`);
        const vendorMealsBase = "Included in Event Experience | Vendor Meals";
        
        const vendorMealsMatches = allCombinations.filter(rec => 
            rec.name.includes("Included in Event Experience") && 
            rec.name.includes("Vendor Meals") &&
            !rec.name.includes('(*)')
        );
        
        console.log(`   Found ${vendorMealsMatches.length} potential vendor meals originals:`);
        vendorMealsMatches.forEach(match => {
            console.log(`     - ID ${match.id}: "${match.name}"`);
            console.log(`       Hidden: ${match.is_hidden_from_menu_selections === 1 ? 'YES' : 'NO'}`);
            console.log(`       Items: ${match.items?.length || 0}`);
        });
        
        // Now test broader pattern with hidden originals
        console.log(`\n🔍 TESTING BROADER PATTERN WITH HIDDEN ORIGINALS:`);
        
        const brokenDuplicates = duplicates.filter(rec => !rec.items || rec.items.length === 0);
        
        let foundPairs = [];
        let testCount = 0;
        
        brokenDuplicates.slice(0, 20).forEach(broken => {
            const baseName = broken.name.replace(' (*)', '');
            
            // Look for originals in ALL records (including hidden)
            const exactMatch = allCombinations.find(rec => 
                rec.name === baseName && !rec.name.includes('(*)')
            );
            
            if (exactMatch) {
                testCount++;
                const isHidden = exactMatch.is_hidden_from_menu_selections === 1;
                const hasItems = exactMatch.items && exactMatch.items.length > 0;
                
                if (hasItems) {
                    foundPairs.push({
                        brokenId: broken.id,
                        brokenName: broken.name,
                        originalId: exactMatch.id,
                        originalName: exactMatch.name,
                        originalItems: exactMatch.items.length,
                        originalHidden: isHidden,
                        priority: getRepairPriority(broken.name)
                    });
                }
                
                console.log(`   ${testCount}. ${hasItems ? '✅' : '❌'} "${baseName}"`);
                console.log(`      Original ID: ${exactMatch.id}, Items: ${exactMatch.items?.length || 0}, Hidden: ${isHidden ? 'YES' : 'NO'}`);
            }
        });
        
        // Sort by priority and show results
        foundPairs.sort((a, b) => b.priority - a.priority);
        
        console.log(`\n🎉 BREAKTHROUGH: FOUND ${foundPairs.length} VIABLE REPAIR PAIRS!`);
        
        if (foundPairs.length > 0) {
            console.log(`\n🏆 TOP REPAIR CANDIDATES:`);
            foundPairs.slice(0, 5).forEach((pair, i) => {
                console.log(`\n${i + 1}. PRIORITY ${pair.priority}/10`);
                console.log(`   Broken: ID ${pair.brokenId} - "${pair.brokenName}"`);
                console.log(`   Original: ID ${pair.originalId} - "${pair.originalName}"`);
                console.log(`   Original: ${pair.originalItems} items, Hidden: ${pair.originalHidden ? 'YES' : 'NO'}`);
            });
            
            const best = foundPairs[0];
            console.log(`\n🚀 RECOMMENDED TEST CASE:`);
            console.log(`   testHiddenOriginalRepair(${best.brokenId}, ${best.originalId})`);
            
            // Show statistics
            const hiddenOriginals = foundPairs.filter(p => p.originalHidden).length;
            console.log(`\n📊 HIDDEN ORIGINALS ANALYSIS:`);
            console.log(`   Total viable pairs: ${foundPairs.length}`);
            console.log(`   Originals that are hidden: ${hiddenOriginals}`);
            console.log(`   Originals that are visible: ${foundPairs.length - hiddenOriginals}`);
            
            if (hiddenOriginals > 0) {
                console.log(`\n💡 CONFIRMED: Originals were hidden during duplication process!`);
            }
        } else {
            console.log(`\n❌ Still no viable pairs found. Need deeper investigation.`);
        }
    });
}

/**
 * Priority scoring for repair candidates
 */
function getRepairPriority(name) {
    let priority = 5;
    
    if (name.includes('Event Experience')) priority += 3;
    if (name.includes('Wedding Experience')) priority += 3;
    if (name.includes('Vendor Meals')) priority += 2;
    if (name.includes('Bar')) priority += 2;
    if (name.includes('Setup')) priority += 1;
    if (name.includes('Rental')) priority += 1;
    
    return Math.min(10, priority);
}

/**
 * Test a repair with hidden original
 */
function testHiddenOriginalRepair(brokenId, originalId) {
    console.log(`🧪 TESTING REPAIR WITH HIDDEN ORIGINAL`);
    console.log(`   Broken ID: ${brokenId}`);
    console.log(`   Original ID: ${originalId}`);
    console.log('='.repeat(50));
    
    databaseConnection.obj.getById('inventory_billable_combinations', brokenId, function(broken) {
        databaseConnection.obj.getById('inventory_billable_combinations', originalId, function(original) {
            
            console.log(`📋 BROKEN RECORD:`);
            console.log(`   ID: ${broken.id}`);
            console.log(`   Name: "${broken.name}"`);
            console.log(`   Items: ${broken.items?.length || 0}`);
            console.log(`   Hidden: ${broken.is_hidden_from_menu_selections === 1 ? 'YES' : 'NO'}`);
            console.log(`   Category: ${broken.category}`);
            
            console.log(`\n📋 ORIGINAL RECORD (HIDDEN):`);
            console.log(`   ID: ${original.id}`);
            console.log(`   Name: "${original.name}"`);
            console.log(`   Items: ${original.items?.length || 0}`);
            console.log(`   Hidden: ${original.is_hidden_from_menu_selections === 1 ? 'YES' : 'NO'}`);
            console.log(`   Category: ${original.category}`);
            
            if (original.items && original.items.length > 0) {
                console.log(`\n📝 ORIGINAL ITEMS STRUCTURE:`);
                original.items.forEach((item, i) => {
                    console.log(`   Item ${i + 1}:`);
                    console.log(`     inventory_group: ${item.inventory_group || 'MISSING'}`);
                    console.log(`     qty: ${JSON.stringify(item.qty || {})}`);
                    console.log(`     choices: ${item.choices?.length || 0}`);
                });
                
                console.log(`\n🔧 REPAIR STRATEGY:`);
                console.log(`   1. ✅ Hidden original found with ${original.items.length} items`);
                console.log(`   2. 🔄 Deep duplicate these inventory groups:`);
                original.items.forEach((item, i) => {
                    console.log(`      - Group ${item.inventory_group}`);
                });
                console.log(`   3. 🔗 Update broken record with new group references`);
                console.log(`   4. 🧪 Test BEO generation`);
                console.log(`   5. 👁️ Set broken record to visible (is_hidden_from_menu_selections = 0)`);
                
                console.log(`\n🎯 THIS IS PERFECT FOR TESTING!`);
                console.log(`\n🚀 NEXT: Develop repair script based on this structure`);
                
            } else {
                console.log(`\n❌ Even hidden original has empty items - need different approach`);
            }
        });
    });
}

/**
 * Quick search for specific item
 */
function searchForVendorMealsHidden() {
    console.log('🔍 SPECIFIC VENDOR MEALS SEARCH (INCLUDING HIDDEN)');
    
    databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombinations) {
        const matches = allCombinations.filter(rec => 
            rec.name.includes("Vendor Meals") || rec.name.includes("vendor meals")
        );
        
        console.log(`Found ${matches.length} vendor meals records:`);
        matches.forEach(match => {
            console.log(`\n- ID ${match.id}: "${match.name}"`);
            console.log(`  Items: ${match.items?.length || 0}`);
            console.log(`  Hidden: ${match.is_hidden_from_menu_selections === 1 ? 'YES' : 'NO'}`);
            console.log(`  Has (*): ${match.name.includes('(*)') ? 'YES' : 'NO'}`);
        });
    });
}

console.log('✅ HIDDEN ORIGINALS FINDER LOADED');
console.log('📋 Available commands:');
console.log('   findHiddenOriginals() - Search including hidden menu items');
console.log('   testHiddenOriginalRepair(brokenId, originalId) - Test specific hidden pair');
console.log('   searchForVendorMealsHidden() - Specific vendor meals search');
console.log('');
console.log('🚀 START HERE: findHiddenOriginals()');
