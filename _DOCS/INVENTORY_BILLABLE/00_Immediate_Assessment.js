// =================================================================================================
// IMMEDIATE ASSESSMENT - RUN THIS FIRST
// =================================================================================================
// Copy and paste this into your browser console to get immediate damage assessment

console.log('🚀 LOADING INVENTORY ASSESSMENT TOOLS...');

// Quick damage check for immediate understanding
function quickDamageCheck() {
    console.log('🔍 QUICK DAMAGE ASSESSMENT');
    console.log('='.repeat(50));
    
    databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombinations) {
        const duplicates = allCombinations.filter(rec => rec.name.includes('(*)'));
        const broken = duplicates.filter(rec => !rec.items || rec.items.length === 0);
        const working = duplicates.filter(rec => rec.items && rec.items.length > 0);
        
        console.log(`📊 IMMEDIATE RESULTS:`);
        console.log(`   Total combinations: ${allCombinations.length}`);
        console.log(`   Duplicated combinations (*): ${duplicates.length}`);
        console.log(`   🔴 BROKEN (empty items): ${broken.length}`);
        console.log(`   ✅ Working duplicates: ${working.length}`);
        
        if (broken.length > 0) {
            console.log(`\n🚨 CRITICAL BROKEN ITEMS:`);
            broken.slice(0, 10).forEach(rec => {
                console.log(`   - ID ${rec.id}: "${rec.name}"`);
            });
            if (broken.length > 10) {
                console.log(`   ... and ${broken.length - 10} more`);
            }
        }
        
        // Check for the specific test case
        const testCase = broken.find(rec => rec.name.includes('Vendor Meals'));
        if (testCase) {
            console.log(`\n🎯 TEST CASE FOUND: ID ${testCase.id} - "${testCase.name}"`);
        }
        
        console.log(`\n📈 REPAIR SCOPE: ${broken.length} items need immediate attention`);
        console.log(`💡 NEXT STEP: Run fullInventoryAssessment() for complete analysis`);
    });
}

// Enhanced assessment for detailed analysis
function fullInventoryAssessment() {
    console.log('🔍 STARTING FULL INVENTORY ASSESSMENT');
    console.log('='.repeat(80));
    
    // This loads the complete assessment from 03_Assessment_Script.js
    // You can copy the full function from that file here if needed
    
    console.log('📋 For complete assessment, please load 03_Assessment_Script.js');
    console.log('🎯 Available commands after loading:');
    console.log('   - runFullInventoryAssessment()');
    console.log('   - analyzeSpecificRecord(id)'); 
    console.log('   - analyzeVendorMealsRecord()');
}

// Specific test for your broken record
function testVendorMealsCase() {
    console.log('🧪 TESTING VENDOR MEALS CASE (ID: 20591444)');
    
    databaseConnection.obj.getById('inventory_billable_combinations', 20591444, function(record) {
        if (!record) {
            console.log('❌ Record 20591444 not found');
            return;
        }
        
        console.log(`📋 RECORD ANALYSIS:`);
        console.log(`   ID: ${record.id}`);
        console.log(`   Name: "${record.name}"`);
        console.log(`   Items array length: ${record.items?.length || 0}`);
        console.log(`   Data source ID: ${record.data_source_id || 'MISSING'}`);
        console.log(`   Status: ${record.items?.length > 0 ? '✅ WORKING' : '❌ BROKEN'}`);
        
        if (record.items?.length === 0 || !record.items) {
            console.log(`\n🔍 LOOKING FOR ORIGINAL...`);
            const originalName = record.name.replace(' (*)', '');
            
            databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(all) {
                const original = all.find(rec => rec.name === originalName);
                if (original) {
                    console.log(`✅ ORIGINAL FOUND:`);
                    console.log(`   Original ID: ${original.id}`);
                    console.log(`   Original items: ${original.items?.length || 0}`);
                    console.log(`   🔧 REPAIR STRATEGY: Copy items from original and duplicate groups`);
                } else {
                    console.log(`❌ ORIGINAL NOT FOUND - Manual reconstruction needed`);
                }
            });
        }
    });
}

// Commands available immediately
console.log('✅ ASSESSMENT TOOLS LOADED');
console.log('📋 Available commands:');
console.log('   quickDamageCheck() - Fast overview of damage');
console.log('   testVendorMealsCase() - Test specific broken record');
console.log('   fullInventoryAssessment() - Load complete assessment tools');
console.log('');
console.log('🚀 START HERE: quickDamageCheck()');
