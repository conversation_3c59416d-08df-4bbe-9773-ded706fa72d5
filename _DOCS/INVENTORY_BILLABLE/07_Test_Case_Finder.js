// =================================================================================================
// FIND BETTER TEST CASE - Choose a broken record with findable original
// =================================================================================================

/**
 * Find the best test case from our broken records list
 */
function findBestTestCase() {
    console.log('🎯 FINDING BEST TEST CASE FROM BROKEN RECORDS');
    console.log('='.repeat(60));
    
    // Get all combinations first
    databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombinations) {
        
        // Get broken records
        const broken = allCombinations.filter(rec => 
            rec.name.includes('(*)') && (!rec.items || rec.items.length === 0)
        );
        
        console.log(`🔍 Testing ${Math.min(10, broken.length)} broken records for originals...`);
        
        let testCandidates = [];
        let completed = 0;
        
        // Test first 10 broken records to find ones with findable originals
        broken.slice(0, 10).forEach(brokenRecord => {
            const baseName = brokenRecord.name.replace(' (*)', '');
            const original = allCombinations.find(rec => rec.name === baseName);
            
            if (original && original.items && original.items.length > 0) {
                testCandidates.push({
                    brokenId: brokenRecord.id,
                    brokenName: brokenRecord.name,
                    originalId: original.id,
                    originalName: original.name,
                    originalItemsCount: original.items.length,
                    priority: getTestPriority(brokenRecord.name)
                });
            }
            
            completed++;
            if (completed === Math.min(10, broken.length)) {
                // Sort by priority and display results
                testCandidates.sort((a, b) => b.priority - a.priority);
                
                console.log(`\n✅ FOUND ${testCandidates.length} VIABLE TEST CASES:`);
                testCandidates.forEach((candidate, i) => {
                    console.log(`\n${i + 1}. 🎯 PRIORITY ${candidate.priority}/10`);
                    console.log(`   Broken: ID ${candidate.brokenId} - "${candidate.brokenName}"`);
                    console.log(`   Original: ID ${candidate.originalId} - "${candidate.originalName}"`);
                    console.log(`   Original has ${candidate.originalItemsCount} items`);
                });
                
                if (testCandidates.length > 0) {
                    const best = testCandidates[0];
                    console.log(`\n🏆 RECOMMENDED TEST CASE:`);
                    console.log(`   Broken ID: ${best.brokenId}`);
                    console.log(`   Original ID: ${best.originalId}`);
                    console.log(`\n🚀 NEXT STEP: testRepairCandidate(${best.brokenId}, ${best.originalId})`);
                } else {
                    console.log(`\n❌ No viable test cases found in first 10. Searching more...`);
                    findMoreTestCases(broken, allCombinations);
                }
            }
        });
    });
}

/**
 * Priority scoring for test cases (higher = better test case)
 */
function getTestPriority(name) {
    let priority = 5; // Base priority
    
    // Higher priority for important packages
    if (name.includes('Event Experience')) priority += 3;
    if (name.includes('Wedding Experience')) priority += 3;
    if (name.includes('Bar')) priority += 2;
    if (name.includes('Setup')) priority += 2;
    if (name.includes('Rental')) priority += 1;
    
    // Lower priority for very complex or unusual items
    if (name.length > 80) priority -= 1;
    if (name.includes('Additional Production')) priority -= 1;
    
    return Math.min(10, priority);
}

/**
 * Search more records if first 10 don't work
 */
function findMoreTestCases(broken, allCombinations) {
    console.log(`\n🔍 SEARCHING MORE RECORDS...`);
    
    let found = 0;
    for (let i = 10; i < Math.min(30, broken.length) && found < 5; i++) {
        const brokenRecord = broken[i];
        const baseName = brokenRecord.name.replace(' (*)', '');
        const original = allCombinations.find(rec => rec.name === baseName);
        
        if (original && original.items && original.items.length > 0) {
            console.log(`\n✅ FOUND VIABLE CASE:`);
            console.log(`   Broken: ID ${brokenRecord.id} - "${brokenRecord.name}"`);
            console.log(`   Original: ID ${original.id} - "${original.name}"`);
            console.log(`   Original has ${original.items.length} items`);
            
            if (found === 0) {
                console.log(`\n🚀 QUICK TEST: testRepairCandidate(${brokenRecord.id}, ${original.id})`);
            }
            found++;
        }
    }
    
    if (found === 0) {
        console.log(`\n❌ This is unusual - may need manual investigation of naming patterns`);
    }
}

/**
 * Test a specific repair candidate pair
 */
function testRepairCandidate(brokenId, originalId) {
    console.log(`🧪 TESTING REPAIR CANDIDATE`);
    console.log(`   Broken ID: ${brokenId}`);
    console.log(`   Original ID: ${originalId}`);
    console.log('='.repeat(50));
    
    databaseConnection.obj.getById('inventory_billable_combinations', brokenId, function(broken) {
        databaseConnection.obj.getById('inventory_billable_combinations', originalId, function(original) {
            
            console.log(`📋 BROKEN RECORD ANALYSIS:`);
            console.log(`   ID: ${broken.id}`);
            console.log(`   Name: "${broken.name}"`);
            console.log(`   Items: ${broken.items?.length || 0}`);
            console.log(`   Category: ${broken.category}`);
            
            console.log(`\n📋 ORIGINAL RECORD ANALYSIS:`);
            console.log(`   ID: ${original.id}`);
            console.log(`   Name: "${original.name}"`);
            console.log(`   Items: ${original.items?.length || 0}`);
            console.log(`   Category: ${original.category}`);
            
            if (original.items && original.items.length > 0) {
                console.log(`\n📝 ORIGINAL ITEMS STRUCTURE:`);
                original.items.forEach((item, i) => {
                    console.log(`   Item ${i + 1}:`);
                    console.log(`     inventory_group: ${item.inventory_group}`);
                    console.log(`     qty: ${JSON.stringify(item.qty || {})}`);
                    console.log(`     choices: ${item.choices?.length || 0}`);
                });
                
                console.log(`\n🔧 REPAIR STRATEGY FOR THIS CASE:`);
                console.log(`   1. ✅ Original found with ${original.items.length} items`);
                console.log(`   2. 🔄 Need to duplicate ${original.items.length} inventory groups`);
                console.log(`   3. 🔗 Update broken record with new group references`);
                console.log(`   4. 🧪 Test BEO generation`);
                
                console.log(`\n🎯 THIS IS A GOOD TEST CASE!`);
                console.log(`📋 Groups to duplicate: ${original.items.map(item => item.inventory_group).join(', ')}`);
                
            } else {
                console.log(`\n❌ Original also has empty items - not a good test case`);
            }
        });
    });
}

// Quick commands
console.log('✅ TEST CASE FINDER LOADED');
console.log('📋 Available commands:');
console.log('   findBestTestCase() - Find best broken→original pairs for testing');
console.log('   testRepairCandidate(brokenId, originalId) - Test specific pair');
console.log('');
console.log('🚀 START HERE: findBestTestCase()');
