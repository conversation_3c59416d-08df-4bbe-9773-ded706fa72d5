// =================================================================================================
// RECURSIVE STRUCTURE FETCHER - Complete tree traversal for inventory structures
// =================================================================================================

/**
 * Recursively fetch complete inventory structure tree
 */
function fetchCompleteInventoryTree(combinationId) {
    console.log(`🌳 FETCHING COMPLETE INVENTORY TREE FOR: ${combinationId}`);
    console.log('This will recursively fetch ALL referenced inventory groups...');
    console.log('='.repeat(60));
    
    const fetchedGroups = new Map(); // Cache to avoid duplicate fetches
    const fetchQueue = []; // Queue of group IDs to fetch
    let totalFetches = 0;
    
    // Start with the combination
    databaseConnection.obj.getById('inventory_billable_combinations', combinationId, function(combination) {
        if (!combination) {
            console.log(`❌ Combination ${combinationId} not found`);
            return;
        }
        
        console.log(`📋 ROOT COMBINATION:`);
        console.log(`   ID: ${combination.id}`);
        console.log(`   Name: "${combination.name}"`);
        console.log(`   Items: ${combination.items?.length || 0}`);
        
        // Extract all group IDs from combination items
        if (combination.items) {
            combination.items.forEach(item => {
                if (item.inventory_group && item.inventory_group > 0) {
                    fetchQueue.push(item.inventory_group);
                }
                if (item.choices) {
                    item.choices.forEach(choice => {
                        if (choice.inventory_group && choice.inventory_group > 0) {
                            fetchQueue.push(choice.inventory_group);
                        }
                    });
                }
            });
        }
        
        console.log(`\n🎯 INITIAL QUEUE: ${fetchQueue.length} groups to fetch`);
        console.log(`   Group IDs: ${fetchQueue.join(', ')}`);
        
        // Start recursive fetching
        fetchGroupsRecursively(fetchQueue, fetchedGroups, function() {
            displayCompleteTree(combination, fetchedGroups);
        });
    });
}

/**
 * Recursively fetch inventory groups and their nested references
 */
function fetchGroupsRecursively(queue, fetchedGroups, onComplete) {
    if (queue.length === 0) {
        console.log(`\n✅ RECURSIVE FETCH COMPLETE`);
        console.log(`   Total groups fetched: ${fetchedGroups.size}`);
        onComplete();
        return;
    }
    
    const currentId = queue.shift();
    
    // Skip if already fetched
    if (fetchedGroups.has(currentId)) {
        fetchGroupsRecursively(queue, fetchedGroups, onComplete);
        return;
    }
    
    console.log(`🔄 Fetching group ${currentId}... (${queue.length} remaining)`);
    
    databaseConnection.obj.getById('inventory_billable_groups', currentId, function(group) {
        if (group) {
            fetchedGroups.set(currentId, group);
            
            // Extract nested group references from this group
            const nestedGroupIds = [];
            
            if (group.items) {
                group.items.forEach(item => {
                    if (item.inventory_group && item.inventory_group > 0) {
                        if (!fetchedGroups.has(item.inventory_group) && !queue.includes(item.inventory_group)) {
                            nestedGroupIds.push(item.inventory_group);
                        }
                    }
                    if (item.choices) {
                        item.choices.forEach(choice => {
                            if (choice.inventory_group && choice.inventory_group > 0) {
                                if (!fetchedGroups.has(choice.inventory_group) && !queue.includes(choice.inventory_group)) {
                                    nestedGroupIds.push(choice.inventory_group);
                                }
                            }
                        });
                    }
                });
            }
            
            // Add nested groups to queue
            if (nestedGroupIds.length > 0) {
                console.log(`   📦 Group ${currentId} ("${group.name}") references ${nestedGroupIds.length} more groups: ${nestedGroupIds.join(', ')}`);
                queue.push(...nestedGroupIds);
            }
        } else {
            console.log(`   ❌ Group ${currentId} not found`);
            fetchedGroups.set(currentId, null);
        }
        
        // Continue with next item in queue
        setTimeout(() => {
            fetchGroupsRecursively(queue, fetchedGroups, onComplete);
        }, 50); // Small delay to avoid overwhelming server
    });
}

/**
 * Display the complete tree structure
 */
function displayCompleteTree(combination, fetchedGroups) {
    console.log(`\n🌳 COMPLETE INVENTORY TREE STRUCTURE`);
    console.log('='.repeat(80));
    
    console.log(`\n📋 ROOT: ${combination.name} (ID: ${combination.id})`);
    
    if (combination.items) {
        combination.items.forEach((item, i) => {
            console.log(`\n├─ ITEM ${i + 1}: ${item.name || 'Unnamed'}`);
            console.log(`│  └─ Quantity: ${JSON.stringify(item.qty)}`);
            
            if (item.inventory_group) {
                displayGroupTree(item.inventory_group, fetchedGroups, '│     ', 1);
            }
            
            if (item.choices && item.choices.length > 0) {
                console.log(`│  └─ CHOICES (${item.choices.length}):`);
                item.choices.forEach((choice, j) => {
                    console.log(`│     ├─ Choice ${j + 1}: Additional price ${choice.additional_price || 0} cents`);
                    if (choice.inventory_group) {
                        displayGroupTree(choice.inventory_group, fetchedGroups, '│     │  ', 1);
                    }
                });
            }
        });
    }
    
    console.log(`\n📊 TREE STATISTICS:`);
    console.log(`   Total groups in tree: ${fetchedGroups.size}`);
    console.log(`   Root items: ${combination.items?.length || 0}`);
    
    // Count total choices across all items
    let totalChoices = 0;
    if (combination.items) {
        combination.items.forEach(item => {
            totalChoices += item.choices?.length || 0;
        });
    }
    console.log(`   Total choices: ${totalChoices}`);
    
    // Store results globally
    window.completeInventoryTree = {
        combination: combination,
        groups: Object.fromEntries(fetchedGroups),
        statistics: {
            totalGroups: fetchedGroups.size,
            rootItems: combination.items?.length || 0,
            totalChoices: totalChoices
        }
    };
    
    console.log(`\n💾 Complete tree stored in: window.completeInventoryTree`);
    console.log(`\n🎯 THIS IS THE COMPLETE STRUCTURE FOR DUPLICATION!`);
    console.log(`\n🔧 REPAIR STRATEGY:`);
    console.log(`   1. Copy this items array structure to broken combinations`);
    console.log(`   2. Deep duplicate ALL ${fetchedGroups.size} inventory groups with (*) naming`);
    console.log(`   3. Update ALL inventory_group references to point to new duplicates`);
    console.log(`   4. Maintain exact same nested structure and choices`);
}

/**
 * Display individual group in tree format
 */
function displayGroupTree(groupId, fetchedGroups, prefix, depth) {
    const group = fetchedGroups.get(groupId);
    
    if (!group) {
        console.log(`${prefix}└─ GROUP ${groupId}: ❌ NOT FOUND`);
        return;
    }
    
    console.log(`${prefix}└─ GROUP ${groupId}: "${group.name}"`);
    console.log(`${prefix}   Price: ${group.price || 0} cents, Per person: ${group.price_per_person || 0} cents`);
    console.log(`${prefix}   Hidden: ${group.is_hidden_from_menu_selections === 1 ? 'YES' : 'NO'}`);
    
    if (group.items && group.items.length > 0) {
        console.log(`${prefix}   Items (${group.items.length}):`);
        group.items.forEach((item, i) => {
            const itemPrefix = prefix + '      ';
            console.log(`${itemPrefix}├─ Item ${i + 1}: ${item.name || 'Unnamed'}`);
            
            if (item.inventory_group && depth < 3) { // Prevent infinite recursion
                displayGroupTree(item.inventory_group, fetchedGroups, itemPrefix + '│  ', depth + 1);
            }
            
            if (item.choices && item.choices.length > 0) {
                console.log(`${itemPrefix}└─ Choices (${item.choices.length}):`);
                item.choices.forEach((choice, j) => {
                    console.log(`${itemPrefix}   ├─ Choice ${j + 1}: Price +${choice.additional_price || 0}`);
                    if (choice.inventory_group && depth < 3) {
                        displayGroupTree(choice.inventory_group, fetchedGroups, itemPrefix + '   │  ', depth + 1);
                    }
                });
            }
        });
    }
}

/**
 * Quick test with known working record
 */
function testCompleteTreeFetch() {
    console.log('🧪 TESTING COMPLETE TREE FETCH');
    console.log('Using: "Ultimate Bar Selection - Cellar\'s Reserve Wine Selection (*)"');
    fetchCompleteInventoryTree(20456674);
}

/**
 * Generate repair blueprint from complete tree
 */
function generateRepairBlueprint() {
    if (!window.completeInventoryTree) {
        console.log('❌ No complete tree loaded. Run testCompleteTreeFetch() first.');
        return;
    }
    
    const tree = window.completeInventoryTree;
    const blueprint = {
        templateCombination: tree.combination,
        itemsStructure: tree.combination.items,
        allReferencedGroups: Object.keys(tree.groups).map(id => parseInt(id)),
        totalGroupsToDuplicate: tree.statistics.totalGroups,
        repairInstructions: [
            '1. Copy items array from template to broken combination',
            '2. Deep duplicate all referenced inventory groups',
            '3. Update all inventory_group references',
            '4. Maintain exact structure including nested choices',
            '5. Test BEO generation'
        ]
    };
    
    console.log(`\n📋 REPAIR BLUEPRINT GENERATED:`);
    console.log(`   Template: "${blueprint.templateCombination.name}"`);
    console.log(`   Items to copy: ${blueprint.itemsStructure.length}`);
    console.log(`   Groups to duplicate: ${blueprint.totalGroupsToDuplicate}`);
    console.log(`   Group IDs: ${blueprint.allReferencedGroups.join(', ')}`);
    
    window.repairBlueprint = blueprint;
    console.log(`\n💾 Blueprint stored in: window.repairBlueprint`);
    
    return blueprint;
}

console.log('✅ RECURSIVE STRUCTURE FETCHER LOADED');
console.log('📋 Available commands:');
console.log('   testCompleteTreeFetch() - Test with working record');
console.log('   fetchCompleteInventoryTree(id) - Fetch complete tree for any combination');
console.log('   generateRepairBlueprint() - Create repair blueprint from fetched tree');
console.log('');
console.log('🚀 START HERE: testCompleteTreeFetch()');
