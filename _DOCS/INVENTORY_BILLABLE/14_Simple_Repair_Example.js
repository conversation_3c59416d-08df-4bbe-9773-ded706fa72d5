// =================================================================================================
// SIMPLE REPAIR EXAMPLE - Focus on one working item for practical repair template
// =================================================================================================

/**
 * Analyze a simple working example to create repair template
 */
function createSimpleRepairTemplate() {
    console.log('Creating simple repair template using known working structure...');
    console.log('='.repeat(60));
    
    // Use the working combination we found: ID 20456674
    // "Ultimate Bar Selection - Cellar's Reserve Wine Selection (*)"
    
    databaseConnection.obj.getById('inventory_billable_combinations', 20456674, function(workingCombo) {
        if (!workingCombo || !workingCombo.items) {
            console.log('❌ Working combination not found or has no items');
            return;
        }
        
        console.log(`📋 WORKING COMBINATION TEMPLATE:`);
        console.log(`   ID: ${workingCombo.id}`);
        console.log(`   Name: "${workingCombo.name}"`);
        console.log(`   Items: ${workingCombo.items.length}`);
        console.log(`   Category: ${workingCombo.category}`);
        
        // Take just the first item as our simple example
        const firstItem = workingCombo.items[0];
        console.log(`\n🎯 FOCUSING ON FIRST ITEM:`);
        console.log(`   Item ID: ${firstItem.id}`);
        console.log(`   inventory_group: ${firstItem.inventory_group}`);
        console.log(`   qty: ${JSON.stringify(firstItem.qty)}`);
        console.log(`   choices: ${firstItem.choices?.length || 0}`);
        
        // Fetch just this one inventory group
        if (firstItem.inventory_group) {
            databaseConnection.obj.getById('inventory_billable_groups', firstItem.inventory_group, function(group) {
                if (group) {
                    console.log(`\n📦 INVENTORY GROUP DETAILS:`);
                    console.log(`   Group ID: ${group.id}`);
                    console.log(`   Group name: "${group.name}"`);
                    console.log(`   Group items: ${group.items?.length || 0}`);
                    console.log(`   Group category: ${group.category}`);
                    console.log(`   Group price: ${group.price || 0} cents`);
                    console.log(`   Group hidden: ${group.is_hidden_from_menu_selections === 1 ? 'YES' : 'NO'}`);
                    
                    // Create simple repair template
                    const repairTemplate = {
                        sourceComboId: workingCombo.id,
                        sourceComboName: workingCombo.name,
                        exampleItem: firstItem,
                        referencedGroup: {
                            id: group.id,
                            name: group.name,
                            structure: group
                        },
                        repairSteps: [
                            '1. Copy items array from working combination to broken combination',
                            '2. For each item.inventory_group, create duplicate with (*) suffix',
                            '3. Update item.inventory_group to reference new duplicate ID',
                            '4. Test that broken combination now works'
                        ]
                    };
                    
                    console.log(`\n🔧 SIMPLE REPAIR TEMPLATE CREATED:`);
                    console.log(`\n📝 REPAIR PROCESS:`);
                    repairTemplate.repairSteps.forEach(step => console.log(`   ${step}`));
                    
                    console.log(`\n💡 EXAMPLE REPAIR:`);
                    console.log(`   Broken combo gets items array: ${JSON.stringify([firstItem], null, 2)}`);
                    console.log(`   Group "${group.name}" gets duplicated as "${group.name} (*)"`);
                    console.log(`   items[0].inventory_group updates from ${group.id} to NEW_DUPLICATE_ID`);
                    
                    // Store template globally
                    window.simpleRepairTemplate = repairTemplate;
                    console.log(`\n💾 Template stored in: window.simpleRepairTemplate`);
                    
                    console.log(`\n🚀 READY TO TEST: testSimpleRepair(brokenComboId)`);
                } else {
                    console.log(`❌ Inventory group ${firstItem.inventory_group} not found`);
                }
            });
        }
    });
}

/**
 * Test the simple repair approach on a broken combination
 */
function testSimpleRepair(brokenComboId) {
    if (!window.simpleRepairTemplate) {
        console.log('❌ No repair template loaded. Run createSimpleRepairTemplate() first.');
        return;
    }
    
    console.log(`🧪 TESTING SIMPLE REPAIR ON BROKEN COMBO: ${brokenComboId}`);
    console.log('='.repeat(50));
    
    const template = window.simpleRepairTemplate;
    
    databaseConnection.obj.getById('inventory_billable_combinations', brokenComboId, function(brokenCombo) {
        if (!brokenCombo) {
            console.log(`❌ Broken combination ${brokenComboId} not found`);
            return;
        }
        
        console.log(`📋 BROKEN COMBINATION:`);
        console.log(`   ID: ${brokenCombo.id}`);
        console.log(`   Name: "${brokenCombo.name}"`);
        console.log(`   Current items: ${brokenCombo.items?.length || 0}`);
        console.log(`   Category: ${brokenCombo.category}`);
        
        console.log(`\n🔧 REPAIR SIMULATION:`);
        console.log(`   Source template: "${template.sourceComboName}"`);
        console.log(`   Would copy ${template.exampleItem ? 1 : 0} items`);
        console.log(`   Would duplicate group: "${template.referencedGroup.name}"`);
        
        // Show what the repair would look like
        const repairedItems = [
            {
                ...template.exampleItem,
                inventory_group: 'NEW_DUPLICATE_ID_HERE'
            }
        ];
        
        console.log(`\n📝 REPAIRED STRUCTURE PREVIEW:`);
        console.log(`   items: ${JSON.stringify(repairedItems, null, 2)}`);
        
        console.log(`\n✅ REPAIR STRATEGY VALIDATED`);
        console.log(`\n🚀 NEXT: Implement actual repair with group duplication`);
    });
}

/**
 * Find a simple broken combination to test with
 */
function findSimpleBrokenCombo() {
    console.log('🔍 Finding a simple broken combination for testing...');
    
    databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombos) {
        const broken = allCombos.filter(rec => 
            rec.name.includes('(*)') && 
            (!rec.items || rec.items.length === 0)
        );
        
        if (broken.length > 0) {
            const testCase = broken[0];
            console.log(`\n🎯 FOUND TEST CASE:`);
            console.log(`   ID: ${testCase.id}`);
            console.log(`   Name: "${testCase.name}"`);
            console.log(`   Items: ${testCase.items?.length || 0}`);
            
            console.log(`\n🚀 TEST COMMAND: testSimpleRepair(${testCase.id})`);
            
            return testCase.id;
        } else {
            console.log('❌ No broken combinations found');
        }
    });
}

console.log('✅ SIMPLE REPAIR EXAMPLE LOADED');
console.log('📋 Available commands:');
console.log('   createSimpleRepairTemplate() - Create template from working example');
console.log('   testSimpleRepair(brokenId) - Test repair on broken combination');  
console.log('   findSimpleBrokenCombo() - Find broken combination for testing');
console.log('');
console.log('🚀 START HERE: createSimpleRepairTemplate()');
