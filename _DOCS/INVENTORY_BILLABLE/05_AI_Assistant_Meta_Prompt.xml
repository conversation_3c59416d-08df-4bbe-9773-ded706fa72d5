<?xml version="1.0" encoding="UTF-8"?>
<BentoInventorySystem version="1.0" updated="2025-01-20">

  <!--
    BENTO INVENTORY SYSTEM AI ASSISTANT CONTEXT
    
    This meta-prompt provides comprehensive context for AI assistants working on
    the Bento inventory billable system, specifically addressing duplication issues
    and BEO integration requirements.
  -->

  <SystemOverview>
    <InventoryArchitecture>
      <Level1>
        <Name>inventory_billable_combination_categories</Name>
        <Purpose>Top-level categorization containers</Purpose>
        <Example>Food - 2025 Pricing (*)</Example>
      </Level1>
      
      <Level2>
        <Name>inventory_billable_combinations</Name>
        <Purpose>Main menu items/packages with items array</Purpose>
        <CriticalField>items</CriticalField>
        <StructurePattern>
          <![CDATA[
          {
            "id": 20591444,
            "name": "Item Name (*)",
            "items": [
              {
                "id": 1,
                "inventory_group": 20435097,
                "qty": {"quantity": 1, "unit_type": "servings"},
                "choices": []
              }
            ],
            "data_source_id": 18240191
          }
          ]]>
        </StructurePattern>
      </Level2>
      
      <Level3>
        <Name>inventory_billable_groups</Name>
        <Purpose>Component building blocks with nested choice structures</Purpose>
        <NestedStructure>
          <![CDATA[
          {
            "id": 20434630,
            "name": "Component Name (*)",
            "items": [
              {
                "id": 1,
                "name": "Component Item",
                "inventory_group": 6225382,
                "choices": [
                  {
                    "id": 13,
                    "inventory_group": 6225383,
                    "additional_price": 0
                  }
                ]
              }
            ]
          }
          ]]>
        </NestedStructure>
      </Level3>
    </InventoryArchitecture>

    <BEOIntegration>
      <Description>
        BEO (Banquet Event Order) system renders inventory data into PDF documents.
        Critical dependency on complete inventory structure for proper rendering.
      </Description>
      
      <DataFlow>
        <Step>Menu → Menu Sections → Menu Line Items</Step>
        <Step>Line Items → inventory_billable_combinations</Step>
        <Step>Combinations.items → inventory_billable_groups</Step>
        <Step>Groups.choices → nested inventory_billable_groups</Step>
        <Step>BEO Generation → get_full_recipe() expansion</Step>
      </DataFlow>

      <CriticalPoints>
        <Point>Empty items arrays break BEO rendering completely</Point>
        <Point>Broken inventory_group references cause missing content</Point>
        <Point>Category filtering depends on proper group references</Point>
        <Point>Three instance variations: standard, Infinity, Dream Catering</Point>
      </CriticalPoints>
    </BEOIntegration>
  </SystemOverview>

  <CommonIssues>
    <ShallowCopyProblem>
      <Description>
        Inventory duplication creates shallow copies with empty items arrays,
        breaking the entire BEO rendering chain.
      </Description>
      
      <Symptoms>
        <Symptom>Empty items array in duplicated combinations</Symptom>
        <Symptom>Missing BEO content for Event Experience packages</Symptom>
        <Symptom>Broken choice options in menu rendering</Symptom>
        <Symptom>Category filtering failures</Symptom>
      </Symptoms>

      <RootCause>
        Duplication process copies top-level object but doesn't recursively
        duplicate referenced inventory_billable_groups, leaving broken references.
      </RootCause>
    </ShallowCopyProblem>

    <OrphanedReferences>
      <Description>
        References to non-existent or incorrectly duplicated inventory groups
        cause cascading failures throughout the system.
      </Description>
    </OrphanedReferences>
  </CommonIssues>

  <RepairGuidelines>
    <AssessmentFirst>
      <Rule>Always run comprehensive assessment before attempting repairs</Rule>
      <Script>Use 03_Assessment_Script.js for damage evaluation</Script>
      <Validation>Identify broken combinations, missing groups, orphaned references</Validation>
    </AssessmentFirst>

    <RepairProcess>
      <Step1>Identify original source combination (without (*))</Step1>
      <Step2>Extract items array structure from original</Step2>
      <Step3>Deep duplicate all referenced inventory_billable_groups</Step3>
      <Step4>Update broken combination with new group references</Step4>
      <Step5>Validate complete structure and test BEO generation</Step5>
    </RepairProcess>

    <ValidationCriteria>
      <DataIntegrity>Non-empty items arrays, valid references, complete nesting</DataIntegrity>
      <BEOGeneration>Successful rendering with all choices and components</BEOGeneration>
      <CategoryFiltering>Proper assignment to Food vs Alcohol BEOs</CategoryFiltering>
    </ValidationCriteria>
  </RepairGuidelines>

  <DatabaseOperations>
    <BrowserConsoleAPI>
      <GetById>databaseConnection.obj.getById(type, id, callback)</GetById>
      <GetWhere>databaseConnection.obj.getWhere(type, conditions, callback)</GetWhere>
      <Create>databaseConnection.obj.create(type, data, callback)</Create>
      <Update>databaseConnection.obj.update(type, dataWithId, callback)</Update>
      <Note>Update requires data object to include id field</Note>
    </BrowserConsoleAPI>

    <SafePatterns>
      <CloneFirst>Always _.clone() objects before modification</CloneFirst>
      <AddDelays>Use 100-200ms delays between batch operations</AddDelays>
      <ErrorHandling>Check for null/undefined before processing</ErrorHandling>
      <Validation>Verify results after each operation</Validation>
    </SafePatterns>

    <DeepDuplicationPattern>
      <![CDATA[
      async function deepDuplicateGroup(originalId, mappings = new Map()) {
        if (mappings.has(originalId)) return mappings.get(originalId);
        
        const original = await getGroup(originalId);
        const duplicateName = original.name + ' (*)';
        
        // Check if duplicate exists
        const existing = await findGroupByName(duplicateName);
        if (existing) {
          mappings.set(originalId, existing.id);
          return existing.id;
        }
        
        // Recursively duplicate nested references first
        const updatedItems = await processNestedItems(original.items, mappings);
        
        // Create new group
        const newGroup = {
          ...original,
          name: duplicateName,
          data_source_id: originalId,
          items: updatedItems
        };
        delete newGroup.id;
        
        const created = await createGroup(newGroup);
        mappings.set(originalId, created.id);
        return created.id;
      }
      ]]>
    </DeepDuplicationPattern>
  </DatabaseOperations>

  <TestingProtocols>
    <RepairValidation>
      <StructureTest>Verify items arrays are populated correctly</StructureTest>
      <ReferenceTest>Confirm all inventory_group IDs are valid</ReferenceTest>
      <BEOTest>Generate actual BEO and verify content appears</BEOTest>
      <CategoryTest>Test filtering with appropriate category IDs</CategoryTest>
    </RepairValidation>

    <BEOGenerationTest>
      <![CDATA[
      // Test BEO generation for repaired item
      function testBEOGeneration(combinationId) {
        // Create test menu with combination
        // Call appropriate BEO merge tag
        // Verify content renders correctly
        // Check for choice options and nested items
      }
      ]]>
    </BEOGenerationTest>
  </TestingProtocols>

  <PreventionStrategies>
    <ProperDuplication>
      <Rule>Always perform deep recursive duplication</Rule>
      <Process>Duplicate referenced groups before updating parent references</Process>
      <Validation>Verify complete structure before considering duplication complete</Validation>
    </ProperDuplication>

    <DataIntegrityChecks>
      <EmptyArrayCheck>Alert if items arrays are empty after duplication</EmptyArrayCheck>
      <ReferenceValidation>Verify all inventory_group references exist</ReferenceValidation>
      <BEOTesting>Test BEO generation as part of duplication process</BEOTesting>
    </DataIntegrityChecks>
  </PreventionStrategies>

  <InstanceSpecificConsiderations>
    <Infinity>
      <BEOType>generateInfinityBEOMergeTag</BEOType>
      <Features>Complex nested rendering, serving styles, quantities</Features>
      <MostAffected>Deep nesting makes this most vulnerable to broken duplications</MostAffected>
    </Infinity>

    <DreamCatering>
      <BEOType>generateDreamBEOMergeTag</BEOType>
      <Features>Custom formatting, different choice handling</Features>
    </DreamCatering>

    <Standard>
      <BEOType>generateBEOMergeTag</BEOType>
      <Features>Basic rendering, still requires complete structure</Features>
    </Standard>
  </InstanceSpecificConsiderations>

  <DocumentationReferences>
    <SystemAnalysis>01_System_Analysis_and_Assessment.md</SystemAnalysis>
    <DatabaseAPI>02_DatabaseConnection_API_Documentation.md</DatabaseAPI>
    <BEOIntegration>03_BEO_System_Integration_Analysis.md</BEOIntegration>
    <RepairStrategy>04_Repair_Strategy_and_Implementation_Guide.md</RepairStrategy>
    <AssessmentScript>03_Assessment_Script.js</AssessmentScript>
  </DocumentationReferences>

  <KeyPrinciples>
    <Principle>Understand the complete data flow before making changes</Principle>
    <Principle>Always assess damage scope before attempting repairs</Principle>
    <Principle>Perform deep duplication to maintain referential integrity</Principle>
    <Principle>Validate repairs through actual BEO generation testing</Principle>
    <Principle>Use conservative, incremental approach to minimize risk</Principle>
    <Principle>Document all changes and maintain audit trail</Principle>
  </KeyPrinciples>

  <EmergencyProcedures>
    <ImmediateAssessment>
      <![CDATA[
      // Quick damage assessment
      databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(all) {
        const broken = all.filter(rec => rec.name.includes('(*)') && (!rec.items || rec.items.length === 0));
        console.log(`CRITICAL: ${broken.length} broken combinations found`);
        broken.forEach(rec => console.log(`- ID ${rec.id}: ${rec.name}`));
      });
      ]]>
    </ImmediateAssessment>

    <QuickRepair>
      <Description>For urgent fixes, identify original and manually restore items array</Description>
      <Warning>Only use for emergency situations - full repair process is recommended</Warning>
    </QuickRepair>
  </EmergencyProcedures>

</BentoInventorySystem>
