// =================================================================================================
// NAMING PATTERN DIAGNOSIS - Understand the actual duplication patterns
// =================================================================================================

/**
 * Analyze the actual naming patterns to understand how duplication was done
 */
function diagnoseDuplicationPatterns() {
    console.log('🔍 DIAGNOSING DUPLICATION PATTERNS');
    console.log('='.repeat(60));
    
    databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombinations) {
        
        // Separate duplicates and non-duplicates
        const duplicates = allCombinations.filter(rec => rec.name.includes('(*)'));
        const nonDuplicates = allCombinations.filter(rec => !rec.name.includes('(*)'));
        
        console.log(`📊 DATASET OVERVIEW:`);
        console.log(`   Total combinations: ${allCombinations.length}`);
        console.log(`   With (*): ${duplicates.length}`);
        console.log(`   Without (*): ${nonDuplicates.length}`);
        
        // Check if originals exist by trying different patterns
        console.log(`\n🔍 TESTING NAMING PATTERNS:`);
        
        let patternResults = {
            exactMatch: 0,
            partialMatch: 0,
            noMatch: 0,
            examples: []
        };
        
        // Test first 20 duplicates
        duplicates.slice(0, 20).forEach(dup => {
            const baseName = dup.name.replace(' (*)', '');
            const exactMatch = nonDuplicates.find(orig => orig.name === baseName);
            const partialMatch = nonDuplicates.find(orig => 
                orig.name.includes(baseName.split(' ').slice(0, 3).join(' '))
            );
            
            if (exactMatch) {
                patternResults.exactMatch++;
                if (patternResults.examples.length < 3) {
                    patternResults.examples.push({
                        type: 'exact',
                        duplicate: dup.name,
                        original: exactMatch.name,
                        dupItems: dup.items?.length || 0,
                        origItems: exactMatch.items?.length || 0
                    });
                }
            } else if (partialMatch) {
                patternResults.partialMatch++;
                if (patternResults.examples.length < 5) {
                    patternResults.examples.push({
                        type: 'partial',
                        duplicate: dup.name,
                        original: partialMatch.name,
                        dupItems: dup.items?.length || 0,
                        origItems: partialMatch.items?.length || 0
                    });
                }
            } else {
                patternResults.noMatch++;
                if (patternResults.examples.length < 5) {
                    patternResults.examples.push({
                        type: 'none',
                        duplicate: dup.name,
                        original: 'NOT FOUND',
                        dupItems: dup.items?.length || 0,
                        origItems: 0
                    });
                }
            }
        });
        
        console.log(`\n📋 PATTERN ANALYSIS RESULTS:`);
        console.log(`   Exact matches found: ${patternResults.exactMatch}/20`);
        console.log(`   Partial matches found: ${patternResults.partialMatch}/20`);
        console.log(`   No matches found: ${patternResults.noMatch}/20`);
        
        console.log(`\n📝 EXAMPLES:`);
        patternResults.examples.forEach((example, i) => {
            console.log(`\n${i + 1}. ${example.type.toUpperCase()} MATCH:`);
            console.log(`   Duplicate: "${example.duplicate}" (${example.dupItems} items)`);
            console.log(`   Original: "${example.original}" (${example.origItems} items)`);
        });
        
        // Check for different theories
        console.log(`\n🤔 POSSIBLE SCENARIOS:`);
        
        if (patternResults.exactMatch === 0 && patternResults.partialMatch === 0) {
            console.log(`❗ THEORY 1: All originals were replaced/deleted during duplication`);
            console.log(`❗ THEORY 2: Duplication used different naming pattern`);
            console.log(`❗ THEORY 3: Originals exist but with different names`);
            
            console.log(`\n🔍 INVESTIGATING FURTHER...`);
            investigateDuplicationTheories(duplicates, nonDuplicates);
        } else {
            console.log(`✅ Some originals found - duplication pattern is partially working`);
            if (patternResults.exactMatch > 0) {
                console.log(`💡 Use exact matches for repair testing`);
            }
        }
    });
}

/**
 * Investigate different theories about what happened during duplication
 */
function investigateDuplicationTheories(duplicates, nonDuplicates) {
    console.log(`\n🕵️ INVESTIGATING DUPLICATION THEORIES...`);
    
    // Theory 1: Check date patterns - were originals created then deleted?
    const dupDates = duplicates.map(d => new Date(d.date_created));
    const nonDupDates = nonDuplicates.map(d => new Date(d.date_created));
    
    console.log(`\n📅 DATE ANALYSIS:`);
    console.log(`   Duplicates date range: ${Math.min(...dupDates).toDateString()} to ${Math.max(...dupDates).toDateString()}`);
    console.log(`   Non-duplicates date range: ${Math.min(...nonDupDates).toDateString()} to ${Math.max(...nonDupDates).toDateString()}`);
    
    // Theory 2: Check if data_source_id points to anything useful
    const duplicatesWithDataSource = duplicates.filter(d => d.data_source_id && d.data_source_id > 0);
    console.log(`\n🔗 DATA SOURCE ANALYSIS:`);
    console.log(`   Duplicates with data_source_id: ${duplicatesWithDataSource.length}/${duplicates.length}`);
    
    if (duplicatesWithDataSource.length > 0) {
        console.log(`💡 Some duplicates have data_source_id - checking if originals exist...`);
        
        // Check if data_source_id points to existing records
        let sourceIdTests = 0;
        duplicatesWithDataSource.slice(0, 5).forEach(dup => {
            const sourceId = dup.data_source_id;
            const potentialOriginal = nonDuplicates.find(orig => orig.id === sourceId);
            
            if (potentialOriginal) {
                sourceIdTests++;
                console.log(`   ✅ Source ID ${sourceId} exists: "${potentialOriginal.name}" (${potentialOriginal.items?.length || 0} items)`);
            } else {
                console.log(`   ❌ Source ID ${sourceId} not found in non-duplicates`);
            }
        });
        
        if (sourceIdTests > 0) {
            console.log(`\n🎯 BREAKTHROUGH: data_source_id points to existing records!`);
            console.log(`🚀 NEXT STEP: findViableDataSourcePairs()`);
        }
    }
    
    // Theory 3: Check for working duplicates to understand the pattern
    const workingDuplicates = duplicates.filter(d => d.items && d.items.length > 0);
    console.log(`\n✅ WORKING DUPLICATES: ${workingDuplicates.length}/${duplicates.length}`);
    
    if (workingDuplicates.length > 0) {
        console.log(`💡 Some duplicates ARE working - let's analyze them...`);
        console.log(`🚀 NEXT STEP: analyzeWorkingDuplicates()`);
    }
}

/**
 * Find pairs using data_source_id
 */
function findViableDataSourcePairs() {
    console.log(`🔍 FINDING VIABLE PAIRS USING DATA_SOURCE_ID`);
    
    databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombinations) {
        const duplicates = allCombinations.filter(rec => rec.name.includes('(*)'));
        const nonDuplicates = allCombinations.filter(rec => !rec.name.includes('(*)'));
        
        // Find broken duplicates with data_source_id
        const brokenWithSource = duplicates.filter(d => 
            (!d.items || d.items.length === 0) && d.data_source_id && d.data_source_id > 0
        );
        
        console.log(`🎯 Found ${brokenWithSource.length} broken duplicates with data_source_id`);
        
        let viablePairs = [];
        
        brokenWithSource.slice(0, 10).forEach(broken => {
            const sourceId = broken.data_source_id;
            const original = allCombinations.find(orig => orig.id === sourceId);
            
            if (original && original.items && original.items.length > 0) {
                viablePairs.push({
                    brokenId: broken.id,
                    brokenName: broken.name,
                    originalId: original.id,
                    originalName: original.name,
                    originalItems: original.items.length
                });
            }
        });
        
        if (viablePairs.length > 0) {
            console.log(`\n✅ FOUND ${viablePairs.length} VIABLE REPAIR PAIRS:`);
            viablePairs.forEach((pair, i) => {
                console.log(`\n${i + 1}. Broken: ID ${pair.brokenId} - "${pair.brokenName}"`);
                console.log(`   Original: ID ${pair.originalId} - "${pair.originalName}" (${pair.originalItems} items)`);
            });
            
            console.log(`\n🏆 RECOMMENDED TEST CASE:`);
            const best = viablePairs[0];
            console.log(`🚀 testRepairCandidate(${best.brokenId}, ${best.originalId})`);
        } else {
            console.log(`❌ No viable pairs found via data_source_id`);
        }
    });
}

/**
 * Analyze working duplicates to understand the pattern
 */
function analyzeWorkingDuplicates() {
    console.log(`🔍 ANALYZING WORKING DUPLICATES`);
    
    databaseConnection.obj.getWhere('inventory_billable_combinations', {}, function(allCombinations) {
        const workingDuplicates = allCombinations.filter(rec => 
            rec.name.includes('(*)') && rec.items && rec.items.length > 0
        );
        
        console.log(`📊 Found ${workingDuplicates.length} working duplicates`);
        
        // Analyze first few working duplicates
        workingDuplicates.slice(0, 5).forEach((dup, i) => {
            console.log(`\n${i + 1}. Working Duplicate Analysis:`);
            console.log(`   ID: ${dup.id}`);
            console.log(`   Name: "${dup.name}"`);
            console.log(`   Items: ${dup.items.length}`);
            console.log(`   Data source ID: ${dup.data_source_id || 'None'}`);
            console.log(`   Category: ${dup.category}`);
        });
        
        console.log(`\n💡 Working duplicates can help us understand successful duplication pattern`);
    });
}

console.log('✅ DUPLICATION PATTERN DIAGNOSIS LOADED');
console.log('📋 Available commands:');
console.log('   diagnoseDuplicationPatterns() - Analyze naming patterns');
console.log('   findViableDataSourcePairs() - Find pairs using data_source_id');
console.log('   analyzeWorkingDuplicates() - Study successful duplicates');
console.log('');
console.log('🚀 START HERE: diagnoseDuplicationPatterns()');
