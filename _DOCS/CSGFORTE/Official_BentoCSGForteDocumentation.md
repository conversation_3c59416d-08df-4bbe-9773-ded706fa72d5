## **How to Use CSGForteService**

### **Service Call Pattern**
All methods follow the standard Bento service pattern:

```javascript
sb.data.db.service(
    "CSGForteService",    // Service name
    "methodName",         // Method to call
    requestData,          // Data to send
    function(response) {  // Callback function
        // Handle response
    }
);
```

## **Available Methods & Their Purpose**

### **1. `testConnection($request)` - API Connectivity Test**

**Purpose:** Verify that your CSG Forte credentials work and the API is accessible.

**When to Use:**
- During development setup
- Troubleshooting connection issues
- Verifying environment configuration

**Frontend Call:**
```javascript
sb.data.db.service(
    "CSGForteService",
    "testConnection",
    {}, // No parameters needed
    function(response) {
        if (response.success) {
            console.log('✅ CSG Forte connected:', response.message);
            console.log('Environment:', response.environment);
            console.log('Test Mode:', response.test_mode);
        } else {
            console.log('❌ Connection failed:', response.message);
        }
    }
);
```

**Response:**
```javascript
{
    success: true/false,
    environment: "sandbox" or "production",
    test_mode: true/false,
    api_access_id: "8adfb585f7a7cde0168cbebc52ccc1b2",
    location_id: "401809",
    status_code: 200,
    message: "CSG Forte API connection successful",
    account_data: { /* API response */ }
}
```

---

### **2. `createPaymentButton($request)` - Secure Payment Button Generation**

**Purpose:** Generate secure payment button configuration with server-side signature generation.

**When to Use:**
- When user clicks "Pay with CSG Forte" button
- Before displaying payment form
- Each time you need a new payment session

**Frontend Call:**
```javascript
sb.data.db.service(
    "CSGForteService",
    "createPaymentButton",
    {
        totalAmount: 10000  // Amount in cents ($100.00)
    },
    function(response) {
        if (response.success) {
            // Use response.buttonData to create secure payment button
            createFortePaymentButton(response.buttonData);
        }
    }
);
```

**Response:**
```javascript
{
    success: true,
    test_mode: true,
    buttonData: {
        api_access_id: "8adfb585f7a7cde0168cbebc52ccc1b2",
        location_id: "401809",
        method: "sale",
        version_number: "2.0",
        total_amount: 10000,
        utc_time: "2025-01-26T15:30:45Z",
        order_number: "ORDER_1737907845_1234",
        hash_method: "sha256",
        signature: "abc123...", // Server-generated secure signature
        callback: "onForteCallback",
        environment: "sandbox",
        script_url: "https://sandbox.forte.net/checkout/v2/js"
    }
}
```

---

### **3. `verifyCallbackSignature($request)` - Webhook Security Verification**

**Purpose:** Verify that payment callbacks from CSG Forte are authentic and haven't been tampered with.

**When to Use:**
- When CSG Forte sends payment completion callback
- Before processing any payment data
- Security validation step

**Frontend Call:**
```javascript
// Called automatically when CSG Forte callback occurs
function onForteCallback(callbackData) {
    sb.data.db.service(
        "CSGForteService",
        "verifyCallbackSignature",
        {
            callbackData: callbackData
        },
        function(response) {
            if (response.success) {
                // Signature valid - proceed with payment processing
                processPayment(callbackData);
            } else {
                // Invalid signature - reject payment
                console.error('Invalid payment signature');
            }
        }
    );
}
```

**Response:**
```javascript
{
    success: true/false,
    expected_signature: "abc123...",
    received_signature: "abc123..."
}
```

---

### **4. `processPaymentCallback($request)` - Payment Processing**

**Purpose:** Handle successful payment processing including database updates and notifications.

**When to Use:**
- After signature verification passes
- To create payment records in database
- To update invoice balances
- To send confirmation emails

**Frontend Call:**
```javascript
sb.data.db.service(
    "CSGForteService",
    "processPaymentCallback",
    {
        callbackData: callbackData,
        proposalId: 12345,
        invoiceIds: [67890, 67891],
        // ... other payment details
    },
    function(response) {
        if (response.success) {
            // Payment processed successfully
            showSuccessMessage();
            redirectToConfirmation();
        }
    }
);
```

**Note:** This method currently has placeholder logic. You'll need to implement the actual payment processing similar to iCheckGatewayService.

---

### **5. `getServiceConfig($request)` - Frontend Configuration**

**Purpose:** Get service configuration for frontend initialization.

**When to Use:**
- During page load
- To determine which environment you're in
- To get correct script URLs

**Frontend Call:**
```javascript
sb.data.db.service(
    "CSGForteService",
    "getServiceConfig",
    {},
    function(response) {
        // Load appropriate CSG Forte script
        loadScript(response.script_url);

        // Configure frontend based on environment
        if (response.test_mode) {
            console.log('Running in sandbox mode');
        }
    }
);
```

**Response:**
```javascript
{
    success: true,
    environment: "sandbox",
    test_mode: true,
    script_url: "https://sandbox.forte.net/checkout/v2/js"
}
```

## **Typical Payment Flow**

1. **Initialize:** Call `getServiceConfig()` to set up frontend
2. **Test Connection:** Call `testConnection()` during development
3. **Create Payment:** Call `createPaymentButton()` when user wants to pay
4. **Process Payment:** CSG Forte calls your callback → `verifyCallbackSignature()` → `processPaymentCallback()`

## **Security Features**

- ✅ **Server-side signature generation** - No secret keys in frontend
- ✅ **Callback verification** - Prevents payment tampering
- ✅ **Environment detection** - Auto-switches between sandbox/production
- ✅ **Secure credential management** - Uses environment variables

