# CSG Forte Performance Metrics Documentation

## Overview
Performance metrics have been added to track the complete CSG Forte payment flow from button click to payment completion. Metrics are logged to **browser console** (frontend) and **Pipedream** (backend).

---

## Frontend Metrics (JavaScript - Browser Console)

### Location
`_SRC/notify/_components/_core/_fields/contact-payment-sources.js`

### Metrics Tracked

#### 1. **Inline Pay Now Button Click**
- **Metric**: `metricInlineStart`
- **Logged**: When user clicks inline "Pay Now" button
- **Console Output**: `⏱️ [METRIC] Inline Pay Now clicked at: {timestamp}`

#### 2. **Custom Pay Now Button Click**
- **Metric**: `metricCustomStart`
- **Logged**: When user clicks custom amount "Pay Now" button
- **Console Output**: `⏱️ [METRIC] Custom Pay Now clicked at: {timestamp}`

#### 3. **Service Response Received**
- **Metric**: `metricServiceResponse`
- **Duration**: Time from button click to `createPaymentButton` response
- **Console Output**: `⏱️ [METRIC] Service response received in: {duration} ms`

#### 4. **Forte Script Load**
- **Metric**: `metricScriptLoadStart` → `metricScriptLoaded`
- **Duration**: Time to load CSG Forte JavaScript SDK
- **Console Output**: 
  - `⏱️ [METRIC] Starting Forte script load at: {timestamp}`
  - `⏱️ [METRIC] Forte script loaded in: {duration} ms`
  - `⏱️ [METRIC] Script already cached, took: {duration} ms` (if cached)

#### 5. **Modal Launch**
- **Metric**: `metricModalLaunchStart`
- **Logged**: When modal launch begins
- **Console Output**: `⏱️ [METRIC] Starting modal launch at: {timestamp}`

#### 6. **Modal Displayed**
- **Metric**: `metricModalDisplayed`
- **Logged**: When CSG Forte modal opens (begin event)
- **Console Output**: `⏱️ [METRIC] Modal displayed at: {timestamp}`

#### 7. **Payment Callback Received**
- **Metric**: `metricCallbackReceived`
- **Logged**: When CSG Forte returns payment success callback
- **Console Output**: `⏱️ [METRIC] Payment callback received at: {timestamp}`

#### 8. **Backend Processing**
- **Metric**: `metricBackendProcessStart` → `metricBackendProcessEnd`
- **Duration**: Time for `processCSGFortePayment` to complete
- **Console Output**:
  - `⏱️ [METRIC] Starting backend processing at: {timestamp}`
  - `⏱️ [METRIC] Backend processing completed in: {duration} ms`

---

## Backend Metrics (PHP - Pipedream)

### Location
`_SRC/pagoda/services/CSGForteService.php`

### Pipedream Endpoint
`https://eomvpn0zjsy8add.m.pipedream.net`

### Metrics Tracked

#### 1. **createPaymentButton() - Function Start**
- **Event**: `METRIC_FUNCTION_START`
- **Data**:
  ```php
  [
    'function' => 'createPaymentButton',
    'timestamp' => microtime(true),
    'request_size' => strlen(json_encode($request))
  ]
  ```

#### 2. **UTC Time Fetch**
- **Event**: `METRIC_UTC_TIME_FETCH`
- **Duration**: Time to fetch UTC from CSG Forte API
- **Data**:
  ```php
  [
    'duration_ms' => {milliseconds},
    'utc_time' => {utc_ticks}
  ]
  ```

#### 3. **Signature Generation**
- **Event**: `METRIC_SIGNATURE_GENERATION`
- **Duration**: Time to generate HMAC-SHA256 signature
- **Data**:
  ```php
  [
    'duration_ms' => {milliseconds}
  ]
  ```

#### 4. **createPaymentButton() - Complete**
- **Event**: `METRIC_FUNCTION_COMPLETE`
- **Duration**: Total function execution time
- **Data**:
  ```php
  [
    'function' => 'createPaymentButton',
    'total_duration_ms' => {total_time},
    'utc_fetch_ms' => {utc_time},
    'signature_gen_ms' => {signature_time},
    'other_processing_ms' => {remaining_time}
  ]
  ```

#### 5. **processCSGFortePayment() - Start**
- **Event**: `CSG_FORTE_CALLBACK_RECEIVED`
- **Data**:
  ```php
  [
    'raw_callback' => {callback_data},
    'callback_type' => gettype($csgForteCallback),
    'callback_keys' => array_keys($csgForteCallback),
    'timestamp' => microtime(true)
  ]
  ```

#### 6. **Allocation Engine Start**
- **Event**: `METRIC_ALLOCATION_ENGINE_START`
- **Data**:
  ```php
  [
    'timestamp' => microtime(true),
    'invoice_value_paid' => {amount_in_cents},
    'selected_invoices_count' => count($selectedInvoices),
    'unpaid_remaining_count' => count($unpaidRemaining)
  ]
  ```

#### 7. **processCSGFortePayment() - Complete**
- **Event**: `METRIC_PROCESS_COMPLETE`
- **Duration**: Total payment processing time
- **Data**:
  ```php
  [
    'total_duration_ms' => {total_time},
    'allocation_engine_ms' => {allocation_time},
    'pre_allocation_ms' => {setup_time}
  ]
  ```

---

## Complete Flow Timeline

```
USER ACTION: Click "Pay Now"
  ↓
[FRONTEND] metricInlineStart / metricCustomStart
  ↓
[FRONTEND] Service call to createPaymentButton
  ↓
[BACKEND] METRIC_FUNCTION_START
  ↓
[BACKEND] METRIC_UTC_TIME_FETCH (duration logged)
  ↓
[BACKEND] METRIC_SIGNATURE_GENERATION (duration logged)
  ↓
[BACKEND] METRIC_FUNCTION_COMPLETE (total duration logged)
  ↓
[FRONTEND] metricServiceResponse (duration from click)
  ↓
[FRONTEND] metricScriptLoadStart
  ↓
[FRONTEND] metricScriptLoaded (script load duration)
  ↓
[FRONTEND] metricModalLaunchStart
  ↓
[FRONTEND] metricModalDisplayed (begin event)
  ↓
USER ACTION: Complete payment in CSG modal
  ↓
[FRONTEND] metricCallbackReceived (success event)
  ↓
[FRONTEND] metricBackendProcessStart
  ↓
[BACKEND] CSG_FORTE_CALLBACK_RECEIVED
  ↓
[BACKEND] METRIC_ALLOCATION_ENGINE_START
  ↓
[BACKEND] Database operations (payment/invoice creation)
  ↓
[BACKEND] METRIC_PROCESS_COMPLETE (total processing duration)
  ↓
[FRONTEND] metricBackendProcessEnd (backend duration)
  ↓
SUCCESS: Payment complete
```

---

## How to Use These Metrics

### Identifying Bottlenecks

1. **Slow Button Response** (> 2 seconds)
   - Check: `METRIC_FUNCTION_COMPLETE.total_duration_ms`
   - Breakdown: `utc_fetch_ms` + `signature_gen_ms` + `other_processing_ms`
   - Common causes:
     - Slow UTC API call to CSG Forte
     - Network latency
     - PHP processing overhead

2. **Slow Script Load** (> 1 second)
   - Check: Frontend script load duration
   - Common causes:
     - CSG Forte CDN latency
     - Network bandwidth
     - Browser caching issues

3. **Slow Backend Processing** (> 3 seconds)
   - Check: `METRIC_PROCESS_COMPLETE.total_duration_ms`
   - Breakdown: `allocation_engine_ms` + `pre_allocation_ms`
   - Common causes:
     - Database query performance
     - Multiple invoice allocations
     - Email notification delays

### Example Console Output

```
⏱️ [METRIC] Inline Pay Now clicked at: 1234567890.123
⏱️ [METRIC] Service response received in: 1847.23 ms
⏱️ [METRIC] Starting Forte script load at: 1234567892.456
⏱️ [METRIC] Forte script loaded in: 523.45 ms
⏱️ [METRIC] Starting modal launch at: 1234567893.012
⏱️ [METRIC] Modal displayed at: 1234567893.234
⏱️ [METRIC] Payment callback received at: 1234567910.567
⏱️ [METRIC] Starting backend processing at: 1234567910.789
⏱️ [METRIC] Backend processing completed in: 2341.67 ms
```

### Example Pipedream Output

```json
{
  "timestamp": "2025-01-26 15:30:45",
  "stage": "METRIC_FUNCTION_COMPLETE",
  "service": "CSGForteService",
  "method": "createPaymentButton",
  "data": {
    "function": "createPaymentButton",
    "total_duration_ms": 1847.23,
    "utc_fetch_ms": 1234.56,
    "signature_gen_ms": 12.34,
    "other_processing_ms": 600.33
  }
}
```

---

## Performance Targets

### Acceptable Performance
- **Button Click → Service Response**: < 2 seconds
- **Script Load**: < 1 second
- **Modal Display**: < 500ms
- **Backend Processing**: < 3 seconds
- **Total End-to-End**: < 10 seconds

### Investigate If
- **Button Click → Service Response**: > 3 seconds
- **Script Load**: > 2 seconds
- **Backend Processing**: > 5 seconds
- **Total End-to-End**: > 15 seconds

---

## Next Steps

1. **Test both payment flows**:
   - Inline "Pay Now" button
   - Custom amount "Pay Now" button

2. **Monitor Pipedream** for backend metrics

3. **Check browser console** for frontend metrics

4. **Identify slowest operations** and propose optimizations

5. **Compare performance** across environments:
   - localhost (sandbox)
   - bento-dev (staging)
   - production

