# Pipedream Metric Events - CSG Forte

## Overview
Only **METRIC_*** events are sent to Pipedream for performance monitoring.

**Pipedream Endpoint**: `https://eomvpn0zjsy8add.m.pipedream.net`

---

## Events Sent to Pipedream

### 1. METRIC_FUNCTION_START
**When**: `createPaymentButton()` function entry
**Purpose**: Mark the start of payment button creation

```json
{
  "timestamp": "2025-01-26 15:30:45",
  "stage": "METRIC_FUNCTION_START",
  "service": "CSGForteService",
  "method": "createPaymentButton",
  "test_mode": true,
  "current_env": "development",
  "forte_environment": "sandbox",
  "context": {},
  "data": {
    "function": "createPaymentButton",
    "timestamp": 1706287845.123,
    "request_size": 156
  }
}
```

---

### 2. METRIC_UTC_TIME_FETCH
**When**: After fetching UTC time from CSG Forte API
**Purpose**: Measure UTC API call duration

```json
{
  "timestamp": "2025-01-26 15:30:46",
  "stage": "METRIC_UTC_TIME_FETCH",
  "service": "CSGForteService",
  "method": "createPaymentButton",
  "test_mode": true,
  "current_env": "development",
  "forte_environment": "sandbox",
  "context": {},
  "data": {
    "duration_ms": 1234.56,
    "utc_time": "638950297489690000"
  }
}
```

**Key Metric**: `duration_ms` - Time to fetch UTC from CSG Forte
**Target**: < 500ms
**Investigate if**: > 1000ms

---

### 3. METRIC_SIGNATURE_GENERATION
**When**: After generating HMAC-SHA256 signature
**Purpose**: Measure signature generation duration

```json
{
  "timestamp": "2025-01-26 15:30:46",
  "stage": "METRIC_SIGNATURE_GENERATION",
  "service": "CSGForteService",
  "method": "createPaymentButton",
  "test_mode": true,
  "current_env": "development",
  "forte_environment": "sandbox",
  "context": {},
  "data": {
    "duration_ms": 12.34
  }
}
```

**Key Metric**: `duration_ms` - Time to generate signature
**Target**: < 50ms
**Investigate if**: > 100ms

---

### 4. METRIC_FUNCTION_COMPLETE
**When**: `createPaymentButton()` function exit
**Purpose**: Measure total function duration with breakdown

```json
{
  "timestamp": "2025-01-26 15:30:47",
  "stage": "METRIC_FUNCTION_COMPLETE",
  "service": "CSGForteService",
  "method": "createPaymentButton",
  "test_mode": true,
  "current_env": "development",
  "forte_environment": "sandbox",
  "context": {},
  "data": {
    "function": "createPaymentButton",
    "total_duration_ms": 1847.23,
    "utc_fetch_ms": 1234.56,
    "signature_gen_ms": 12.34,
    "other_processing_ms": 600.33
  }
}
```

**Key Metrics**:
- `total_duration_ms` - Total function time
- `utc_fetch_ms` - UTC API call time
- `signature_gen_ms` - Signature generation time
- `other_processing_ms` - Remaining processing time

**Target**: `total_duration_ms` < 2000ms
**Investigate if**: `total_duration_ms` > 3000ms

---

### 5. METRIC_ALLOCATION_ENGINE_START
**When**: Before payment allocation engine executes
**Purpose**: Mark start of payment processing

```json
{
  "timestamp": "2025-01-26 15:31:10",
  "stage": "METRIC_ALLOCATION_ENGINE_START",
  "service": "CSGForteService",
  "method": "processCSGFortePayment",
  "test_mode": true,
  "current_env": "development",
  "forte_environment": "sandbox",
  "context": {},
  "data": {
    "timestamp": 1706287870.456,
    "invoice_value_paid": 5000,
    "selected_invoices_count": 1,
    "unpaid_remaining_count": 0
  }
}
```

**Key Metrics**:
- `invoice_value_paid` - Amount being processed (cents)
- `selected_invoices_count` - Number of invoices to update
- `unpaid_remaining_count` - Number of remaining unpaid invoices

---

### 6. METRIC_PROCESS_COMPLETE
**When**: `processCSGFortePayment()` function exit
**Purpose**: Measure total payment processing duration

```json
{
  "timestamp": "2025-01-26 15:31:14",
  "stage": "METRIC_PROCESS_COMPLETE",
  "service": "CSGForteService",
  "method": "processCSGFortePayment",
  "test_mode": true,
  "current_env": "development",
  "forte_environment": "sandbox",
  "context": {},
  "data": {
    "total_duration_ms": 4135.30,
    "allocation_engine_ms": 3456.78,
    "pre_allocation_ms": 678.52
  }
}
```

**Key Metrics**:
- `total_duration_ms` - Total processing time
- `allocation_engine_ms` - Database operations time
- `pre_allocation_ms` - Setup/validation time

**Target**: `total_duration_ms` < 3000ms
**Investigate if**: `total_duration_ms` > 5000ms

---

## Events NOT Sent to Pipedream

The following debug events are **NOT** sent to Pipedream (filtered out):

- `SIGNATURE_GENERATION_START`
- `SIGNATURE_GENERATION_COMPLETE`
- `UTC_TIME_REQUEST_START`
- `UTC_TIME_URL_SELECTED`
- `UTC_TIME_CURL_RESPONSE`
- `UTC_TIME_EXTRACTION`
- `UTC_TIME_API_FAILED`
- `UTC_TIME_FALLBACK_GENERATED`
- `CREDENTIAL_VERIFICATION`
- `SIGNATURE_STRING_COMPONENTS`
- `SIGNATURE_STRING_BUILT`
- `CSG_FORTE_CALLBACK_RECEIVED`
- `CSG_FORTE_ERROR`
- `PAYMENT_CREATE_BEFORE`
- `PAYMENT_CREATE_AFTER`
- `INVOICE_UPDATE_BEFORE`
- `INVOICE_UPDATE_AFTER`
- `PROCESS_SUCCESS`

These events are still logged via `error_log()` for PHP error logs but not sent to Pipedream.

---

## Complete Flow Timeline

```
[PIPEDREAM] METRIC_FUNCTION_START
    ↓
[PIPEDREAM] METRIC_UTC_TIME_FETCH (duration logged)
    ↓
[PIPEDREAM] METRIC_SIGNATURE_GENERATION (duration logged)
    ↓
[PIPEDREAM] METRIC_FUNCTION_COMPLETE (total + breakdown)
    ↓
... User completes payment in CSG modal ...
    ↓
[PIPEDREAM] METRIC_ALLOCATION_ENGINE_START
    ↓
... Database operations ...
    ↓
[PIPEDREAM] METRIC_PROCESS_COMPLETE (total + breakdown)
```

---

## How to Analyze Pipedream Logs

### 1. Check createPaymentButton Performance

Look for `METRIC_FUNCTION_COMPLETE`:
```json
{
  "stage": "METRIC_FUNCTION_COMPLETE",
  "data": {
    "total_duration_ms": 1847.23,
    "utc_fetch_ms": 1234.56,      // <-- Is this > 1000ms?
    "signature_gen_ms": 12.34,     // <-- Should be < 50ms
    "other_processing_ms": 600.33  // <-- Should be < 500ms
  }
}
```

**If `total_duration_ms` > 3000ms**:
- Check `utc_fetch_ms` - UTC API is slow
- Check `other_processing_ms` - PHP processing overhead

### 2. Check processCSGFortePayment Performance

Look for `METRIC_PROCESS_COMPLETE`:
```json
{
  "stage": "METRIC_PROCESS_COMPLETE",
  "data": {
    "total_duration_ms": 4135.30,
    "allocation_engine_ms": 3456.78,  // <-- Database operations
    "pre_allocation_ms": 678.52       // <-- Setup time
  }
}
```

**If `total_duration_ms` > 5000ms**:
- Check `allocation_engine_ms` - Database queries are slow
- Check `pre_allocation_ms` - Setup/validation overhead

### 3. Compare Environments

Compare metrics across environments:
- **localhost** (sandbox) - May be slower
- **bento-dev** (staging) - Should be faster
- **production** - Should be fastest

---

## Expected Pipedream Output

For a single payment transaction, you should see **6 events**:

1. `METRIC_FUNCTION_START`
2. `METRIC_UTC_TIME_FETCH`
3. `METRIC_SIGNATURE_GENERATION`
4. `METRIC_FUNCTION_COMPLETE`
5. `METRIC_ALLOCATION_ENGINE_START`
6. `METRIC_PROCESS_COMPLETE`

**Total Events**: 6 per payment
**Total Payload Size**: ~2-3KB per payment

---

## Troubleshooting

### No Events in Pipedream
- Check Pipedream endpoint is accessible
- Check `httpPost()` method is working
- Check PHP error logs for curl errors

### Missing Events
- Check event name starts with `METRIC_`
- Check `sendDebugLog()` is being called
- Check no exceptions are thrown before metric logging

### Duplicate Events
- Check if multiple payment attempts are being made
- Check if page is being refreshed during payment

---

## Next Steps

1. **Test a payment** (inline or custom)
2. **Check Pipedream** for the 6 metric events
3. **Analyze durations** to identify bottlenecks
4. **Compare environments** to see if sandbox is slower
5. **Report findings** with specific duration values

