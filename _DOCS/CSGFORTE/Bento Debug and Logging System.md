# CSG Forte Debug & Logging System

## Overview
Comprehensive debug logging system using Pipedream service and mailspons email debugging for real-time monitoring of CSG Forte payment processing in dockerized development environment.

## Current Implementation Status

### ✅ **CONFIGURED & READY**
- **Pipedream URL**: `https://eo9hb9w1dkixb3e.m.pipedream.net`
- **Email Debug**: `<EMAIL>`
- **Debug Methods**: Documented and ready for integration
- **Integration Points**: Identified in Phase 2 implementation

### 📋 **TO BE IMPLEMENTED**
- Debug methods in CSGForteService.php
- Frontend debug calls in callback handler
- Email debug system activation

## Debug System Architecture

### **Dual Monitoring Approach**
1. **Real-time Logging**: Pipedream for immediate visibility
2. **Email Notifications**: Mailspons for persistent debugging and alerts

### **Debug Levels**
- **Step-by-step**: Payment processing flow tracking
- **Error Alerts**: Immediate notification of failures
- **Email Copies**: All customer/manager notifications copied to debug inbox
- **Flow Analysis**: Complete payment flow with all data points

## Implementation Specifications

### **Backend Debug Methods**

#### **File: `_SRC/pagoda/services/CSGForteService.php`**

```php
/**
 * Send debug data to Pipedream for real-time monitoring
 */
private function sendDebugLog($stage, $data, $context = []) {
    if (!$this->testMode) return;
    
    $debugData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'stage' => $stage,
        'service' => 'CSGForteService',
        'method' => debug_backtrace()[1]['function'] ?? 'unknown',
        'test_mode' => $this->testMode,
        'context' => $context,
        'data' => $data
    ];
    
    $this->httpPost("https://eo9hb9w1dkixb3e.m.pipedream.net", [
        'debug_data' => json_encode($debugData, JSON_PRETTY_PRINT)
    ]);
}

/**
 * Send debug emails to developer inbox with labels
 */
private function sendDebugEmail($subject, $body, $labels = []) {
    if (!$this->testMode) return;
    
    // Create labeled email: e3d1576329f340ebaca8+**Label1**+**Label2**@mailspons.com
    $baseEmail = 'e3d1576329f340ebaca8';
    $labelString = '';
    if (!empty($labels)) {
        $labelString = '+' . implode('+', array_map(function($label) {
            return '**' . $label . '**';
        }, $labels));
    }
    $debugEmail = $baseEmail . $labelString . '@mailspons.com';
    
    $mergevars = (object) array(
        'TITLE' => 'CSG Forte Debug',
        'SUBJECT' => '[DEBUG] ' . $subject,
        'BODY' => $body,
        'INSTANCE_NAME' => 'Bento Development'
    );
    
    $this->sb->sendEmail($debugEmail, '<EMAIL>', '[DEBUG] ' . $subject, $mergevars, 'CSG Forte Debug', false);
}

/**
 * HTTP POST helper for Pipedream logging
 */
private function httpPost($url, $data) {
    $curl = curl_init($url);
    curl_setopt($curl, CURLOPT_POST, true);
    curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_TIMEOUT, 5);
    $response = curl_exec($curl);
    curl_close($curl);
    return $response;
}
```

### **Integration Points in Phase 2**

#### **Enhanced `processCSGFortePayment()` with Debug Logging**

```php
public function processCSGFortePayment($csgForteCallback) {
    // Log callback receipt
    $this->sendDebugLog('CALLBACK_RECEIVED', [
        'response_code' => $csgForteCallback['response_code'] ?? 'MISSING',
        'method_used' => $csgForteCallback['method_used'] ?? 'MISSING',
        'total_amount' => $csgForteCallback['total_amount'] ?? 'MISSING',
        'xdata_fields' => [
            'xdata_5' => $csgForteCallback['xdata_5'] ?? 'MISSING',
            'xdata_6' => $csgForteCallback['xdata_6'] ?? 'MISSING',
            'xdata_7' => $csgForteCallback['xdata_7'] ?? 'MISSING'
        ]
    ], ['step' => 'ENTRY']);

    try {
        // Parse callback
        $this->sendDebugLog('PARSING_START', [], ['step' => 'PARSE_CALLBACK']);
        $callbackData = $this->parseCSGForteCallback($csgForteCallback);
        $this->sendDebugLog('PARSING_SUCCESS', [
            'transaction_id' => $callbackData['transaction_id'],
            'payment_type' => $callbackData['payment_type'],
            'base_amount' => $callbackData['base_amount']
        ], ['step' => 'PARSE_SUCCESS']);

        // Extract payment data
        $paymentData = $this->extractPaymentData($callbackData);
        $this->sendDebugLog('PAYMENT_DATA_EXTRACTED', $paymentData, ['step' => 'PAYMENT_DATA']);

        // Determine payment type
        $paymentType = $this->determinePaymentType($callbackData);
        $this->sendDebugLog('PAYMENT_TYPE_DETERMINED', $paymentType, ['step' => 'PAYMENT_TYPE']);

        // Execute allocation engine
        $this->sendDebugLog('ALLOCATION_START', [], ['step' => 'EXECUTE_ENGINE']);
        $allocationResult = $this->executePaymentAllocationEngine($engineInput);
        $this->sendDebugLog('ALLOCATION_SUCCESS', [
            'payments_created' => count($allocationResult['payments']),
            'invoices_updated' => count($allocationResult['invoices'])
        ], ['step' => 'ALLOCATION_COMPLETE']);

        // Send success email summary
        $this->sendPaymentFlowEmail($callbackData, $allocationResult, $engineInput);

        return $this->sb->sendData([
            'success' => true,
            'paymentType' => $paymentType,
            'processedPayments' => $allocationResult['payments'],
            'updatedInvoices' => $allocationResult['invoices']
        ], 1);

    } catch (Exception $e) {
        // Send error email with full details
        $this->sendErrorEmail($e, $csgForteCallback, 'PAYMENT_PROCESSING');
        
        $this->sendDebugLog('ERROR_OCCURRED', [
            'exception_message' => $e->getMessage(),
            'exception_file' => $e->getFile(),
            'exception_line' => $e->getLine()
        ], ['step' => 'EXCEPTION']);

        return $this->sb->sendData($e, 0);
    }
}
```

#### **Payment Flow Summary Email**

```php
private function sendPaymentFlowEmail($callbackData, $allocationResult, $engineInput) {
    $totalPayments = count($allocationResult['payments']);
    $totalInvoices = count($allocationResult['invoices']);
    $paymentType = $engineInput['paymentType']['type'];
    
    $subject = "Payment Processed: {$paymentType} - {$totalPayments} payments, {$totalInvoices} invoices";
    
    $body = "
    <h2>CSG Forte Payment Processing Summary</h2>
    <hr>
    
    <h3>Transaction Details</h3>
    <table border='1' cellpadding='5'>
        <tr><td><strong>Transaction ID:</strong></td><td>{$callbackData['transaction_id']}</td></tr>
        <tr><td><strong>Payment Type:</strong></td><td>{$paymentType}</td></tr>
        <tr><td><strong>Method Used:</strong></td><td>{$callbackData['method_used']}</td></tr>
        <tr><td><strong>Total Amount:</strong></td><td>\$" . number_format($callbackData['total_amount_csg']/100, 2) . "</td></tr>
        <tr><td><strong>Base Amount:</strong></td><td>\$" . number_format($callbackData['base_amount']/100, 2) . "</td></tr>
        <tr><td><strong>Fee Amount:</strong></td><td>\$" . number_format($callbackData['fee_amount']/100, 2) . "</td></tr>
    </table>
    
    <h3>Processing Results</h3>
    <table border='1' cellpadding='5'>
        <tr><td><strong>Payments Created:</strong></td><td>{$totalPayments}</td></tr>
        <tr><td><strong>Invoices Updated:</strong></td><td>{$totalInvoices}</td></tr>
        <tr><td><strong>Project ID:</strong></td><td>{$engineInput['project']['id']}</td></tr>
    </table>
    ";
    
    $labels = ['CSGForte', $paymentType, 'PaymentFlow', 'Success'];
    $this->sendDebugEmail($subject, $body, $labels);
}
```

#### **Error Email Notifications**

```php
private function sendErrorEmail($exception, $callbackData, $stage) {
    $subject = "CSG Forte Error: {$stage} - {$exception->getMessage()}";
    
    $body = "
    <h2>CSG Forte Payment Processing Error</h2>
    <hr>
    
    <h3>Error Details</h3>
    <table border='1' cellpadding='5'>
        <tr><td><strong>Stage:</strong></td><td>{$stage}</td></tr>
        <tr><td><strong>Message:</strong></td><td>" . htmlspecialchars($exception->getMessage()) . "</td></tr>
        <tr><td><strong>File:</strong></td><td>{$exception->getFile()}</td></tr>
        <tr><td><strong>Line:</strong></td><td>{$exception->getLine()}</td></tr>
        <tr><td><strong>Timestamp:</strong></td><td>" . date('Y-m-d H:i:s') . "</td></tr>
    </table>
    
    <h3>Callback Data</h3>
    <pre>" . json_encode($callbackData, JSON_PRETTY_PRINT) . "</pre>
    ";
    
    $labels = ['CSGForte', 'ERROR', $stage, 'URGENT'];
    $this->sendDebugEmail($subject, $body, $labels);
}
```

### **Frontend Debug Integration**

#### **File: `_SRC/notify/_components/_core/_fields/contact-payment-sources.js`**

```javascript
// Frontend debug helper
function sendDebugToConsole(stage, data) {
    if (typeof sb !== 'undefined' && sb.data && sb.data.db && sb.data.db.service) {
        sb.data.db.service(
            'CSGForteService',
            'debugLog',
            {
                stage: stage,
                data: data,
                timestamp: new Date().toISOString(),
                source: 'frontend_callback'
            },
            function(result) {
                console.log('🔧 Debug sent to backend:', stage);
            },
            function(error) {
                console.log('❌ Debug send failed:', error);
            }
        );
    }
}

// Enhanced callback handler with debugging
window.oncallback = function(messageEvent) {
    console.log('=== CSG FORTE CALLBACK START ===');
    
    // Debug: Log callback receipt
    sendDebugToConsole('CALLBACK_RECEIVED', {
        hasData: !!(messageEvent && messageEvent.data)
    });
    
    var callbackData = JSON.parse(messageEvent.data);
    
    // Debug: Log parsed data
    sendDebugToConsole('CALLBACK_PARSED', {
        event: callbackData.event,
        response_code: callbackData.response_code,
        method_used: callbackData.method_used,
        xdata_fields: {
            xdata_1: callbackData.xdata_1,
            xdata_5: callbackData.xdata_5,
            xdata_6: callbackData.xdata_6,
            xdata_7: callbackData.xdata_7
        }
    });
    
    if (callbackData.response_code === 'A01') {
        sendDebugToConsole('PAYMENT_SUCCESS', {
            transaction_id: callbackData.trace_number,
            method_used: callbackData.method_used
        });
        
        // Call backend processing
        sb.data.db.service(
            'CSGForteService',
            'processCSGFortePayment', 
            callbackData,
            function(result) {
                sendDebugToConsole('BACKEND_PROCESSING_SUCCESS', {
                    success: result.success,
                    payments_count: result.processedPayments ? result.processedPayments.length : 0
                });
                
                // Handle success/error UI updates
            },
            function(error) {
                sendDebugToConsole('BACKEND_PROCESSING_ERROR', { error: error });
            }
        );
    } else {
        sendDebugToConsole('PAYMENT_FAILED', {
            response_code: callbackData.response_code,
            response_description: callbackData.response_description
        });
    }
};
```

#### **Backend Debug Service Method**

```php
/**
 * Debug logging endpoint for frontend
 */
public function debugLog($request) {
    if (!$this->testMode) {
        return $this->sb->sendData(['success' => false, 'message' => 'Debug only available in test mode'], 0);
    }
    
    $debugData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'source' => $request->source ?? 'unknown',
        'stage' => $request->stage ?? 'unknown',
        'data' => $request->data ?? []
    ];
    
    $this->httpPost("https://eo9hb9w1dkixb3e.m.pipedream.net", [
        'frontend_debug' => json_encode($debugData, JSON_PRETTY_PRINT)
    ]);
    
    return $this->sb->sendData(['success' => true], 1);
}
```

## Email Label Organization

### **Recommended Label Structure**

```php
// Payment Flow Labels
['CSGForte', 'PaymentFlow', 'Success', 'CreditCard']
['CSGForte', 'PaymentFlow', 'Success', 'ACH']

// Error Labels (Priority)
['CSGForte', 'ERROR', 'AllocationEngine', 'URGENT']
['Frontend', 'ServiceError', 'URGENT']

// Notification Copies
['CSGForte', 'ManagerNotification', 'infinity', 'Copy']
['CSGForte', 'CustomerReceipt', 'CreditCard', 'Copy']

// Engine Debug Labels
['CSGForte', 'Engine', 'Loop1', 'Allocation']
['CSGForte', 'Engine', 'Loop2', 'Remaining']
```

## Debug Benefits

### **Real-time Monitoring**
- **Pipedream Console**: Live payment processing visibility
- **Step-by-step Tracking**: Flow progression from callback to completion
- **Performance Monitoring**: Identify bottlenecks and timing issues

### **Error Management**
- **Immediate Alerts**: Urgent error emails with full context
- **Stack Traces**: Complete error details for debugging
- **Data Context**: Full callback data included in error reports

### **Development Support**
- **Email Testing**: All customer/manager emails copied to debug inbox
- **Flow Analysis**: Complete payment processing breakdown
- **Data Validation**: Verify xdata field integrity at each step

## Security & Performance

### **Test Mode Only**
- All debug logging only active in development/test mode
- Production deployments will not trigger debug systems
- No sensitive data logged (card numbers, bank details filtered)

### **Non-blocking Design**
- Quick timeouts (5 seconds) prevent debug from affecting payment processing
- Debug failures do not impact main payment flow
- Asynchronous logging to avoid performance impact

## Usage Examples

### **Backend Debug Calls**
```php
// Simple debug logging
$this->sendDebugLog('PAYMENT_START', ['amount' => $amount]);

// Detailed logging with context
$this->sendDebugLog('INVOICE_PROCESSING', $invoiceData, ['step' => 'ALLOCATION']);

// Email debug notification
$this->sendDebugEmail('Payment Success', $emailBody, ['CSGForte', 'Success']);
```

### **Frontend Debug Calls**
```javascript
// Log user interactions
sendDebugToConsole('BUTTON_CLICKED', { buttonType: 'csg_forte_custom' });

// Log API responses
sendDebugToConsole('SERVICE_RESPONSE', result);
```

## Implementation Checklist

### **Phase 2 Integration Tasks**
- [ ] Add debug methods to CSGForteService.php
- [ ] Integrate debug calls in processCSGFortePayment()
- [ ] Add error email notifications
- [ ] Add payment flow summary emails
- [ ] Enhance frontend callback with debug calls
- [ ] Test debug email labeling system
- [ ] Verify Pipedream logging functionality

### **Testing Scenarios**
- [ ] Successful payment flow debugging
- [ ] Error scenario debugging  
- [ ] Email notification testing
- [ ] Frontend callback debugging
- [ ] Label organization verification

This comprehensive debug system provides complete visibility into CSG Forte payment processing through both real-time Pipedream logging and organized email debugging via the mailspons developer inbox.