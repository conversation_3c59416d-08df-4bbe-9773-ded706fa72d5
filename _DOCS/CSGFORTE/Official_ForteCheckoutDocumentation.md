# Forte Checkout v2 Auth

## Overview

Offload the burden of hosting your own payment form and reduce your PCI scope with Forte's third-generation Checkout. Checkout's advanced features enable you to make quick and painless payments on your own site with just a few lines of code.

With Checkout you get:

- **A simple integration**: Start using Checkout today with customizable JavaScript that sits on top of your existing site.
- **Wallet storage functionality**: Save customer and payment method data to make checking out fast and easy.
- **Up-to-date customer data**: Enable customers to edit their own address and payment data to ensure you always have the correct information.
- **Advanced form field settings**: Default Checkout's form fields to a value, empty, or hidden for full control over the data collected.
- **Granular amount settings**: Specify a non-editable amount to pay or a range of amounts for the customer.
- **Reduced cart abandonment**: Ensure your customer's trust by running the entire checkout process from your site.

## User Experience

Checkout's Pay Now button uses an HTML form POST action with a signature to create the modal window. When the customer clicks the button with coded parameters on the client side, Forte servers read the parameters and populate the modal window. The JavaScript, https://checkout.forte.net/v2/js, includes a Forte name-spaced version of jQuery so as not to conflict with other client-side jQuery code.

## Browser Compatibility

Checkout supports the three previous versions of the following browsers on a rolling basis:

- Firefox
- Chrome
- Safari
- Microsoft Edge
- Mobile – Chrome, Safari, and Firefox

Each time a new version of one of these browsers is released, Checkout begins supporting it and continues to support the previous two versions (and only the previous two). For example, if Checkout is currently supported on versions 9, 10, and 11 of a particular browser, when there is version 12 of this browser released, Checkout will support versions 10, 11, and 12 and will no longer support version 9.

This compatibility standard enables Checkout to use the latest and greatest security and performance features of the newest, most up-to-date browsers.

## Merchant Configuration

Before coding the Pay Now button, you must set up a test account and perform the following tasks:

1. Generate an API Access ID
2. Generate an API Secure Key

Only these values are required for Checkout. The Secure Web Pay configuration options do not affect Checkout.

### Generating Keys

Requests to Checkout require a hashed signature for authentication. This signature is created using your unique credentials, including a private key that you'll use when hashing signed elements. To integrate with Forte Checkout v2, use Forte's Dex application to create your API credentials.

Complete the following steps to generate your API Access ID and API Secure Key:

1. Log into your Dex Account.
2. Search for and select the Home Organization for which you want to create the credential set. If your Home Organization is the same as your Logged-In Organization, skip this step.
3. Select Developer > API Credentials from the Dex Main Menu.
4. Click the Create button. The Create API Credentials screen displays.
5. Enter a name for this set of API credentials in the Name Field. This field is required.
6. Click Save. The API Access ID and API Secure Key values display in their corresponding fields.
7. Click the Copy button next to the API Access ID and API Secure Key fields to record both of these newly generated values in a secure location to use in authenticating your requests.

**NOTE**: Once you save your API Secure Key, you will not be able to see the value again. If you forget your API Secure Key or it becomes compromised, you will have to regenerate the value in Dex.

## Authentication

Checkout uses hash signatures for authentication.

### Creating a Signature

The example below displays what information your signature should contain:

```
HMACSHA256("api_access_id|method|version_number|total_amount|utc_time|order_number|customer_token|paymethod_token",
"API Secure Key")
```

Use the following parameters when creating your hash signature:

| Parameter | Description | Type |
|-----------|-------------|------|
| api_access_id | The organization-specific API Access ID value that you created in Dex. See the Merchant Configuration for information on creating this value. | string |
| method | Supported types include the following: <br> - sale <br> - schedule <br> - token <br> - auth | string |
| version_number | 2.0 | string |
| total_amount | A string that represents the total amount of the transaction. **NOTE**: The total amount must be sent as a string as the button parameter. A possible side effect of total_amount being sent as a number is seemingly random, intermittent authentication failures. | string |
| utc_time | A date-time value (since 01/01/0001 00:00:00) that marks the day and time the request was sent to Checkout in ticks. Checkout will only accept utc_time values that are 20 minutes before the current time or 10 minutes after the current time. Requests expire after 10 minutes to prevent malicious users from capturing requests and resubmitting them at a later time. | string |
| order_number | A string that represents the order number associated with a transaction. | string |
| customer_token | An alphanumeric ID used to reference a customer. Forte stores the token and its associated information. | string |
| paymethod_token | An alphanumeric ID used to reference stored payment information (card or eCheck) for a customer. Forte stores the token and its associated information. | string |

Send the hashed values in signature parameter, a character string of varying length (depending on the hash method) that is used to represent a specific transaction for a specific merchant in Forte Checkout.

### Getting UTC Time

Checkout provides an API that returns the correct UTC time from Forte's server. Using this server-side value prevents inadvertent expiration errors from client PCs that do not have the correct local time set.

Use the following get UTC API URLs in your code to retrieve UTC ticks from Forte's servers:

- Production: `https://checkout.forte.net/getUTC?callback=?`
- Sandbox: `https://sandbox.forte.net/checkout/getUTC?callback=?`

The following script returns the correct UTC time for the hash:

```javascript
<script>
  var button = $('button[api_access_id]');
  $.getJSON('https://sandbox.forte.net/checkout/getUTC?callback=?').done(function (utc) {
    button.attr('utc_time', utc);
  });
</script>
```

You can also use the following PHP/Curl code sample to fetch the UTC time from the getUTC API URL:

```php
<?php
function utc() {
  $curlUTC = curl_init();
  curl_setopt($curlUTC, CURLOPT_URL, 'https://checkout.forte.net/getUTC?callback=?');
  curl_setopt($curlUTC, CURLOPT_BINARYTRANSFER, true);
  curl_setopt($curlUTC, CURLOPT_RETURNTRANSFER, true);

  $curlData = (curl_exec($curlUTC));
  $positionOfOpeningParenthesis = stripos($curlData,"(");
  $positionOfClosingParenthesis = stripos($curlData,")");
  $utc = substr($curlData,$positionOfOpeningParenthesis+1,$positionOfClosingParenthesis-2);
  return $utc;
  curl_close($curlUTC);
}
```

### Sample String

The following sections displays sample strings that will be hashed and their button attributes.

#### Single Amount

```
"8dcd03dc50d5aeed2f221e7e88ee4d23|sale|2.0|10.00|636397036957980000|A1234||","eedce6b47748968641a6af8bcd4756fe"
```

```html
<button api_access_id="8dcd03dc50d5aeed2f221e7e88ee4d23"
method="sale"
version_number="2.0"
location_id="115161"
utc_time="636397036957980000"
hash_method="sha256"
signature="44575464e3b99f8638858ac627eb9f03"
callback="oncallback"
total_amount="10.00"
order_number="A1234">
    Pay Now</button>
```

#### Scheduled, Ranged Amount

```
"8dcd03dc50d5aeed2f221e7e88ee4d23|schedule|2.0|1-9.5;5|636397071841460000|A1234|10047592|14554198","eedce6b47748968641a6af8bcd4756fe"
```

```html
<button api_access_id="8dcd03dc50d5aeed2f221e7e88ee4d23"
method="schedule"
customer_token="10047592"
paymethod_token="14554198"
version_number="2.0"
location_id="115161"
utc_time="636397071841460000"
hash_method="sha256"
signature="1df5eb88210898c04e5f48da5086dadb"
callback="oncallback"
total_amount="1-9.5;5"
schedule_start_date="1/1/2018"
schedule_frequency="weekly"
schedule_quantity="12"
schedule_continuous="false"
order number="A1234">
    Subscribe</button>
```

#### Selection Amount

```
"8dcd03dc50d5aeed2f221e7e88ee4d23|sale|2.0|{20,40,60,80,100,0};20|636397073365110000|||","eedce6b47748968641a6af8bcd4756fe"
```

```html
<button api_access_id="8dcd03dc50d5aeed2f221e7e88ee4d23"
method="sale"
version_number="2.0"
utc_time="636397073365110000"
location_id="115161"
hash_method="sha256"
signature="216760998032e94aa16ddb2293d4cf05"
callback="oncallback"
total_amount="{20,40,60,80,100,0};20">
    Select Amount</button>
```

#### Ranged Selection Amount with Labels

```
"8dcd03dc50d5aeed2f221e7e88ee4d23|sale|2.0|{1375.23,1573.66,56.99,0|Total outstanding,Last statement balance,Minimum balance,Specify different amount}|636397074849820000|||","eedce6b47748968641a6af8bcd4756fe"
```

```html
<button api_access_id="8dcd03dc50d5aeed2f221e7e88ee4d23"
method="sale"
version_number="2.0"
location_id="115161"
utc_time="636397074849820000"
hash_method="sha256"
signature="fec0347215626c217b03190960fa5744"
callback="oncallback"
total_amount="{1375.23,1573.66,56.99,0|Total Outstanding,Last Statement,Minimum Balance,Specify Different Amount}">
    Select Amount with Labels</button>
```

## Coding the Button

To create the Pay Now button for your site, you will first need to generate your API Access ID and API Secure Key in Dex (see Merchant Configuration).

### Endpoints

The following scripts invoke Checkout and should be placed in the `<head>` section of your website:

- Sandbox: `https://sandbox.forte.net/checkout/v2/js`
- Production: `https://checkout.forte.net/v2/js`

### Customizing the Button

The code below represents the minimal HTML code required to generate a payment button. If you want Checkout to capture additional information, such as a customer's shipping address, you must include the additional parameters listed in the Parameters section.

```html
<!DOCTYPE html>
<script type="text/javascript" src="https://sandbox.forte.net/checkout/v2/js"></script>
<button api_access_id="apiaccessid"
    method="sale"
    version_number="2.0"
    location_id="115161"
    utc_time="UTCtime"
    order_number="ASX458956"
    signature="sha256signature">
    Pay Now</button>
```

## Parameters

| Parameter | Description | Max Length | Attributes | Req | Type | Dex Transaction Reference |
|-----------|-------------|------------|------------|-----|------|--------------------------|
| method | Supported types: <br> - sale - creates an ad-hoc transaction <br> - schedule - used to schedule a single, future-dated transaction or create a schedule of recurring transactions <br> - token - creates both customer and payment method tokens or generates a new payment method token for an existing customer <br> - auth - authorizes the payment method | 8 | N | R | string | Action |
| allowed_methods | Pass all allowable payment methods as a comma-delimited string (e.g., allowed_methods=visa,mast,disc,amex,echeck). The first payment method in the list will be the default. <br> Supported payment methods include: visa, mast, amex, dine, jcb, disc, echeck | 6 | N | O | string | -- |
| hash_method | Supported values: md5*, sha1, sha256 | 6 | N | R | string | -- |
| version_number | 2.0* | 4 | N | R | string | -- |
| location_id | The location (Merchant ID) under which the transaction should be processed | 9 | N | R | string | Location ID |
| total_amount | See Configuring Amounts section. Must be greater than zero and sent as a string. | -- | Y | O | string | Authorization Amount |
| tax_amount | The sales tax amount of the transaction | -- | Y | O | dec | Tax Amount |
| consumer_id | A merchant-defined string used to identify the customer | -- | N | O | string | Customer ID |
| order_number | A string that represents the order number associated with a transaction | -- | N | O | string | Order ID |
| save_token | true = Create and save both customer and payment method tokens <br> false = Do not create or save tokens* | -- | N | O | bool | -- |

*Additional parameters are available for customer information, billing/shipping addresses, scheduling, and more. See the full documentation for details.*

## Configuring Amounts

Checkout supports a number of different amount configurations including:

1. **Single Amount greater than zero**:
   - The merchant specifies the amount
   - The customer specifies the amount

2. **Ranged Amount**:
   - The customer must enter an amount between merchant-specified values greater than zero

3. **Selection**:
   - The customer must select different merchant-specified values that include descriptive text

4. **Range and Selection**:
   - The customer must select different merchant-specified values or can enter an amount between merchant-specified values

### Creating Editable Amounts

To provide an Amount field in which a user can enter their own amount, specify the "edit" attribute when passing the total_amount parameter: `total_amount_attr="edit"`.

### Specifying Default Amounts

To define a default amount, add a semi-colon and the amount to the end of the total_amount parameter. Example for a ranged amount configuration that defaults to $5.00: `total_amount="1-10;5"`

### Using Amount Labels

For selection configurations, you can define descriptive text for different amounts. Example: `total_amount={1375.23,1573.66,56.99,0|Total Outstanding,Last Statement Balance,Minimum Balance,Specify Different Amount};500`

## Callbacks and Webhooks

Checkout's callbacks and webhooks both contain the results of a transaction. Callbacks provide real-time (synchronous), client-side notifications, while webhooks provide asynchronous server-side notifications.

**Note**: Forte recommends using both callbacks and webhooks in your Checkout implementation.

### Using Callbacks

Callbacks provide your front-end with synchronous updates about the current state of the checkout process. The button contains a callback attribute that accepts and displays notifications for the following transaction events:

- **begin** – When the customer launches the payment button
- **success** – Transaction was successful
- **failure** – Transaction has failed
- **error** – Request is invalid and Checkout could not be rendered
- **abort** – Customer canceled the checkout process
- **expired** – Customer failed to complete the transaction prior to the expiration time

#### Capturing Callback Messages

To capture callback messages, code the callback attribute into your button:

```html
<!DOCTYPE html>
<script type="text/javascript" src="https://sandbox.forte.net/checkout/v2/js"></script>
<button api_access_id="apiaccessid"
    method="sale"
    version_number="2.0"
    utc_time="UTCtime"
    order_number="ASX458956"
    callback="oncallback"
    signature="sha256signature">
    Pay Now</button>
```

This button code defines that your page will use the `oncallback` JavaScript function to capture the callback message.

### Using Webhooks

Webhooks send asynchronous messages about a transaction directly to a server-side URL endpoint that a merchant defines. You can subscribe to events like:

- transaction.sale
- transaction.authorize
- customer.create
- customer.update
- paymethod.create
- paymethod.update
- schedule.create (v3 only)
- schedule.update (v3 only)

If a webhook post fails (does not result in an HTTP 200 response), Forte retries the webhook post up to twenty times, adding one minute for each retry.

## Using Point-of-Sale Devices

Checkout supports the following types of credit card POS devices for ad-hoc and scheduled payments:
- Dynamag #21073062 v.1
- eDynamo #21079802
- Dynaflex #dynaflex2go

If `swipe=EMV-1` is passed in the button code and you have the Device Handler installed, Forte Checkout supports the V400C in hybrid mode for EMV card reading on Sale and Auth transactions.

## Troubleshooting FAQs

**Q. The Checkout button does nothing when clicked or is not styled correctly.**  
This can occur if the client blocks the following URL: checkout.forte.net. Verify the JavaScript loads correctly and check that you've included the script in the `<Head>` section of the HTML page.

**Q. I get an error message when I try to use Checkout with Internet Explorer.**  
Forte Checkout is not supported with Internet Explorer. Please refer to Browser Compatibility section for more information on supported browsers.

**Q. Does Forte Checkout Support Canadian Transaction Processing?**  
Yes, Forte Checkout supports Canadian transaction processing.

## Contact Information

For assistance, contact CSG Forte at:
- Phone: **************
- Email: <EMAIL> (technical support)
- Email: <EMAIL> (account management)
