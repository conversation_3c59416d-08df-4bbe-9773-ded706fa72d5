# CSG Forte Payment Integration - System Instructions

## Project Context
Migration from iCheckGateway (iCG) to CSG Forte payment processing platform for Infinity's Bento app. CSG acquired iCG and is deprecating iCG APIs by Q1 2026.

## Strategic Approach
- **Additive Integration**: Add CSG Forte buttons alongside existing Stripe/iCG buttons - do NOT replace existing functionality
- **No Backend Refactoring**: Maintain existing payment processing logic, invoice reconciliation, and notification systems
- **Metadata Field Reuse**: Use existing Stripe metadata fields for CC processing, existing iCG metadata fields for ACH processing
- **Feature Branch**: All work contained in `csgforte-integration` branch
- **Priority**: Credit Card processing first (replace Stripe), then ACH processing (supplement iCG)

## Environment Configuration
- **Local Dev**: localhost:8080 (rickyvoltz instance) → CSG Forte sandbox
- **Staging**: bento-dev.infinityhospitality.net (infinity instance) → CSG Forte sandbox
- **Production**: bento.infinityhospitality.net (infinity instance) → CSG Forte live

## Integration Architecture
Current iCG integration has 4 main files:
1. **Frontend (JS)**: `/Users/<USER>/Infinity/bento/_SRC/notify/_components/_core/_fields/contact-payment-sources.js` - iFrame-based payment capture
2. **Backend (PHP)**: `/Users/<USER>/Infinity/bento/_SRC/pagoda/services/iCheckGatewayService.php` - Payment processing, invoice reconciliation
3. **Cron Job (PHP)**: `/Users/<USER>/Infinity/bento/_SRC/pagoda/cron/daily-9-30-updateICGPaymentStatus.php` - Status checking via SOAP API
4. **Service Layer**: `/Users/<USER>/Infinity/bento/_SERVICES/app/src/api/services/iCheckGatewayService.php` - `gulp build` outputs here.

## Implementation Guidelines

### UI Integration
- Add new payment buttons alongside existing ones
- Maintain existing UI patterns and styling
- Create payment method selection modal if needed
- Preserve existing error handling and user feedback

### Payment Processing
- **DO NOT** modify existing payment reconciliation logic
- **DO NOT** change existing invoice update workflows
- **DO NOT** alter existing email notification systems
- **DO** reuse existing metadata field structures
- **DO** maintain existing payment object creation patterns

### Data Handling
- **Credit Cards**: Reuse existing Stripe metadata fields and payment object structure
- **ACH Payments**: Reuse existing iCG metadata fields and payment object structure
- **No Database Changes**: Use existing payment table schema
- **No Migration**: Existing payments remain unchanged

### Environment Variables
- Hardcode CSG Forte credentials for development
- AWS pipeline updates (T34) can be done independently
- Environment-specific configurations for sandbox vs live

## Critical Requirements
1. **Preserve Existing Functionality**: Stripe and iCG buttons must remain functional
2. **Sequential Development**: Complete Credit Cards before starting ACH integration
3. **Test-as-you-go**: Validate each task before proceeding
4. **Single Instance**: Focus on Infinity instance only (not multi-tenant)
5. **Plug-and-play**: Replace iframe implementations only, not business logic

## Task Execution
- Each task results in separate git commit
- Follow existing code patterns and conventions
- Use existing logging (MailSpon service)
- Maintain existing security and compliance patterns
- Document any deviations or issues in task commits

## Success Criteria
- CSG Forte payments process alongside existing Stripe/iCG payments
- Existing payment flows remain unaffected
- Invoice reconciliation and email notifications work identically
- Seamless user experience with new payment options
- Zero downtime during integration and deployment

## Features

| ID | Feature | Description |
|----|---------|-------------|
| F1 | Discovery & Environment Setup | Initial discovery of API tokens, object types, and development environment setup |
| F2 | Core Implementation | Main webhook endpoint implementation with parsing, deduplication, and data creation |
| F3 | Testing & Validation | Comprehensive testing across local, staging, and integration environments |
| F4 | Production Deployment | Security review, production deployment, and documentation completion |
| F5 | CSG Forte Platform Setup | CSG Forte platform registration, API research, and development environment configuration |
| F6 | Credit Card Integration | CSG Forte Credit Card processing implementation (Priority 1) |
| F7 | ACH Integration | CSG Forte ACH processing implementation (Priority 2) |
| F8 | Payment Status & Production | Webhook implementation, staging/production deployment, and status management |
| F9 | Infrastructure Updates | AWS pipeline configuration and environment variable management |

## ⚠️ Critical Multi-Tenant Gotchas

### Development Warnings
- **Multi-tenant architecture** - Same blueprints, different data per instance
- **Instance-aware code** - Always use discovered values, never hardcode

## Notes

- Each task should result in a separate git commit for tracking
- Manual tasks require developer action and documentation of results
- AI-assisted tasks include detailed instructions for implementation
- Dependencies must be completed before starting dependent tasks
- Test each phase thoroughly before proceeding to next phase

## Integration Architecture Notes
- **iFrame Pattern**: CSG Forte follows same iframe-based pattern as current iCG implementation
- **Response Handler**: Similar `window.addEventListener('message', csgForteResponseHandler)` pattern needed
- **Metadata Reuse**: CC payments use Stripe metadata structure, ACH payments use iCG metadata structure
- **Service Layer**: May need duplicate CSG Forte service in both `/pagoda/services/` and `/_SERVICES/app/src/api/services/`
- **Payment Object**: Existing payment object structure can be reused with new `csg_forte_payment_id` field instead of `icg_payment_id`
