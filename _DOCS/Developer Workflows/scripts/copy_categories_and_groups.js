/**
 * Simple script to copy groups from one category to another
 *
 * @param {number} sourceCategoryId - ID of the category to copy groups FROM
 * @param {number} targetCategoryId - ID of the category to copy groups TO
 * @param {string} nameSuffix - Suffix to append to duplicated names (default: ' (COPY)')
 * @param {boolean} dryRun - If true, only logs changes without creating records (default: true)
 * @param {boolean} skipExisting - If true, skips groups that already exist in target (default: true)
 */
function copyGroupsBetweenCategories(sourceCategoryId, targetCategoryId, nameSuffix = ' (COPY)', dryRun = true, skipExisting = true) {
  console.log(`\n========== COPYING GROUPS BETWEEN CATEGORIES ==========`);
  console.log(`Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE RUN (will create records)'}`);
  console.log(`Source Category ID: ${sourceCategoryId}`);
  console.log(`Target Category ID: ${targetCategoryId}`);
  console.log(`Name suffix: "${nameSuffix}"`);
  console.log(`Skip existing: ${skipExisting ? 'YES' : 'NO'}`);
  console.log(`=======================================================\n`);

  if (!sourceCategoryId || !targetCategoryId) {
    console.error('❌ ERROR: Both sourceCategoryId and targetCategoryId are required!');
    return;
  }

  // Get all groups from the source category
  databaseConnection.obj.getWhere('inventory_billable_groups', {category: sourceCategoryId}, function(sourceGroups) {
    console.log(`Found ${sourceGroups.length} groups in source category ${sourceCategoryId}.`);

    if (sourceGroups.length === 0) {
      console.warn('⚠️ WARNING: No groups found in source category. Nothing to copy.');
      return;
    }

    // Track statistics
    const stats = {
      groupsProcessed: 0,
      groupsSkipped: 0,
      groupsCreated: 0
    };

    // Process groups sequentially
    processNextGroupCopy(sourceGroups, 0, targetCategoryId, nameSuffix, dryRun, skipExisting, stats);
  });
}

/**
 * Process groups one at a time to avoid server connection issues
 */
function processNextGroupCopy(groups, index, targetCategoryId, nameSuffix, dryRun, skipExisting, stats) {
  if (index >= groups.length) {
    console.log(`\n========== COPY PROCESS COMPLETE ==========`);
    console.log(`Groups processed: ${stats.groupsProcessed}`);
    console.log(`Groups skipped (already existed): ${stats.groupsSkipped}`);
    console.log(`Groups created: ${stats.groupsCreated}`);
    console.log(`==========================================\n`);
    return;
  }

  const sourceGroup = groups[index];
  stats.groupsProcessed++;

  console.log(`\n--- Processing group ${index + 1}/${groups.length} ---`);
  console.log(`Source: "${sourceGroup.name}" (ID: ${sourceGroup.id})`);

  const newGroupName = sourceGroup.name + nameSuffix;
  console.log(`Target name: "${newGroupName}"`);

  // Check if group already exists in target category (if skipExisting is true)
  if (skipExisting && !dryRun) {
    databaseConnection.obj.getWhere('inventory_billable_groups', {
      category: targetCategoryId,
      name: newGroupName
    }, function(existingGroups) {
      if (existingGroups && existingGroups.length > 0) {
        stats.groupsSkipped++;
        console.log(`⚠️ SKIPPING: Group "${newGroupName}" already exists in target category (ID: ${existingGroups[0].id})`);

        // Continue with next group
        setTimeout(function() {
          processNextGroupCopy(groups, index + 1, targetCategoryId, nameSuffix, dryRun, skipExisting, stats);
        }, 50);
      } else {
        // Group doesn't exist, create it
        createGroupCopy(sourceGroup, targetCategoryId, nameSuffix, dryRun, stats, function() {
          // Continue with next group
          setTimeout(function() {
            processNextGroupCopy(groups, index + 1, targetCategoryId, nameSuffix, dryRun, skipExisting, stats);
          }, 50);
        });
      }
    });
  } else {
    // Skip existing check disabled or dry run, just create
    createGroupCopy(sourceGroup, targetCategoryId, nameSuffix, dryRun, stats, function() {
      // Continue with next group (no delay needed for dry run)
      processNextGroupCopy(groups, index + 1, targetCategoryId, nameSuffix, dryRun, skipExisting, stats);
    });
  }
}

/**
 * Create a copy of a group in the target category
 */
function createGroupCopy(sourceGroup, targetCategoryId, nameSuffix, dryRun, stats, callback) {
  var newGroup = _.clone(sourceGroup);

  // Remove the id property as a new one will be assigned by the database
  delete newGroup.id;

  // Update name by appending the suffix
  newGroup.name = sourceGroup.name + nameSuffix;

  // Update category to point to the target category
  newGroup.category = targetCategoryId;

  // Set data_source properties
  const nameHash = sourceGroup.name.split('').reduce((hash, char) => {
    return ((hash << 5) - hash) + char.charCodeAt(0);
  }, 0);
  newGroup.data_source = Math.abs(nameHash); // Ensure positive integer
  newGroup.data_source_id = sourceGroup.id;

  // Update date_created to current time
  newGroup.date_created = new Date().toISOString().replace('T', ' ').replace('Z', '');

  if (!dryRun) {
    console.log(`Creating: "${newGroup.name}" in category ${targetCategoryId}...`);
    databaseConnection.obj.create('inventory_billable_groups', newGroup, function(createdGroup) {
      stats.groupsCreated++;
      console.log(`✅ CREATED: "${createdGroup.name}" (ID: ${createdGroup.id})`);
      if (callback) callback();
    });
  } else {
    console.log(`[DRY RUN] Would create: "${newGroup.name}" in category ${targetCategoryId}`);
    if (callback) callback();
  }
}

/**
 * UNDO function to delete all groups that were copied with a specific suffix
 * This will find and delete all groups that match the naming pattern
 *
 * @param {number} targetCategoryId - ID of the category where copies were created
 * @param {string} nameSuffix - Suffix that was used when creating copies
 * @param {boolean} dryRun - If true, only logs what would be deleted (default: true)
 * @param {boolean} useRestore - If true, uses restore() instead of erase() (default: false)
 */
function undoCopiedGroups(targetCategoryId, nameSuffix, dryRun = true, useRestore = false) {
  console.log(`\n========== UNDO COPIED GROUPS ==========`);
  console.log(`Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE RUN (will delete records)'}`);
  console.log(`Target Category ID: ${targetCategoryId}`);
  console.log(`Name suffix to find: "${nameSuffix}"`);
  console.log(`Delete method: ${useRestore ? 'restore()' : 'erase()'}`);
  console.log(`=======================================\n`);

  if (!targetCategoryId || !nameSuffix) {
    console.error('❌ ERROR: Both targetCategoryId and nameSuffix are required!');
    return;
  }

  // Get all groups from the target category
  databaseConnection.obj.getWhere('inventory_billable_groups', {category: targetCategoryId}, function(allGroups) {
    console.log(`Found ${allGroups.length} total groups in target category ${targetCategoryId}.`);

    // Filter to only groups that end with the suffix (these are the copies we created)
    const copiedGroups = allGroups.filter(group => group.name.endsWith(nameSuffix));
    console.log(`Found ${copiedGroups.length} groups with suffix "${nameSuffix}" that appear to be copies.`);

    if (copiedGroups.length === 0) {
      console.warn('⚠️ WARNING: No copied groups found to undo. Nothing to delete.');
      return;
    }

    console.log(`\nGroups that will be ${dryRun ? 'WOULD BE ' : ''}deleted:`);
    copiedGroups.forEach((group, i) => {
      console.log(`  ${i+1}. "${group.name}" (ID: ${group.id})`);
    });

    if (!dryRun) {
      console.log(`\n🚨 PROCEEDING WITH DELETION OF ${copiedGroups.length} GROUPS...`);

      // Track statistics
      const stats = {
        groupsProcessed: 0,
        groupsDeleted: 0,
        groupsErrored: 0
      };

      // Process deletions sequentially
      processNextGroupDeletion(copiedGroups, 0, useRestore, stats);
    } else {
      console.log(`\n[DRY RUN] Would delete ${copiedGroups.length} groups using ${useRestore ? 'restore()' : 'erase()'} method.`);
      console.log(`To execute for real, call: undoCopiedGroups(${targetCategoryId}, "${nameSuffix}", false, ${useRestore})`);
    }
  });
}

/**
 * Process group deletions one at a time to avoid server connection issues
 */
function processNextGroupDeletion(groups, index, useRestore, stats) {
  if (index >= groups.length) {
    console.log(`\n========== UNDO PROCESS COMPLETE ==========`);
    console.log(`Groups processed: ${stats.groupsProcessed}`);
    console.log(`Groups deleted: ${stats.groupsDeleted}`);
    console.log(`Groups with errors: ${stats.groupsErrored}`);
    console.log(`==========================================\n`);
    return;
  }

  const group = groups[index];
  stats.groupsProcessed++;

  console.log(`\n--- Deleting group ${index + 1}/${groups.length} ---`);
  console.log(`Group: "${group.name}" (ID: ${group.id})`);

  const deleteMethod = useRestore ? 'restore' : 'erase';
  console.log(`Using method: databaseConnection.obj.${deleteMethod}()`);

  // Use the appropriate deletion method
  if (useRestore) {
    databaseConnection.obj.restore('inventory_billable_groups', group, function(result) {
      if (result && result.success !== false) {
        stats.groupsDeleted++;
        console.log(`✅ RESTORED: "${group.name}" (ID: ${group.id})`);
      } else {
        stats.groupsErrored++;
        console.error(`❌ ERROR RESTORING: "${group.name}" (ID: ${group.id})`);
      }

      // Continue with next group after a short delay
      setTimeout(function() {
        processNextGroupDeletion(groups, index + 1, useRestore, stats);
      }, 100);
    });
  } else {
    databaseConnection.obj.erase('inventory_billable_groups', group, function(result) {
      if (result && result.success !== false) {
        stats.groupsDeleted++;
        console.log(`✅ ERASED: "${group.name}" (ID: ${group.id})`);
      } else {
        stats.groupsErrored++;
        console.error(`❌ ERROR ERASING: "${group.name}" (ID: ${group.id})`);
      }

      // Continue with next group after a short delay
      setTimeout(function() {
        processNextGroupDeletion(groups, index + 1, useRestore, stats);
      }, 100);
    });
  }
}

/**
 * OLD COMPLEX SCRIPT BELOW - KEPT FOR REFERENCE
 * Script to duplicate inventory billable categories and their groups
 * and assign them to a target category
 *
 * @param {boolean} dryRun - If true, only logs changes without creating records
 * @param {number} limit - Limit processing to this many categories (for testing)
 * @param {number} startIndex - Skip this many categories before starting (default: 0)
 * @param {boolean} skipExisting - If true, skips categories that already have copies (default: true)
 * @param {string} nameSuffix - Suffix to append to duplicated names (default: ' (COPY)')
 * @param {number} targetCategoryId - ID of the category to assign all duplicated groups to
 */
function duplicateBillableCategories(dryRun = true, limit = 1, startIndex = 0, skipExisting = true, nameSuffix = ' (COPY)', targetCategoryId = null) {
  console.log(`\n========== STARTING DUPLICATION PROCESS ==========`);
  console.log(`Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE RUN (will create records)'}`);
  console.log(`Processing: ${limit} categories starting from index ${startIndex}`);
  console.log(`Skip existing: ${skipExisting ? 'YES (will skip categories with existing copies)' : 'NO (will create new copies regardless)'}`);
  console.log(`Name suffix: "${nameSuffix}"`);
  console.log(`Target category ID: ${targetCategoryId || 'NONE (will create new categories)'}`);
  console.log(`=================================================\n`);

  databaseConnection.obj.getAll('inventory_billable_categories', function(ib_cat) {
    console.log(`Found ${ib_cat.length} total billable categories in the database.`);

    // Filter out categories that have already been processed (contain the suffix in name)
    const filteredCategories = ib_cat.filter(category => !category.name.includes(nameSuffix));
    console.log(`Found ${filteredCategories.length} original categories (${ib_cat.length - filteredCategories.length} are copies).`);

    // Apply start index and limit
    const categoriesToProcess = filteredCategories.slice(startIndex, startIndex + limit);

    if (categoriesToProcess.length === 0) {
      console.warn(`⚠️ WARNING: No categories to process with current settings. Check your startIndex (${startIndex}) and limit (${limit}).`);
      return;
    }

    console.log(`\nWill process ${categoriesToProcess.length} categories:`);
    categoriesToProcess.forEach((cat, idx) => {
      console.log(`  ${idx+1}. "${cat.name}" (ID: ${cat.id})`);
    });

    // Track statistics
    const stats = {
      categoriesProcessed: 0,
      categoriesSkipped: 0,
      categoriesCreated: 0,
      groupsProcessed: 0,
      groupsSkipped: 0,
      groupsCreated: 0
    };

    // Process categories sequentially to avoid overwhelming the server
    processNextCategory(categoriesToProcess, 0, dryRun, skipExisting, stats, nameSuffix, targetCategoryId);
  });
}

/**
 * Process categories one at a time to avoid server connection issues
 */
function processNextCategory(categories, index, dryRun, skipExisting, stats, nameSuffix, targetCategoryId) {
  if (index >= categories.length) {
    console.log(`\n========== DUPLICATION PROCESS COMPLETE ==========`);
    console.log(`Categories processed: ${stats.categoriesProcessed}`);
    console.log(`Categories skipped (already had copies): ${stats.categoriesSkipped}`);
    console.log(`Categories created: ${stats.categoriesCreated}`);
    console.log(`Groups processed: ${stats.groupsProcessed}`);
    console.log(`Groups skipped (already existed): ${stats.groupsSkipped}`);
    console.log(`Groups created: ${stats.groupsCreated}`);
    console.log(`=================================================\n`);
    return;
  }

  const ibc = categories[index];
  stats.categoriesProcessed++;

  console.log(`\n========== CATEGORY ${index+1}/${categories.length} ==========`);
  console.log(`Processing category: "${ibc.name}" (ID: ${ibc.id})`);

  // If skipExisting is true, check if this category already has a copy
  if (skipExisting) {
    const searchName = ibc.name + nameSuffix;
    console.log(`Checking if category "${ibc.name}" already has copies...`);

    databaseConnection.obj.getWhere('inventory_billable_categories', {name: searchName}, function(existingCategories) {
      if (existingCategories && existingCategories.length > 0) {
        stats.categoriesSkipped++;
        console.warn(`⚠️ SKIPPING CATEGORY: "${ibc.name}" already has ${existingCategories.length} copies:`);

        // Log each existing copy
        existingCategories.forEach((cat, i) => {
          console.warn(`  Copy ${i+1}: "${cat.name}" (ID: ${cat.id})`);
        });

        // If we want to check for groups in the existing category
        if (existingCategories.length === 1) {
          console.log(`\nChecking for groups in the existing category copy...`);
          checkGroupsInExistingCategory(ibc.id, existingCategories[0].id, dryRun, skipExisting, stats, nameSuffix, targetCategoryId, function() {
            // Continue with the next category
            processNextCategory(categories, index + 1, dryRun, skipExisting, stats, nameSuffix, targetCategoryId);
          });
        } else {
          console.warn(`Multiple copies exist. Skipping group check to avoid confusion.`);
          // Continue with the next category
          processNextCategory(categories, index + 1, dryRun, skipExisting, stats, nameSuffix, targetCategoryId);
        }
      } else {
        console.log(`No existing copies found for "${ibc.name}". Will create a new copy.`);
        // No copy exists, proceed with creation
        createCategory(ibc, dryRun, skipExisting, stats, nameSuffix, targetCategoryId, function() {
          // Continue with the next category
          processNextCategory(categories, index + 1, dryRun, skipExisting, stats, nameSuffix, targetCategoryId);
        });
      }
    });
  } else {
    console.log(`Skip existing check disabled. Will create a new copy regardless.`);
    // Skip checking, proceed with creation
    createCategory(ibc, dryRun, skipExisting, stats, nameSuffix, targetCategoryId, function() {
      // Continue with the next category
      processNextCategory(categories, index + 1, dryRun, skipExisting, stats, nameSuffix, targetCategoryId);
    });
  }
}

/**
 * Check for groups in an existing category copy
 */
function checkGroupsInExistingCategory(originalCategoryId, existingCopyCategoryId, dryRun, skipExisting, stats, nameSuffix, targetCategoryId, callback) {
  // Get groups from original category
  databaseConnection.obj.getWhere('inventory_billable_groups', {category: originalCategoryId}, function(originalGroups) {
    console.log(`Found ${originalGroups.length} groups in the original category.`);
    stats.groupsProcessed += originalGroups.length;

    // Get groups from the existing copy category
    databaseConnection.obj.getWhere('inventory_billable_groups', {category: existingCopyCategoryId}, function(existingGroups) {
      console.log(`Found ${existingGroups.length} groups in the existing category copy.`);

      // Simple direct comparison - find originals that don't have copies
      const missingGroups = [];

      // For each original group, check if a corresponding copy exists
      originalGroups.forEach(originalGroup => {
        const expectedCopyName = originalGroup.name + nameSuffix;
        const hasCopy = existingGroups.some(copyGroup => copyGroup.name === expectedCopyName);

        if (!hasCopy) {
          missingGroups.push(originalGroup);
        }
      });

      // Report and process missing groups
      if (missingGroups.length > 0) {
        console.log(`Found ${missingGroups.length} groups that need to be copied:`);
        missingGroups.forEach((group, i) => {
          console.log(`  ${i+1}. "${group.name}" (ID: ${group.id})`);
        });

        if (!dryRun) {
          console.log(`Creating these missing groups...`);
          processNextGroup(missingGroups, 0, targetCategoryId || existingCopyCategoryId, dryRun, stats, nameSuffix, callback);
        } else {
          console.log(`[DRY RUN] Would create these ${missingGroups.length} missing groups.`);
          if (callback) callback();
        }
      } else {
        console.log(`All groups from the original category have copies in the existing category copy.`);
        if (callback) callback();
      }
    });
  });
}

/**
 * Create a new category based on an existing one
 */
function createCategory(ibc, dryRun, skipExisting, stats, nameSuffix, targetCategoryId, callback) {
  var newIBC = _.clone(ibc);

  // Remove the id property as a new one will be assigned by the database
  delete newIBC.id;

  // Update name by appending the suffix
  newIBC.name = ibc.name + nameSuffix;

  // Set data_source properties
  // data_source should be a hash of the original name (as integer)
  // data_source_id should be the original record ID
  const nameHash = ibc.name.split('').reduce((hash, char) => {
    return ((hash << 5) - hash) + char.charCodeAt(0);
  }, 0);
  newIBC.data_source = Math.abs(nameHash); // Ensure positive integer
  newIBC.data_source_id = ibc.id;

  // Update date_created to current time
  newIBC.date_created = new Date().toISOString().replace('T', ' ').replace('Z', '');

  if (!dryRun) {
    console.log(`Creating new category: "${newIBC.name}"...`);
    databaseConnection.obj.create('inventory_billable_categories', newIBC, function(createdIBC) {
      stats.categoriesCreated++;
      console.log(`✅ CREATED: New category "${createdIBC.name}" (ID: ${createdIBC.id})`);

      // Now process the groups for this category - assign to target category if specified
      const categoryIdForGroups = targetCategoryId || createdIBC.id;
      processGroupsForCategory(ibc.id, categoryIdForGroups, dryRun, skipExisting, stats, nameSuffix, callback);
    });
  } else {
    console.log(`[DRY RUN] Would create new category: "${newIBC.name}"`);
    // For dry run, still show what groups would be processed
    const categoryIdForGroups = targetCategoryId || 'new-id-placeholder';
    processGroupsForCategory(ibc.id, categoryIdForGroups, dryRun, skipExisting, stats, nameSuffix, callback);
  }
}

/**
 * Process all groups for a given category
 */
function processGroupsForCategory(originalCategoryId, newCategoryId, dryRun, skipExisting, stats, nameSuffix, callback) {
  console.log(`\nProcessing groups for category ID ${originalCategoryId}...`);

  databaseConnection.obj.getWhere('inventory_billable_groups', {category: originalCategoryId}, function(ib_groups) {
    console.log(`Found ${ib_groups.length} groups in the original category.`);
    stats.groupsProcessed += ib_groups.length;

    // Process groups sequentially
    processNextGroup(ib_groups, 0, newCategoryId, dryRun, stats, nameSuffix, callback);
  });
}

/**
 * Process groups one at a time to avoid server connection issues
 */
function processNextGroup(groups, index, newCategoryId, dryRun, stats, nameSuffix, callback) {
  if (index >= groups.length) {
    console.log(`Completed processing all groups for this category.`);
    if (callback) callback();
    return;
  }

  const ibg = groups[index];
  const newGroupName = ibg.name + nameSuffix;

  // Check if this group already exists in the new category (if not a dry run and not a placeholder ID)
  if (!dryRun && newCategoryId !== 'new-id-placeholder') {
    databaseConnection.obj.getWhere('inventory_billable_groups', {
      category: newCategoryId,
      name: newGroupName
    }, function(existingGroups) {
      if (existingGroups && existingGroups.length > 0) {
        stats.groupsSkipped++;
        console.log(`⚠️ SKIPPING GROUP: "${ibg.name}" already exists in the new category as "${newGroupName}" (ID: ${existingGroups[0].id})`);

        // Process the next group after a short delay
        setTimeout(function() {
          processNextGroup(groups, index + 1, newCategoryId, dryRun, stats, nameSuffix, callback);
        }, 50);
      } else {
        // Group doesn't exist, create it
        createGroup(ibg, newCategoryId, dryRun, stats, nameSuffix, function() {
          // Process the next group after a short delay
          setTimeout(function() {
            processNextGroup(groups, index + 1, newCategoryId, dryRun, stats, nameSuffix, callback);
          }, 50);
        });
      }
    });
  } else {
    // For dry run or when using placeholder ID, just simulate creation
    createGroup(ibg, newCategoryId, dryRun, stats, nameSuffix, function() {
      // Process the next group (no delay needed for dry run)
      processNextGroup(groups, index + 1, newCategoryId, dryRun, stats, nameSuffix, callback);
    });
  }
}

/**
 * Create a new group based on an existing one
 */
function createGroup(ibg, newCategoryId, dryRun, stats, nameSuffix, callback) {
  var newIBG = _.clone(ibg);

  // Remove the id property as a new one will be assigned by the database
  delete newIBG.id;

  // Update name by appending the suffix
  newIBG.name = ibg.name + nameSuffix;

  // Update category to point to the new category
  newIBG.category = newCategoryId;

  // Set data_source properties
  const nameHash = ibg.name.split('').reduce((hash, char) => {
    return ((hash << 5) - hash) + char.charCodeAt(0);
  }, 0);
  newIBG.data_source = Math.abs(nameHash); // Ensure positive integer
  newIBG.data_source_id = ibg.id;

  // Update date_created to current time
  newIBG.date_created = new Date().toISOString().replace('T', ' ').replace('Z', '');

  if (!dryRun) {
    console.log(`Creating new group: "${newIBG.name}"...`);
    databaseConnection.obj.create('inventory_billable_groups', newIBG, function(createdIBG) {
      stats.groupsCreated++;
      console.log(`✅ CREATED: New group "${createdIBG.name}" (ID: ${createdIBG.id})`);
      if (callback) callback();
    });
  } else {
    console.log(`[DRY RUN] Would create new group: "${newIBG.name}"`);
    if (callback) callback();
  }
}

/**
 * Create a new combination based on an existing one
 */
function createCombination(ibc, newCategoryId, dryRun, stats, nameSuffix, callback) {
  var newIBC = _.clone(ibc);

  // Remove the id property as a new one will be assigned by the database
  delete newIBC.id;

  // Update name by appending the suffix
  newIBC.name = ibc.name.trim() + nameSuffix;

  // Update category to point to the new category
  newIBC.category = newCategoryId;

  // Set data_source properties
  const nameHash = ibc.name.split('').reduce((hash, char) => {
    return ((hash << 5) - hash) + char.charCodeAt(0);
  }, 0);
  newIBC.data_source = Math.abs(nameHash); // Ensure positive integer
  newIBC.data_source_id = ibc.id;

  // Update date_created to current time
  newIBC.date_created = new Date().toISOString().replace('T', ' ').replace('Z', '');

  if (!dryRun) {
    console.log(`Creating new combination: "${newIBC.name}"...`);
    databaseConnection.obj.create('inventory_billable_combinations', newIBC, function(createdIBC) {
      stats.combinationsCreated++;
      console.log(`✅ CREATED: New combination "${createdIBC.name}" (ID: ${createdIBC.id})`);
      if (callback) callback();
    });
  } else {
    console.log(`[DRY RUN] Would create new combination: "${newIBC.name}"`);
    if (callback) callback();
  }
}

/**
 * Script to duplicate inventory billable combination categories and their combinations
 * with modified surcharges
 *
 * @param {boolean} dryRun - If true, only logs changes without creating records
 * @param {number} limit - Limit processing to this many categories (for testing)
 * @param {number} startIndex - Skip this many categories before starting (default: 0)
 * @param {boolean} skipExisting - If true, skips categories that already have copies (default: true)
 */
function duplicateBillableCombinationCategories(dryRun = true, limit = 1, startIndex = 0, skipExisting = true) {
  console.log(`\n========== STARTING COMBINATION DUPLICATION PROCESS ==========`);
  console.log(`Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE RUN (will create records)'}`);
  console.log(`Processing: ${limit} combination categories starting from index ${startIndex}`);
  console.log(`Skip existing: ${skipExisting ? 'YES (will skip categories with existing copies)' : 'NO (will create new copies regardless)'}`);
  console.log(`=================================================\n`);

  databaseConnection.obj.getAll('inventory_billable_combination_categories', function(ib_cat) {
    console.log(`Found ${ib_cat.length} total billable combination categories in the database.`);

    // Filter out categories that have already been processed (contain "(*)" in name)
    const filteredCategories = ib_cat.filter(category => !category.name.includes('(*)'));
    console.log(`Found ${filteredCategories.length} original combination categories (${ib_cat.length - filteredCategories.length} are copies).`);

    // Apply start index and limit
    const categoriesToProcess = filteredCategories.slice(startIndex, startIndex + limit);

    if (categoriesToProcess.length === 0) {
      console.warn(`⚠️ WARNING: No combination categories to process with current settings. Check your startIndex (${startIndex}) and limit (${limit}).`);
      return;
    }

    console.log(`\nWill process ${categoriesToProcess.length} combination categories:`);
    categoriesToProcess.forEach((cat, idx) => {
      console.log(`  ${idx+1}. "${cat.name}" (ID: ${cat.id})`);
    });

    // Track statistics
    const stats = {
      categoriesProcessed: 0,
      categoriesSkipped: 0,
      categoriesCreated: 0,
      combinationsProcessed: 0,
      combinationsSkipped: 0,
      combinationsCreated: 0
    };

    // Process categories sequentially to avoid overwhelming the server
    processNextCombinationCategory(categoriesToProcess, 0, dryRun, skipExisting, stats);
  });
}

/**
 * Process combination categories one at a time to avoid server connection issues
 */
function processNextCombinationCategory(categories, index, dryRun, skipExisting, stats) {
  if (index >= categories.length) {
    console.log(`\n========== COMBINATION DUPLICATION PROCESS COMPLETE ==========`);
    console.log(`Combination categories processed: ${stats.categoriesProcessed}`);
    console.log(`Combination categories skipped (already had copies): ${stats.categoriesSkipped}`);
    console.log(`Combination categories created: ${stats.categoriesCreated}`);
    console.log(`Combinations processed: ${stats.combinationsProcessed}`);
    console.log(`Combinations skipped (already existed): ${stats.combinationsSkipped}`);
    console.log(`Combinations created: ${stats.combinationsCreated}`);
    console.log(`=================================================\n`);
    return;
  }

  const ibc = categories[index];
  stats.categoriesProcessed++;

  console.log(`\n========== COMBINATION CATEGORY ${index+1}/${categories.length} ==========`);
  console.log(`Processing combination category: "${ibc.name}" (ID: ${ibc.id})`);

  // If skipExisting is true, check if this category already has a copy
  if (skipExisting) {
    const searchName = ibc.name.trim() + ' (*)';
    console.log(`Checking if combination category "${ibc.name}" already has copies...`);

    databaseConnection.obj.getWhere('inventory_billable_combination_categories', {name: searchName}, function(existingCategories) {
      if (existingCategories && existingCategories.length > 0) {
        stats.categoriesSkipped++;
        console.warn(`⚠️ SKIPPING COMBINATION CATEGORY: "${ibc.name}" already has ${existingCategories.length} copies:`);

        // Log each existing copy
        existingCategories.forEach((cat, i) => {
          console.warn(`  Copy ${i+1}: "${cat.name}" (ID: ${cat.id})`);
        });

        // If we want to check for combinations in the existing category
        if (existingCategories.length === 1) {
          console.log(`\nChecking for combinations in the existing category copy...`);
          checkCombinationsInExistingCategory(ibc.id, existingCategories[0].id, dryRun, skipExisting, stats, function() {
            // Continue with the next category
            processNextCombinationCategory(categories, index + 1, dryRun, skipExisting, stats);
          });
        } else {
          console.warn(`Multiple copies exist. Skipping combinations check to avoid confusion.`);
          // Continue with the next category
          processNextCombinationCategory(categories, index + 1, dryRun, skipExisting, stats);
        }
      } else {
        console.log(`No existing copies found for "${ibc.name}". Will create a new copy.`);
        // No copy exists, proceed with creation
        createCombinationCategory(ibc, dryRun, skipExisting, stats, function() {
          // Continue with the next category
          processNextCombinationCategory(categories, index + 1, dryRun, skipExisting, stats);
        });
      }
    });
  } else {
    console.log(`Skip existing check disabled. Will create a new copy regardless.`);
    // Skip checking, proceed with creation
    createCombinationCategory(ibc, dryRun, skipExisting, stats, function() {
      // Continue with the next category
      processNextCombinationCategory(categories, index + 1, dryRun, skipExisting, stats);
    });
  }
}

/**
 * Check for combinations in an existing category copy
 */
function checkCombinationsInExistingCategory(originalCategoryId, existingCopyCategoryId, dryRun, skipExisting, stats, callback) {
  // Get combinations from original category
  databaseConnection.obj.getWhere('inventory_billable_combinations', {category: originalCategoryId}, function(originalCombinations) {
    console.log(`Found ${originalCombinations.length} combinations in the original category.`);
    stats.combinationsProcessed += originalCombinations.length;

    // Get combinations from the existing copy category
    databaseConnection.obj.getWhere('inventory_billable_combinations', {category: existingCopyCategoryId}, function(existingCombinations) {
      console.log(`Found ${existingCombinations.length} combinations in the existing category copy.`);

      // Simple direct comparison - find originals that don't have copies
      const missingCombinations = [];

      // For each original combination, check if a corresponding copy exists
      originalCombinations.forEach(originalCombination => {
        const expectedCopyName = (originalCombination.name.trim() + ' (*)').trim();
        const hasCopy = existingCombinations.some(copyCombination =>
          copyCombination.name.trim() === expectedCopyName);

        if (!hasCopy) {
          missingCombinations.push(originalCombination);
        }
      });

      // Report and process missing combinations
      if (missingCombinations.length > 0) {
        console.log(`Found ${missingCombinations.length} combinations that need to be copied:`);
        missingCombinations.forEach((combination, i) => {
          console.log(`  ${i+1}. "${combination.name}" (ID: ${combination.id})`);
        });

        if (!dryRun) {
          console.log(`Creating these missing combinations...`);
          processNextCombination(missingCombinations, 0, existingCopyCategoryId, dryRun, stats, callback);
        } else {
          console.log(`[DRY RUN] Would create these ${missingCombinations.length} missing combinations.`);
          if (callback) callback();
        }
      } else {
        console.log(`All combinations from the original category have copies in the existing category copy.`);
        if (callback) callback();
      }
    });
  });
}

/**
 * Create a new combination category based on an existing one
 */
function createCombinationCategory(ibc, dryRun, skipExisting, stats, callback) {
  var newIBC = _.clone(ibc);

  // Remove the id property as a new one will be assigned by the database
  delete newIBC.id;

  // Update name by appending "(*)""
  newIBC.name = ibc.name.trim() + ' (*)';

  // Remove surcharges with ID 1310026 if it exists
  if (newIBC.surcharges && Array.isArray(newIBC.surcharges)) {
    const originalLength = newIBC.surcharges.length;
    newIBC.surcharges = newIBC.surcharges.filter(id => id !== 1310026);
    console.log(`Modified surcharges: ${originalLength} → ${newIBC.surcharges.length} (Removed 1310026)`);
  } else {
    console.log(`No surcharges to modify`);
  }

  // Update date_created to current time
  newIBC.date_created = new Date().toISOString().replace('T', ' ').replace('Z', '');

  if (!dryRun) {
    console.log(`Creating new combination category: "${newIBC.name}"...`);
    databaseConnection.obj.create('inventory_billable_combination_categories', newIBC, function(createdIBC) {
      stats.categoriesCreated++;
      console.log(`✅ CREATED: New combination category "${createdIBC.name}" (ID: ${createdIBC.id})`);

      // Now process the combinations for this category
      processCombinationsForCategory(ibc.id, createdIBC.id, dryRun, skipExisting, stats, callback);
    });
  } else {
    console.log(`[DRY RUN] Would create new combination category: "${newIBC.name}"`);
    // For dry run, still show what combinations would be processed
    processCombinationsForCategory(ibc.id, 'new-id-placeholder', dryRun, skipExisting, stats, callback);
  }
}

/**
 * Process all combinations for a given category
 */
function processCombinationsForCategory(originalCategoryId, newCategoryId, dryRun, skipExisting, stats, callback) {
  console.log(`\nProcessing combinations for category ID ${originalCategoryId}...`);

  databaseConnection.obj.getWhere('inventory_billable_combinations', {category: originalCategoryId}, function(ib_combinations) {
    console.log(`Found ${ib_combinations.length} combinations in the original category.`);
    stats.combinationsProcessed += ib_combinations.length;

    // Process combinations sequentially
    processNextCombination(ib_combinations, 0, newCategoryId, dryRun, stats, callback);
  });
}

/**
 * Process combinations one at a time to avoid server connection issues
 */
function processNextCombination(combinations, index, newCategoryId, dryRun, stats, callback) {
  if (index >= combinations.length) {
    console.log(`Completed processing all combinations for this category.`);
    if (callback) callback();
    return;
  }

  const ibc = combinations[index];
  const newCombinationName = ibc.name.trim() + ' (*)';

  // Check if this combination already exists in the new category (if not a dry run and not a placeholder ID)
  if (!dryRun && newCategoryId !== 'new-id-placeholder') {
    databaseConnection.obj.getWhere('inventory_billable_combinations', {
      category: newCategoryId,
      name: newCombinationName
    }, function(existingCombinations) {
      if (existingCombinations && existingCombinations.length > 0) {
        stats.combinationsSkipped++;
        console.log(`⚠️ SKIPPING COMBINATION: "${ibc.name}" already exists in the new category as "${newCombinationName}" (ID: ${existingCombinations[0].id})`);

        // Process the next combination after a short delay
        setTimeout(function() {
          processNextCombination(combinations, index + 1, newCategoryId, dryRun, stats, callback);
        }, 50);
      } else {
        // Combination doesn't exist, create it
        createCombination(ibc, newCategoryId, dryRun, stats, function() {
          // Process the next combination after a short delay
          setTimeout(function() {
            processNextCombination(combinations, index + 1, newCategoryId, dryRun, stats, callback);
          }, 50);
        });
      }
    });
  } else {
    // For dry run or when using placeholder ID, just simulate creation
    createCombination(ibc, newCategoryId, dryRun, stats, function() {
      // Process the next combination (no delay needed for dry run)
      processNextCombination(combinations, index + 1, newCategoryId, dryRun, stats, callback);
    });
  }
}

/**
 * Create a new combination based on an existing one
 */
function createCombination(ibc, newCategoryId, dryRun, stats, callback) {
  var newIBC = _.clone(ibc);

  // Remove the id property as a new one will be assigned by the database
  delete newIBC.id;

  // Update name by appending "(*)"
  newIBC.name = ibc.name.trim() + ' (*)';

  // Update category to point to the new category
  newIBC.category = newCategoryId;

  // Update date_created to current time
  newIBC.date_created = new Date().toISOString().replace('T', ' ').replace('Z', '');

  if (!dryRun) {
    console.log(`Creating new combination: "${newIBC.name}"...`);
    databaseConnection.obj.create('inventory_billable_combinations', newIBC, function(createdIBC) {
      stats.combinationsCreated++;
      console.log(`✅ CREATED: New combination "${createdIBC.name}" (ID: ${createdIBC.id})`);
      if (callback) callback();
    });
  } else {
    console.log(`[DRY RUN] Would create new combination: "${newIBC.name}"`);
    if (callback) callback();
  }
}
