# HubSpot Webhook Integration - Implementation Guide

## Overview

Simple webhook endpoint that receives HubSpot lead data and creates corresponding records in Bento. No complexity, no edge cases, just straightforward CRUD operations.

## Endpoint Configuration

**File Location**: `_SRC/pagoda/custom_scripts/infinityHubspot.php`
**URL**: `http://localhost:8080/api/custom_scripts/infinityHubspot.php`

## Environment Detection

```php
function getEnvironmentConfig() {
    $host = $_SERVER['HTTP_HOST'];

    if ($host === 'localhost:8080') {
        return [
            'instance' => 'rickyvoltz',
            'api_token' => '1ac04c9bfe10b64d1fa9cdff6216035d9795e2a1f46e3fb5df36d6c9a2ec1169fea0618fbc014a0ff8381a75974f1bb46c6e2c1dd9c40ff4db6594a122d9d5ca',
            'company_type_id' => 24,
            'contact_type_id' => 23,
            'email_contact_info_type_id' => 14,
            'lead_source_contact_info_type_id' => 17,
            'hq_id' => 3530,
            'client_notes_entity_type' => 63
        ];
    } else {
        return [
            'instance' => 'infinity',
            'api_token' => '102344ab0c1ee8d39394de54e2f0acef9b8b3097197143b856d747018555beef92a095b96a09338194a5ceb85091ab22ac04b16e56ce6b61b59f6cc8beaca011',
            'company_type_id' => 6004528,
            'contact_type_id' => 1929600,
            'email_contact_info_type_id' => 20600612,
            'lead_source_contact_info_type_id' => 20600613,
            'hq_id' => 1476971,
            'client_notes_entity_type' => 1826954
        ];
    }
}
```

## HubSpot Payload Format

```json
{
  "email": "<EMAIL>",
  "dealname": "Company Name Deal",
  "lastname": "Last",
  "firstname": "First",
  "lead_source": "Website",
  "hubspot_owner_id": 79618815
}
```

## Implementation Steps

### 1. Company Creation

```php
$company = $bento->create([
    'objectType' => 'companies',
    'objectData' => [
        'type' => $config['company_type_id'],
        'name' => $dealname,
        'tagged_with' => [$config['hq_id']],
        'data_source' => 'hubspot',
        'data_source_id' => $hubspot_owner_id
    ]
]);
```

### 2. Contact Creation

```php
$contact = $bento->create([
    'objectType' => 'contacts',
    'objectData' => [
        'type' => $config['contact_type_id'],
        'company' => $company['id'],
        'name' => $firstname . ' ' . $lastname,
        'fname' => $firstname,
        'lname' => $lastname,
        'tagged_with' => [$config['hq_id'], $company['id']],
        'data_source' => 'hubspot',
        'data_source_id' => $hubspot_owner_id
    ]
]);
```

### 3. Email Contact Info Creation

```php
$email_contact_info = $bento->create([
    'objectType' => 'contact_info',
    'objectData' => [
        'object_id' => $contact['id'],
        'object_type' => 'contact_info',
        'name' => 'Email Address',
        'title' => 'Email Address',
        'info' => $email,
        'type' => $config['email_contact_info_type_id'],
        'is_primary' => 'yes'
    ]
]);
```

### 4. Lead Source Contact Info Creation (if provided)

```php
if (!empty($lead_source)) {
    $lead_source_contact_info = $bento->create([
        'objectType' => 'contact_info',
        'objectData' => [
            'object_id' => $contact['id'],
            'object_type' => 'contact_info',
            'name' => 'Lead Source',
            'title' => 'Lead Source',
            'info' => $lead_source,
            'type' => $config['lead_source_contact_info_type_id'],
            'is_primary' => 'no'
        ]
    ]);
}
```

### 5. Client Notes Creation

```php
// Get Client Notes blueprint
$client_notes_entity = $bento->getById($config['client_notes_entity_type']);
$bp_name = $client_notes_entity['bp_name'];

$hubspot_details = '<h5><strong>HubSpot Lead Details:</strong></h5>';
$hubspot_details .= '<h5>Deal Name: ' . $dealname . '</h5>';
$hubspot_details .= '<h5>Contact: ' . $firstname . ' ' . $lastname . '</h5>';
$hubspot_details .= '<h5>Email: ' . $email . '</h5>';
$hubspot_details .= '<h5>Lead Source: ' . $lead_source . '</h5>';
$hubspot_details .= '<h5>HubSpot Owner ID: ' . $hubspot_owner_id . '</h5>';
$hubspot_details .= '<h5>Company ID: ' . $company['id'] . '</h5>';
$hubspot_details .= '<h5>Contact ID: ' . $contact['id'] . '</h5>';
$hubspot_details .= '<h5>Timestamp: ' . date('d F Y, h:i:s A') . '</h5>';

$client_notes = $bento->create([
    'objectType' => $bp_name,
    'objectData' => [
        'name' => 'HubSpot Lead - ' . $contact['name'],
        'parent' => $contact['id'],
        'tagged_with' => [$config['hq_id'], $contact['id'], $company['id']],
        [$client_notes_entity]['blueprint']['_17'] => $hubspot_details
    ]
]);
```

## Complete Implementation Template

```php
<?php
error_reporting(E_ERROR | E_PARSE);

header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');
header('Content-Type: application/json');

require_once '../apiExample/bento.php';

// Environment configuration
$config = getEnvironmentConfig();
$bento = new BentoAPI($config['instance'], $config['api_token']);

// Parse HubSpot payload
$json = file_get_contents("php://input");
$data = json_decode($json, true);

$firstname = $data['firstname'] ?? '';
$lastname = $data['lastname'] ?? '';
$email = $data['email'] ?? '';
$dealname = $data['dealname'] ?? '';
$lead_source = $data['lead_source'] ?? '';
$hubspot_owner_id = $data['hubspot_owner_id'] ?? '';

// Forward to Pipedream for logging
$ch = curl_init('https://eo8vqnch968fhpz.m.pipedream.net');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_exec($ch);
curl_close($ch);

// Create records (implementation steps 1-5 from above)

http_response_code(200);
echo json_encode(['status' => 'success', 'contact_id' => $contact['id'], 'company_id' => $company['id']]);
?>
```

## Testing

**Postman Configuration:**
- Method: POST
- URL: `http://localhost:8080/api/custom_scripts/infinityHubspot.php`
- Headers: `Content-Type: application/json`
- Body: Use HubSpot payload format above

## Notes

- No error handling needed - Bento API is production ready
- Network errors return appropriate HTTP status codes automatically
- All operations are straightforward CRUD - no complexity required
- Pipedream logging maintained for debugging
