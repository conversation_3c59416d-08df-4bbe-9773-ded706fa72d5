# HubSpot-Bento Webhook Integration - Technical Specification v2.0

## Document Purpose

This document provides a complete technical specification for implementing a production-ready HubSpot webhook integration that syncs contact data to the Bento CRM system. This specification is designed for handoff to developers and AI assistants working in both local development and production environments.

---

## Table of Contents

1. [System Overview](#system-overview)
2. [Architecture & Data Flow](#architecture--data-flow)
3. [Environment Configuration](#environment-configuration)
4. [Reference Implementation Patterns](#reference-implementation-patterns)
5. [Core Business Logic](#core-business-logic)
6. [Data Structures & Mappings](#data-structures--mappings)
7. [Error Handling & Logging](#error-handling--logging)
8. [Testing Strategy](#testing-strategy)
9. [Deployment Considerations](#deployment-considerations)
10. [Appendices](#appendices)

---

## 1. System Overview

### 1.1 Objective

Create a webhook endpoint that receives HubSpot deal/contact data and synchronizes it to Bento CRM with the following requirements:

- **Email-based deduplication**: Prevent duplicate contact records using email as the source of truth
- **Contact info linking**: Properly link contact_info records to parent contact records
- **Manager field mapping**: Map HubSpot Deal Owners to Bento Manager users
- **Company management**: Create or locate companies based on deal names
- **Audit trail**: Maintain comprehensive logging for debugging and compliance

### 1.2 Critical Success Criteria

1. **No duplicate contacts**: One email = one contact record in Bento
2. **Email searchability**: Contacts must be searchable by email in Bento interface
3. **Contract delivery**: Primary email must be set for contract delivery functionality
4. **Manager assignment**: HubSpot Deal Owner correctly maps to Bento Manager field
5. **Idempotency**: Re-sending the same webhook should not create duplicate records

### 1.3 System Context

```
HubSpot → [Pipedream (Dev)] → Bento Webhook Endpoint → Bento API → Bento Database
                  ↓
             Direct (Prod) → Bento Webhook Endpoint → Bento API → Bento Database
```

---

## 2. Architecture & Data Flow

### 2.1 High-Level Process Flow

```
1. Receive Webhook
   ↓
2. Parse & Validate Payload
   ↓
3. Company Deduplication & Create/Locate
   ↓
4. Contact Deduplication & Create/Update
   ↓
5. Contact Info Creation (Email, Lead Source)
   ↓
6. Link Contact Info to Contact (CRITICAL STEP)
   ↓
7. Create System Note (Audit Trail)
   ↓
8. Return Success Response
```

### 2.2 Key Design Decisions

#### 2.2.1 Email as Primary Key
**Decision**: Use email address as the unique identifier for contact deduplication.

**Rationale**:
- Names are unreliable (John Doe vs Johnny Doe vs J. Doe)
- HubSpot payload does not contain reliable contact IDs
- Email is what sales team searches by in practice
- Email is required for contract delivery functionality

**Implementation**:
```php
'data_source_hash' => $email,      // Store email as string
'data_source' => crc32($email)     // Store email hash as integer for indexing
```

#### 2.2.2 Contact Info Relationship Pattern
**Decision**: Create contact_info records first, then update parent contact with IDs.

**Rationale**:
- Bento's data model requires bidirectional relationship
- `contact_info.object_id` points TO contact (created during contact_info creation)
- `contact.contact_info` array contains contact_info IDs (requires update after creation)

**Critical Pattern** (from Salesforce reference implementation):
```php
// Step 1: Create contact_info records
$contactInfoIds = [];
foreach($contactInfoObjs as $info) {
    $contactInfoIds[] = $bento->create($info)['id'];
}

// Step 2: Update contact with contact_info IDs
$bento->update([
    'objectType' => 'contacts',
    'objectData' => [
        'id' => $contact['id'],
        'contact_info' => $contactInfoIds  // ← CRITICAL: This enables searchability
    ]
]);
```

#### 2.2.3 Global vs Scoped Deduplication
**Decision**: Search for existing contacts globally by email, not scoped to company.

**Rationale**:
- Contacts may change companies but retain email addresses
- Email should be globally unique in the system
- Avoids edge cases where contact exists but company changed
- Simpler implementation and reasoning

---

## 3. Environment Configuration

### 3.1 File Location
```
Production: /Users/<USER>/Infinity/bento/_SRC/pagoda/custom_scripts/infinityHubspot.php
Development: Same path in Docker container
```

### 3.2 Environment Detection & Configuration

The webhook must support two environments with different data:

```php
function getEnvironmentConfig() {
    $host = $_SERVER['HTTP_HOST'];

    if ($host === 'localhost:8080') {
        // DEVELOPMENT ENVIRONMENT
        return [
            'instance' => 'rickyvoltz',
            'api_token' => '1ac04c9bfe10b64d1fa9cdff6216035d9795e2a1f46e3fb5df36d6c9a2ec1169fea0618fbc014a0ff8381a75974f1bb46c6e2c1dd9c40ff4db6594a122d9d5ca',

            // Type IDs (must be discovered in dev instance)
            'company_type_id' => 24,
            'contact_type_id' => 23,
            'email_contact_info_type_id' => 14,
            'lead_source_contact_info_type_id' => 17,

            // Structural IDs
            'hq_id' => 3530,
            'client_notes_entity_type' => 63,

            // Manager Mapping (dev users)
            'hubspot_managers' => [
                '79905155' => 100,  // Alli Casebeer (dev equivalent)
                '79618815' => 101,  // Mary Topp (dev equivalent)
                '79905153' => 102   // Cameron Creasy (dev equivalent)
            ],
            'default_manager' => 101  // Mary Topp equivalent
        ];
    } else {
        // PRODUCTION ENVIRONMENT (Infinity Instance)
        return [
            'instance' => 'infinity',
            'api_token' => '102344ab0c1ee8d39394de54e2f0acef9b8b3097197143b856d747018555beef92a095b96a09338194a5ceb85091ab22ac04b16e56ce6b61b59f6cc8beaca011',

            // Type IDs (Infinity specific)
            'company_type_id' => 6004528,
            'contact_type_id' => 1929600,
            'email_contact_info_type_id' => 20600612,
            'lead_source_contact_info_type_id' => 20600613,

            // Structural IDs
            'hq_id' => 1476971,
            'client_notes_entity_type' => 1826954,

            // Manager Mapping (production users)
            'hubspot_managers' => [
                '79905155' => 5461358,   // Alli Casebeer
                '79618815' => 20295118,  // Mary Topp
                '79905153' => 17220899   // Cameron Creasy
            ],
            'default_manager' => 20295118  // Mary Topp
        ];
    }
}
```

### 3.3 Configuration Strategy

**Approach**: Dual environment configuration with whitelisted values.

**Advantages**:
- No runtime discovery needed
- Explicit mapping of all environment-specific values
- Easy to maintain and update
- Clear separation of dev vs prod data

**Disadvantages**:
- Requires manual ID discovery for each environment
- Dev and prod configurations must be kept in sync structurally

**Alternative Considered**: Runtime discovery of IDs by querying Bento API for type names.
**Rejected Because**: Adds complexity, potential for runtime failures, slower execution.

### 3.4 Required Configuration Discovery

Before implementation, the following IDs must be discovered in the development environment:

#### 3.4.1 Object Type IDs
Query the Bento instance to find:
- Company type ID for "Client" or equivalent
- Contact type ID for "Contact" or equivalent
- Contact info type ID for "Email"
- Contact info type ID for "Lead Source"

**Discovery Method**:
```javascript
// In browser console on Bento dev instance:
// Find company types
fetch('/api/companies/types').then(r => r.json()).then(console.log)

// Find contact types
fetch('/api/contacts/types').then(r => r.json()).then(console.log)

// Find contact_info types
fetch('/api/contact_info/types').then(r => r.json()).then(console.log)
```

#### 3.4.2 Structural IDs
- HQ parent ID (organization root)
- Team/tag IDs for company and contact tagging
- Client notes entity type ID

#### 3.4.3 User Mappings
Create three test users in development environment corresponding to:
- Alli Casebeer (HubSpot Owner ID: 79905155)
- Mary Topp (HubSpot Owner ID: 79618815)
- Cameron Creasy (HubSpot Owner ID: 79905153)

Record their Bento user IDs for the configuration mapping.

---

## 4. Reference Implementation Patterns

### 4.1 Salesforce Integration Pattern

The Salesforce webhook implementation at `/Users/<USER>/Infinity/bento/_SRC/pagoda/incoming/fg_salesforce.php` serves as the reference implementation for this project.

#### 4.1.1 Key Patterns to Follow

**Pattern 1: Deduplication with Fallback**
```php
// Try specific ID first
$organization = $bento->getWhere([
    'objectType' => 'companies',
    'queryObj' => [
        'data_source_hash' => $external_id,
        'data_source' => crc32($external_id)
    ]
])[0];

// Fallback to name search
if (empty($organization)) {
    $organization = $bento->getWhere([
        'objectType' => 'companies',
        'queryObj' => ['name' => $organization_name]
    ])[0];
}
```

**Pattern 2: Contact Info Array Management**
```php
// Build array of contact_info objects to create
$contactInfoObjs = [];

if (!empty($email)) {
    array_push($contactInfoObjs, [
        'objectType' => 'contact_info',
        'objectData' => [
            'object_id' => $contact['id'],
            'object_type' => 'contacts',
            'name' => 'Email Address',
            'title' => 'Email Address',
            'info' => $email,
            'type' => $email_type_id,
            'is_primary' => 'yes'
        ]
    ]);
}

// Deduplicate (important!)
$contactInfoObjs = _.uniq($contactInfoObjs);
```

**Pattern 3: ID Collection and Update**
```php
if (!empty($contactInfoObjs)) {
    $contactInfoIds = [];

    foreach($contactInfoObjs as $info) {
        $contactInfoIds[] = $bento->create($info)['id'];
    }

    // CRITICAL UPDATE STEP
    $bento->update([
        'objectType' => 'contacts',
        'objectData' => [
            'id' => $contact['id'],
            'contact_info' => $contactInfoIds
        ]
    ]);
}
```

**Pattern 4: Underscore.php Functional Operations**
```php
require_once '../lib/_.php';

// Use underscore for array operations
$accountType = __::find($bp_options, function($option) use($data) {
    return $option['name'] == $data['type'] && !$option['is_archived'];
});

// Use for uniqueness
$contactInfoObjs = _.uniq($contactInfoObjs);
```

**Pattern 5: System Notes for Audit Trail**
```php
$noteBody = '<h5><strong>HubSpot Lead Details:</strong></h5>';
$noteBody .= '<h5>Deal Name: ' . $dealname . '</h5>';
$noteBody .= '<h5>Contact: ' . $firstname . ' ' . $lastname . '</h5>';
$noteBody .= '<h5>Email: ' . $email . '</h5>';
$noteBody .= '<h5>Timestamp: '. date('d F Y, h:i:s A') .'</h5>';

$note = [
    'record_type' => 'log',
    'record_type_name' => 'System Log',
    'activity_type' => 'Create',
    'type_id' => $contact['id'],
    'tagged_with' => [$hq_id, $company['id'], $contact['id']],
    'note' => $noteBody,
    'author' => 0
];

$bento->create([
    'objectType' => 'notes',
    'objectData' => $note
]);
```

#### 4.1.2 Patterns to Avoid

**Anti-Pattern 1**: Creating contact_info without updating parent
```php
// WRONG - This creates orphaned contact_info
$bento->create(['objectType' => 'contact_info', 'objectData' => [...]]);
// Missing: Update contact with contact_info IDs
```

**Anti-Pattern 2**: Assuming names are unique
```php
// WRONG - Names are not unique identifiers
$existing = $bento->getWhere(['queryObj' => ['name' => $fullname]]);
```

**Anti-Pattern 3**: Not handling empty responses
```php
// WRONG - Accessing array index without checking
$user = $bento->getWhere([...])[0];
$userId = $user['id'];  // Fatal error if empty
```

### 4.2 BentoAPI Usage Patterns

#### 4.2.1 Initialization
```php
require_once '../apiExample/bento.php';
$bento = new BentoAPI($instance, $api_token);
```

#### 4.2.2 Create Operation
```php
$result = $bento->create([
    'objectType' => 'contacts',  // or 'companies', 'contact_info', etc.
    'objectData' => [
        'name' => 'John Doe',
        'type' => 123456,
        // ... other fields
    ]
]);

// Returns: ['id' => 789, 'name' => 'John Doe', ...]
```

#### 4.2.3 Update Operation
```php
$result = $bento->update([
    'objectType' => 'contacts',
    'objectData' => [
        'id' => 789,  // Required
        'contact_info' => [101, 102, 103]  // Field to update
    ]
]);
```

#### 4.2.4 Query Operation
```php
$results = $bento->getWhere([
    'objectType' => 'contacts',
    'queryObj' => [
        'data_source_hash' => '<EMAIL>',
        'data_source' => crc32('<EMAIL>')
    ]
]);

// Returns: array of matching objects, or empty array
```

---

## 5. Core Business Logic

### 5.1 Complete Processing Algorithm

```
ALGORITHM: ProcessHubSpotWebhook(payload)

INPUT: HubSpot webhook payload containing deal and contact data
OUTPUT: Success/error response with created/updated record IDs

PROCEDURE:

1. PARSE_PAYLOAD(payload) → extract fields
   - firstname, lastname, email, dealname, lead_source, hubspot_owner_id

2. RESOLVE_MANAGER(hubspot_owner_id, config)
   - IF hubspot_owner_id IN config.hubspot_managers THEN
       manager_id ← config.hubspot_managers[hubspot_owner_id]
   - ELSE
       manager_id ← config.default_manager
   - RETURN manager_id

3. RESOLVE_OR_CREATE_COMPANY(dealname, config)
   - existing ← QUERY_BENTO(companies, {name: dealname})
   - IF existing IS_EMPTY THEN
       company ← CREATE_BENTO(companies, {
           name: dealname,
           type: config.company_type_id,
           parent: config.hq_id,
           tagged_with: [config.hq_id],
           data_source: crc32('hubspot'),
           data_source_id: crc32(dealname)
       })
   - ELSE
       company ← existing[0]
   - RETURN company

4. RESOLVE_OR_UPDATE_CONTACT(email, firstname, lastname, company_id, manager_id, config)
   - existing ← QUERY_BENTO(contacts, {
       data_source_hash: email,
       data_source: crc32(email)
   })

   - IF existing IS_EMPTY THEN
       // Create new contact
       contact ← CREATE_BENTO(contacts, {
           name: firstname + ' ' + lastname,
           fname: firstname,
           lname: lastname,
           type: config.contact_type_id,
           company: company_id,
           parent: company_id,
           manager: manager_id,
           tagged_with: [config.hq_id, company_id],
           data_source_hash: email,
           data_source: crc32(email)
       })
       is_new ← TRUE
   - ELSE
       // Update existing contact
       contact ← existing[0]
       UPDATE_BENTO(contacts, {
           id: contact.id,
           fname: firstname,
           lname: lastname,
           company: company_id,
           manager: manager_id
       })
       is_new ← FALSE

   - RETURN (contact, is_new)

5. CREATE_CONTACT_INFO_RECORDS(contact_id, email, lead_source, config)
   - contact_info_objects ← []

   - // Email contact_info
   - IF email IS_NOT_EMPTY THEN
       APPEND_TO contact_info_objects: {
           object_id: contact_id,
           object_type: 'contacts',
           name: 'Email Address',
           title: 'Email Address',
           info: email,
           type: config.email_contact_info_type_id,
           is_primary: 'yes',
           data_source: crc32('hubspot'),
           data_source_id: crc32(email)
       }

   - // Lead source contact_info
   - IF lead_source IS_NOT_EMPTY THEN
       APPEND_TO contact_info_objects: {
           object_id: contact_id,
           object_type: 'contacts',
           name: 'Lead Source',
           title: 'Lead Source',
           info: lead_source,
           type: config.lead_source_contact_info_type_id,
           is_primary: 'yes',
           data_source: crc32('hubspot'),
           data_source_id: crc32(lead_source)
       }

   - // Create all contact_info records
   - contact_info_ids ← []
   - FOR EACH info_obj IN contact_info_objects DO
       created ← CREATE_BENTO(contact_info, info_obj)
       APPEND_TO contact_info_ids: created.id

   - RETURN contact_info_ids

6. LINK_CONTACT_INFO_TO_CONTACT(contact_id, contact_info_ids)
   - // CRITICAL STEP: Update contact with contact_info IDs
   - UPDATE_BENTO(contacts, {
       id: contact_id,
       contact_info: contact_info_ids
   })

7. CREATE_SYSTEM_NOTE(contact_id, company_id, payload_data, config)
   - note_body ← FORMAT_HTML_NOTE(payload_data)
   - CREATE_BENTO(notes, {
       record_type: 'log',
       record_type_name: 'System Log',
       activity_type: 'Create',
       type_id: contact_id,
       tagged_with: [config.hq_id, company_id, contact_id],
       note: note_body,
       author: 0
   })

8. RETURN_SUCCESS_RESPONSE({
   company_id,
   contact_id,
   contact_info_ids,
   is_new_contact,
   timestamp
})

END PROCEDURE
```

### 5.2 Deduplication Logic Specification

#### 5.2.1 Contact Deduplication by Email

**Primary Key**: Email address stored in `data_source_hash`

**Search Strategy**:
```php
// Step 1: Search by email in data_source fields
$existing_contact = $bento->getWhere([
    'objectType' => 'contacts',
    'queryObj' => [
        'data_source_hash' => $email,
        'data_source' => crc32($email)
    ]
]);
```

**Decision Tree**:
```
IF existing_contact IS_EMPTY:
    → Create new contact
    → Set data_source_hash = email
    → Set data_source = crc32(email)
ELSE:
    → Use existing_contact[0]
    → Update fname, lname, company, manager
    → Create NEW contact_info records (with is_primary = 'yes')
    → Append new contact_info IDs to existing contact_info array
```

**Critical Implementation Detail**: When updating an existing contact with new contact_info:
```php
// Get existing contact_info IDs
$existing_contact_info = $existing_contact['contact_info'] ?? [];

// Create new contact_info records
$new_contact_info_ids = [...]; // From creation

// Merge arrays, ensuring new is_primary replaces old primary
$all_contact_info_ids = array_merge($existing_contact_info, $new_contact_info_ids);

// Update contact with complete array
$bento->update([
    'objectType' => 'contacts',
    'objectData' => [
        'id' => $existing_contact['id'],
        'contact_info' => $all_contact_info_ids
    ]
]);
```

#### 5.2.2 Company Deduplication by Name

**Primary Key**: Company name (dealname from HubSpot)

**Search Strategy**:
```php
$existing_company = $bento->getWhere([
    'objectType' => 'companies',
    'queryObj' => ['name' => $dealname]
]);
```

**Decision Tree**:
```
IF existing_company IS_EMPTY:
    → Create new company
    → Set name = dealname
ELSE:
    → Use existing_company[0]
    → Do NOT update (companies are considered stable)
```

**Rationale**: Companies change less frequently than contacts. If a company with the same name exists, we assume it's the same entity. Manual merging can be done by sales team if needed.

### 5.3 Manager Resolution Logic

**Input**: `hubspot_owner_id` from webhook payload

**Algorithm**:
```php
function resolveManager($hubspot_owner_id, $config) {
    if (isset($config['hubspot_managers'][$hubspot_owner_id])) {
        return $config['hubspot_managers'][$hubspot_owner_id];
    }

    // Default to Mary Topp if not found
    return $config['default_manager'];
}
```

**Fallback Behavior**: If HubSpot Owner ID is not in the mapping, default to Mary Topp.

**Future Enhancement**: Log a warning when using default manager for monitoring/reporting.

### 5.4 Contact Info Handling

#### 5.4.1 Primary Email Behavior

**Rule**: The most recent email from HubSpot is always marked as `is_primary = 'yes'`.

**Implication**: If contact already exists with a primary email, the new email becomes primary. The old primary email remains but is no longer primary.

**Implementation**:
```php
// Email contact_info always has is_primary = 'yes'
[
    'object_id' => $contact['id'],
    'object_type' => 'contacts',
    'name' => 'Email Address',
    'title' => 'Email Address',
    'info' => $email,
    'type' => $config['email_contact_info_type_id'],
    'is_primary' => 'yes'  // ← Always 'yes' for email
]
```

**Rationale**:
- Contract delivery requires a primary email
- Most recent data from HubSpot is considered authoritative
- Historical emails are preserved but not primary

#### 5.4.2 Lead Source Handling

**Rule**: Lead source is optional. Only create if provided in payload.

**Implementation**:
```php
$lead_source_contact_info = null;

if (!empty($lead_source)) {
    $lead_source_contact_info = $bento->create([
        'objectType' => 'contact_info',
        'objectData' => [
            'object_id' => $contact['id'],
            'object_type' => 'contacts',
            'name' => 'Lead Source',
            'title' => 'Lead Source',
            'info' => $lead_source,
            'type' => $config['lead_source_contact_info_type_id'],
            'is_primary' => 'yes'
        ]
    ]);
}
```

---

## 6. Data Structures & Mappings

### 6.1 HubSpot Payload Structure

**Expected Input Format**:
```json
{
    "firstname": "John",
    "lastname": "Doe",
    "email": "<EMAIL>",
    "dealname": "Example Corp Deal",
    "lead_source": "Website Form",
    "hubspot_owner_id": "79618815"
}
```

**Field Definitions**:
- `firstname` (string, required): Contact's first name
- `lastname` (string, required): Contact's last name
- `email` (string, required): Contact's email address (primary key for deduplication)
- `dealname` (string, required): Name of the deal, used as company name
- `lead_source` (string, optional): Source of the lead (e.g., "Website Form", "Referral")
- `hubspot_owner_id` (string, optional): HubSpot user ID of deal owner

**Validation Requirements**:
- Email must be valid email format
- Firstname and lastname must be non-empty strings
- Dealname must be non-empty string
- hubspot_owner_id can be null/empty (will use default manager)

### 6.2 Bento Object Structures

#### 6.2.1 Company Object
```php
[
    'objectType' => 'companies',
    'objectData' => [
        'type' => (int),           // Company type ID from config
        'name' => (string),        // Deal name from HubSpot
        'parent' => (int),         // HQ ID from config
        'tagged_with' => [(int)],  // Array of tag IDs
        'data_source' => (int),    // crc32('hubspot')
        'data_source_id' => (int)  // crc32($dealname) for tracking
    ]
]
```

#### 6.2.2 Contact Object
```php
[
    'objectType' => 'contacts',
    'objectData' => [
        'type' => (int),               // Contact type ID from config
        'company' => (int),            // Company ID from previous step
        'parent' => (int),             // Company ID (same as company)
        'name' => (string),            // "Firstname Lastname"
        'fname' => (string),           // First name
        'lname' => (string),           // Last name
        'manager' => (int),            // Manager user ID from mapping
        'tagged_with' => [(int)],      // [HQ ID, Company ID]
        'data_source_hash' => (string), // Email address
        'data_source' => (int),        // crc32($email)
        'contact_info' => [(int)]      // Array of contact_info IDs (set via update)
    ]
]
```

#### 6.2.3 Contact Info Object (Email)
```php
[
    'objectType' => 'contact_info',
    'objectData' => [
        'object_id' => (int),          // Contact ID
        'object_type' => 'contacts',   // Literal string 'contacts'
        'name' => 'Email Address',     // Literal string
        'title' => 'Email Address',    // Literal string
        'info' => (string),            // Email address
        'type' => (int),               // Email contact_info type ID from config
        'is_primary' => 'yes',         // Literal string 'yes'
        'data_source' => (int),        // crc32('hubspot')
        'data_source_id' => (int)      // crc32($email)
    ]
]
```

#### 6.2.4 Contact Info Object (Lead Source)
```php
[
    'objectType' => 'contact_info',
    'objectData' => [
        'object_id' => (int),          // Contact ID
        'object_type' => 'contacts',   // Literal string 'contacts'
        'name' => 'Lead Source',       // Literal string
        'title' => 'Lead Source',      // Literal string
        'info' => (string),            // Lead source value
        'type' => (int),               // Lead source contact_info type ID from config
        'is_primary' => 'yes',         // Literal string 'yes'
        'data_source' => (int),        // crc32('hubspot')
        'data_source_id' => (int)      // crc32($lead_source)
    ]
]
```

#### 6.2.5 System Note Object
```php
[
    'objectType' => 'notes',
    'objectData' => [
        'record_type' => 'log',                // Literal string
        'record_type_name' => 'System Log',    // Literal string
        'activity_type' => 'Create',           // Or 'Update' for existing contacts
        'type_id' => (int),                    // Contact ID
        'tagged_with' => [(int)],              // [HQ ID, Company ID, Contact ID]
        'note' => (string),                    // HTML formatted note body
        'author' => 0                          // System user
    ]
]
```

### 6.3 Manager Mapping Table

**Production (Infinity Instance)**:
```php
'hubspot_managers' => [
    '79905155' => 5461358,   // Alli Casebeer
    '79618815' => 20295118,  // Mary Topp (default)
    '79905153' => 17220899   // Cameron Creasy
]
```

**Development (rickyvoltz Instance)**:
```php
'hubspot_managers' => [
    '79905155' => [DEV_USER_ID_1],  // Alli equivalent
    '79618815' => [DEV_USER_ID_2],  // Mary equivalent (default)
    '79905153' => [DEV_USER_ID_3]   // Cameron equivalent
]
```

**Maintenance**: When adding new HubSpot users, add their Owner ID and corresponding Bento user ID to both production and development mappings.

---

## 7. Error Handling & Logging

### 7.1 Error Handling Strategy

#### 7.1.1 Error Categories

**Category 1: Fatal Errors** (Return HTTP 500, log to MailSpon)
- BentoAPI connection failure
- Missing required configuration values
- Invalid API token
- Database connection errors

**Category 2: Validation Errors** (Return HTTP 400, log to Pipedream)
- Missing required fields (email, firstname, lastname, dealname)
- Invalid email format
- Empty payload

**Category 3: Business Logic Warnings** (Return HTTP 200, log to Pipedream)
- Unknown HubSpot Owner ID (use default manager)
- Duplicate webhook (already processed)
- Empty lead_source field

#### 7.1.2 Error Handling Pattern

```php
try {
    // Main processing logic

} catch (Exception $e) {
    // Determine error severity
    $error_payload = [
        'error_type' => 'fatal',
        'error_message' => $e->getMessage(),
        'error_trace' => $e->getTraceAsString(),
        'payload_data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ];

    // Log to Pipedream
    httpPost("https://eowsqc6detxmwqb.m.pipedream.net", $error_payload);

    // Send critical errors to MailSpon
    if (isCriticalError($e)) {
        httpPost("https://api.mailspons.com/send", [
            'to' => '<EMAIL>',
            'subject' => 'HubSpot Webhook Critical Error',
            'body' => json_encode($error_payload, JSON_PRETTY_PRINT)
        ]);
    }

    // Return appropriate HTTP response
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
    exit;
}
```

### 7.2 Logging Requirements

#### 7.2.1 Pipedream Debug Logging (Development)

**Endpoint**: `https://eowsqc6detxmwqb.m.pipedream.net`

**Log Events**:
1. Webhook received
2. Payload parsed
3. Manager resolved
4. Company found/created
5. Contact found/updated/created
6. Contact info created
7. Contact info linked
8. System note created
9. Success response

**Log Format**:
```php
$debug_payload = [
    'step' => 'company_resolution',
    'company_id' => $company['id'],
    'company_name' => $company['name'],
    'was_created' => $was_created,
    'timestamp' => date('Y-m-d H:i:s')
];

httpPost("https://eowsqc6detxmwqb.m.pipedream.net", $debug_payload);
```

#### 7.2.2 MailSpon Alert System (Critical Errors Only)

**Endpoint**: Custom MailSpon delivery

**Trigger Conditions**:
- BentoAPI failure (unable to create/update records)
- Configuration error (missing required IDs)
- Repeated failures (same error 3+ times in 1 hour)

**Email Format**:
```
To: <EMAIL>
Subject: HubSpot Webhook Critical Failure

Error Type: [Fatal/Configuration/Repeated]
Error Message: [Exception message]
Timestamp: [ISO 8601]
Payload: [JSON dump]
Stack Trace: [Full trace]
```

#### 7.2.3 Bento System Notes (Audit Trail)

**Purpose**: Maintain permanent audit trail within Bento for every webhook processed.

**Note Format**:
```html
<h5><strong>HubSpot Lead Processed:</strong></h5>
<h5>Action: [Created/Updated] Contact</h5>
<h5>Deal Name: [dealname]</h5>
<h5>Contact: [firstname lastname]</h5>
<h5>Email: [email]</h5>
<h5>Lead Source: [lead_source]</h5>
<h5>HubSpot Owner: [hubspot_owner_id]</h5>
<h5>Assigned Manager: [manager_name]</h5>
<h5>Company ID: [company_id]</h5>
<h5>Contact ID: [contact_id]</h5>
<h5>Contact Info IDs: [comma-separated IDs]</h5>
<h5>Timestamp: [formatted datetime]</h5>
```

**Tagged With**: HQ ID, Company ID, Contact ID (for searchability)

### 7.3 Success Response Format

```json
{
    "status": "success",
    "message": "HubSpot webhook processed successfully",
    "action": "created|updated",
    "timestamp": "2025-09-30 14:30:00",
    "created_records": {
        "company": {
            "id": 123456,
            "name": "Example Corp Deal",
            "was_created": true
        },
        "contact": {
            "id": 789012,
            "name": "John Doe",
            "email": "<EMAIL>",
            "was_created": true
        },
        "contact_info": {
            "email": {
                "id": 345678,
                "info": "<EMAIL>"
            },
            "lead_source": {
                "id": 345679,
                "info": "Website Form"
            }
        },
        "system_note": {
            "id": 901234
        }
    },
    "environment": "infinity"
}
```

---

## 8. Testing Strategy

### 8.1 Development Environment Challenges

**Problem**: Cannot snapshot production data to local development environment.

**Constraints**:
- Different instance names (infinity vs rickyvoltz)
- Different object type IDs
- Different user IDs
- Different structural IDs (HQ, teams, etc.)
- Dependency chains (contact_info_types, contact_types, etc.)

### 8.2 Proposed Testing Approach

#### 8.2.1 Minimal Test Data Creation

**Strategy**: Manually create minimal required data in development environment to mirror production structure.

**Required Objects**:

1. **Users** (3 test users):
   - Alli Casebeer equivalent
   - Mary Topp equivalent
   - Cameron Creasy equivalent

2. **Contact Types** (1 type):
   - Name: "Contact" or "Lead"
   - Record the type ID

3. **Contact Info Types** (2 types):
   - Name: "Email" or "Email Address"
   - Name: "Lead Source"
   - Record both type IDs

4. **Company Types** (1 type):
   - Name: "Client" or "Prospect"
   - Record the type ID

5. **HQ/Parent Organization**:
   - Create a root company to serve as HQ
   - Record the ID

**Creation Script** (to run in Bento dev environment):
```javascript
// In browser console on development Bento instance

// 1. Create test users
await createTestUsers([
    {fname: "Alli", lname: "Casebeer"},
    {fname: "Mary", lname: "Topp"},
    {fname: "Cameron", lname: "Creasy"}
]);

// 2. Create contact type
const contactType = await createContactType("Lead");

// 3. Create contact info types
const emailType = await createContactInfoType("Email");
const leadSourceType = await createContactInfoType("Lead Source");

// 4. Create company type
const companyType = await createCompanyType("Prospect");

// 5. Create HQ company
const hq = await createCompany({name: "Test HQ", type: companyType.id});

// Record all IDs for configuration
console.log({
    contact_type_id: contactType.id,
    email_contact_info_type_id: emailType.id,
    lead_source_contact_info_type_id: leadSourceType.id,
    company_type_id: companyType.id,
    hq_id: hq.id,
    users: [alliId, maryId, cameronId]
});
```

#### 8.2.2 Test Case Matrix

**Test Case 1: New Contact, New Company**
```json
{
    "firstname": "John",
    "lastname": "Doe",
    "email": "<EMAIL>",
    "dealname": "Test Company Alpha",
    "lead_source": "Website",
    "hubspot_owner_id": "79618815"
}
```
**Expected Outcome**:
- New company created: "Test Company Alpha"
- New contact created: "John Doe"
- 2 contact_info records created (email, lead source)
- Contact searchable by email in Bento
- Manager field set to Mary Topp

**Test Case 2: Existing Contact (Duplicate Email)**
```json
{
    "firstname": "John",
    "lastname": "Doe",
    "email": "<EMAIL>",
    "dealname": "Test Company Beta",
    "lead_source": "Referral",
    "hubspot_owner_id": "79905155"
}
```
**Expected Outcome**:
- No duplicate contact created
- Existing contact updated with new company
- New contact_info records created and appended
- Manager updated to Alli Casebeer
- Original contact_info still exists

**Test Case 3: Missing Lead Source**
```json
{
    "firstname": "Jane",
    "lastname": "Smith",
    "email": "<EMAIL>",
    "dealname": "Test Company Gamma",
    "hubspot_owner_id": "79905153"
}
```
**Expected Outcome**:
- New contact created
- Only email contact_info created (no lead source)
- No errors thrown
- Manager set to Cameron Creasy

**Test Case 4: Unknown HubSpot Owner**
```json
{
    "firstname": "Bob",
    "lastname": "Johnson",
    "email": "<EMAIL>",
    "dealname": "Test Company Delta",
    "lead_source": "Cold Call",
    "hubspot_owner_id": "99999999"
}
```
**Expected Outcome**:
- New contact created
- Manager defaulted to Mary Topp
- Warning logged to Pipedream
- System note indicates default manager used

**Test Case 5: Existing Company**
```json
{
    "firstname": "Alice",
    "lastname": "Williams",
    "email": "<EMAIL>",
    "dealname": "Test Company Alpha",
    "lead_source": "Partner",
    "hubspot_owner_id": "79618815"
}
```
**Expected Outcome**:
- Existing company reused (not duplicated)
- New contact created and linked to existing company
- Both contacts show under same company in Bento

**Test Case 6: Validation Error (Missing Email)**
```json
{
    "firstname": "Charlie",
    "lastname": "Brown",
    "dealname": "Test Company Epsilon",
    "hubspot_owner_id": "79618815"
}
```
**Expected Outcome**:
- HTTP 400 response
- Error message: "Email is required"
- No records created
- Error logged to Pipedream

#### 8.2.3 Verification Checklist

After each test case, verify:

- [ ] Contact exists in Bento contacts table
- [ ] Contact is searchable by email in Bento UI
- [ ] Contact has correct manager assigned
- [ ] Contact is linked to correct company
- [ ] Email contact_info exists and is marked primary
- [ ] Lead source contact_info exists (if provided)
- [ ] Contact's `contact_info` array contains correct IDs
- [ ] System note created with correct details
- [ ] No duplicate contacts with same email
- [ ] Logging shows all processing steps
- [ ] HTTP 200 response received
- [ ] Response JSON contains all created IDs

### 8.3 Manual Testing Procedure

**Step 1: Environment Setup**
```bash
# Start Docker development environment
cd /Users/<USER>/Infinity/bento
docker-compose up

# Access at localhost:8080
```

**Step 2: Configuration Verification**
```php
// Test configuration loading
$config = getEnvironmentConfig();
var_dump($config);

// Verify all required IDs are present
assert(isset($config['company_type_id']));
assert(isset($config['contact_type_id']));
assert(isset($config['email_contact_info_type_id']));
assert(isset($config['lead_source_contact_info_type_id']));
assert(isset($config['hubspot_managers']));
```

**Step 3: Send Test Webhooks**
```bash
# Use curl or Postman to send test payloads
curl -X POST http://localhost:8080/custom_scripts/infinityHubspot.php \
  -H "Content-Type: application/json" \
  -d '{
    "firstname": "John",
    "lastname": "Doe",
    "email": "<EMAIL>",
    "dealname": "Test Company Alpha",
    "lead_source": "Website",
    "hubspot_owner_id": "79618815"
  }'
```

**Step 4: Verify in Bento UI**
- Navigate to Contacts
- Search for "<EMAIL>"
- Verify contact appears
- Open contact detail
- Verify manager field populated
- Verify email appears in contact info section
- Verify email searchable

**Step 5: Check Logs**
- Review Pipedream logs for all processing steps
- Verify no errors in execution
- Check system notes in Bento

### 8.4 Automated Testing (Future Enhancement)

**PHPUnit Test Structure**:
```php
class HubSpotWebhookTest extends TestCase {

    public function testNewContactCreation() {
        // Arrange
        $payload = [...];

        // Act
        $response = processWebhook($payload);

        // Assert
        $this->assertEquals(200, $response['status_code']);
        $this->assertNotNull($response['contact_id']);
    }

    public function testEmailDeduplication() {
        // Create contact with email
        // Send webhook with same email
        // Assert only one contact exists
    }

    public function testContactInfoLinking() {
        // Create contact and contact_info
        // Assert contact.contact_info contains correct IDs
    }
}
```

---

## 9. Deployment Considerations

### 9.1 Pre-Deployment Checklist

**Configuration**:
- [ ] All production type IDs discovered and configured
- [ ] All production user IDs mapped to HubSpot Owner IDs
- [ ] Production API token verified and tested
- [ ] Production HQ ID and structural IDs configured

**Code Quality**:
- [ ] All error handling implemented
- [ ] All logging implemented
- [ ] Code follows Salesforce integration patterns
- [ ] Contact info linking update step included
- [ ] Email deduplication logic implemented
- [ ] Manager mapping logic implemented

**Testing**:
- [ ] All test cases pass in development
- [ ] Manual verification completed
- [ ] Edge cases tested (missing fields, duplicates, etc.)
- [ ] Pipedream logging verified
- [ ] MailSpon alerts tested

**Documentation**:
- [ ] Deployment notes prepared
- [ ] Rollback procedure documented
- [ ] Monitoring dashboard configured

### 9.2 Deployment Steps

**Step 1: Backup Current File**
```bash
cp /Users/<USER>/Infinity/bento/_SRC/pagoda/custom_scripts/infinityHubspot.php \
   /Users/<USER>/Infinity/bento/_SRC/pagoda/custom_scripts/infinityHubspot.php.backup
```

**Step 2: Deploy New Code**
```bash
# Copy new implementation to production location
cp infinityHubspot_v2.php \
   /Users/<USER>/Infinity/bento/_SRC/pagoda/custom_scripts/infinityHubspot.php
```

**Step 3: Restart Gulp/Build Process**
```bash
# Restart gulp watch to pick up changes
gulp watch
```

**Step 4: Smoke Test**
```bash
# Send test webhook to production endpoint
curl -X POST https://bento.infinityhospitality.net/custom_scripts/infinityHubspot.php \
  -H "Content-Type: application/json" \
  -d '{...test payload...}'
```

**Step 5: Monitor Initial Production Webhooks**
- Watch Pipedream logs for first 5-10 production webhooks
- Verify no errors in processing
- Check Bento for correctly created records
- Monitor MailSpon for any alerts

**Step 6: Enable HubSpot Production Webhook**
- Update HubSpot webhook URL from Pipedream to direct production URL
- Verify webhook deliveries in HubSpot dashboard

### 9.3 Rollback Procedure

**If Issues Detected**:

**Step 1: Restore Backup**
```bash
cp /Users/<USER>/Infinity/bento/_SRC/pagoda/custom_scripts/infinityHubspot.php.backup \
   /Users/<USER>/Infinity/bento/_SRC/pagoda/custom_scripts/infinityHubspot.php
```

**Step 2: Restart Build Process**
```bash
gulp watch
```

**Step 3: Revert HubSpot Webhook URL**
- Change back to Pipedream endpoint if direct connection was enabled

**Step 4: Document Issues**
- Log all errors and unexpected behaviors
- Note which test cases failed
- Identify root cause before retry

### 9.4 Monitoring & Maintenance

**Daily Monitoring**:
- Check Pipedream logs for errors
- Review MailSpon alerts
- Spot check random contacts created via webhook

**Weekly Review**:
- Analyze webhook success rate
- Review duplicate detection accuracy
- Check for unknown HubSpot Owner IDs
- Verify manager assignments

**Monthly Maintenance**:
- Update manager mapping if new sales team members added
- Review and optimize error handling
- Check for any Bento API changes
- Update documentation with any changes

**Metrics to Track**:
- Total webhooks received
- Success rate
- Average processing time
- Duplicate detection rate
- Unknown owner ID frequency
- Error frequency by type

---

## 10. Appendices

### 10.1 Complete Code Structure Template

```php
<?php
error_reporting(E_ERROR | E_PARSE);

// CORS Headers
header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Methods: GET, POST');
header("Access-Control-Allow-Headers: Content-Type, Bento-Token, content-type, bento-token, X-Requested-With");
header('Access-Control-Allow-Credentials: true');
header('Content-Type: application/json');

// Dependencies
require_once '../apiExample/bento.php';
require_once '../lib/_.php';

// Environment Configuration
function getEnvironmentConfig() {
    // Implementation as specified in section 3.2
}

// Logging Function
function httpPost($url, $data) {
    // Implementation for Pipedream/MailSpon logging
}

// Main Processing
$config = getEnvironmentConfig();
$bento = new BentoAPI($config['instance'], $config['api_token']);

try {
    // 1. Parse payload
    $json = file_get_contents("php://input");
    $data = json_decode($json, true);

    // 2. Extract fields
    $firstname = $data['firstname'] ?? '';
    $lastname = $data['lastname'] ?? '';
    $email = $data['email'] ?? '';
    $dealname = $data['dealname'] ?? '';
    $lead_source = $data['lead_source'] ?? '';
    $hubspot_owner_id = $data['hubspot_owner_id'] ?? '';

    // 3. Validate required fields
    // Implementation

    // 4. Resolve manager
    // Implementation

    // 5. Resolve or create company
    // Implementation

    // 6. Resolve or update contact
    // Implementation

    // 7. Create contact_info records
    // Implementation

    // 8. CRITICAL: Link contact_info to contact
    // Implementation

    // 9. Create system note
    // Implementation

    // 10. Return success response
    // Implementation

} catch (Exception $e) {
    // Error handling as specified in section 7
}
?>
```

### 10.2 Key Dependencies

**PHP Version**: 7.4+ (for null coalescing operator `??`)

**Required PHP Extensions**:
- curl (for API calls)
- json (for payload parsing)

**Required Files**:
- `/apiExample/bento.php` - BentoAPI class
- `/lib/_.php` - Underscore.php library

**External Services**:
- Pipedream webhook endpoint for logging
- MailSpon API for critical alerts

### 10.3 Bento API Reference

**Common Methods**:

```php
// Create
$result = $bento->create([
    'objectType' => 'contacts',
    'objectData' => [...]
]);

// Update
$result = $bento->update([
    'objectType' => 'contacts',
    'objectData' => ['id' => 123, ...]
]);

// Query
$results = $bento->getWhere([
    'objectType' => 'contacts',
    'queryObj' => ['email' => '<EMAIL>']
]);

// Get by ID
$object = $bento->getById($objectId);
```

**Response Format**:
- Create/Update: Returns object with ID and all fields
- Query: Returns array of matching objects (empty array if none found)
- Errors: Throws exception with error message

### 10.4 Glossary

**Terms**:
- **BentoAPI**: PHP class for interacting with Bento CRM API
- **contact_info**: Child objects of contacts/companies storing email, phone, address, etc.
- **data_source**: Integer field tracking external system origin (crc32 hash)
- **data_source_hash**: String field storing unique identifier from external system
- **deduplication**: Process of identifying and preventing duplicate records
- **HQ**: Parent organization/company in Bento hierarchy
- **objectType**: String identifier for Bento object types (e.g., 'contacts', 'companies')
- **tagged_with**: Array of IDs for categorization/filtering in Bento

### 10.5 Reference Files

**Primary Reference**: `/Users/<USER>/Infinity/bento/_SRC/pagoda/incoming/fg_salesforce.php`
- Salesforce integration showing complete pattern implementation
- Reference for deduplication logic
- Reference for contact_info linking pattern
- Reference for system notes and audit trail

**Current Implementation**: `/Users/<USER>/Infinity/bento/_SRC/pagoda/custom_scripts/infinityHubspot.php`
- v1 implementation with known issues
- Working base for company/contact creation
- Missing contact_info linking step

**Blueprint Files**:
- `/Users/<USER>/Infinity/bento/_SRC/blueprints/contacts.json`
- `/Users/<USER>/Infinity/bento/_SRC/blueprints/contact_info.json`
- `/Users/<USER>/Infinity/bento/_SRC/blueprints/companies.json`

**Frontend Reference**: `/Users/<USER>/Infinity/bento/_SRC/notify/_components/_cms/contact.js`
- Shows how Bento UI queries and displays contacts
- Reference for contact_info relationship expectations

### 10.6 Known Limitations

1. **No HubSpot Contact ID**: Payload does not include HubSpot contact ID, relying on email for deduplication
2. **No Company Data**: Payload only includes deal name, not full company details
3. **Manager Mapping Maintenance**: Manual mapping required when new sales team members added
4. **No Historical Data**: Cannot retroactively fix contacts created by v1 implementation
5. **Primary Email Override**: New webhooks always set their email as primary, potentially deprioritizing existing emails

### 10.7 Future Enhancements

**Phase 2 Enhancements** (Post-MVP):
1. **Intelligent Company Matching**: Fuzzy matching for similar company names
2. **Contact Merge Tool**: UI for manually merging duplicate contacts if found
3. **Manager Mapping UI**: Admin interface for maintaining HubSpot Owner mappings
4. **Webhook Replay**: Ability to replay failed webhooks from logs
5. **Advanced Deduplication**: Check for similar names/emails to flag potential duplicates
6. **Bulk Import**: Process historical HubSpot data for migration
7. **Two-Way Sync**: Update HubSpot when Bento data changes
8. **Custom Field Mapping**: Support for additional HubSpot custom fields

---

## Summary

This specification provides a complete technical blueprint for implementing the HubSpot-Bento webhook integration. The implementation must:

1. **Follow the Salesforce pattern** exactly for contact_info linking
2. **Use email as the primary deduplication key** globally across all contacts
3. **Map HubSpot Owner IDs to Bento Manager users** with fallback to Mary Topp
4. **Support dual environments** (development and production) with different configurations
5. **Implement comprehensive logging** for debugging and monitoring
6. **Maintain audit trail** via Bento system notes

The critical fix from v1 to v2 is the addition of the contact info linking step after creating contact_info records. This single update step enables email searchability and contract delivery functionality.

All patterns and approaches are derived from the proven Salesforce integration implementation, adapted for HubSpot's specific payload structure and business requirements.
