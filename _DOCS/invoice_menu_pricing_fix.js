/**
 * Invoice Menu Pricing Bug Fix Script
 * 
 * This script repairs the invoice menu pricing bug where proposals point to
 * blank menus instead of the original menus with populated sections.
 * 
 * Usage in browser console:
 * 1. Copy and paste this entire script
 * 2. Call: fixInvoiceMenuPricingBug(PROJECT_ID)
 * 3. Or call: diagnoseProject(PROJECT_ID) for analysis only
 * 
 * Author: System Analysis
 * Date: 2025-09-29
 * Bug Reference: Invoice_Menu_Pricing_Bug_Analysis_and_Fix.md
 */

/**
 * Comprehensive fix for invoice menu pricing bug
 * @param {number} projectId - The project/space ID to fix
 * @param {boolean} dryRun - If true, only diagnose without making changes
 */
function fixInvoiceMenuPricingBug(projectId, dryRun = false) {
    console.log(`🔧 ${dryRun ? 'DIAGNOSING' : 'FIXING'} invoice menu pricing for project ${projectId}`);
    console.log('=====================================');
    
    // Step 1: Find all pricing breakdowns for this project
    databaseConnection.obj.getWhere('inventory_menu_pricing_breakdown', {
        space: projectId,
        childObjs: 2
    }, function(breakdowns) {
        
        if (!breakdowns || breakdowns.length === 0) {
            console.error('❌ No pricing breakdowns found for project', projectId);
            return;
        }
        
        console.log(`📊 Found ${breakdowns.length} pricing breakdown(s)`);
        
        // Step 2: Analyze and categorize breakdowns
        const analysis = analyzeBreakdowns(breakdowns);
        
        if (!analysis.isValid) {
            console.error('❌ Analysis failed:', analysis.error);
            return;
        }
        
        console.log('📈 Analysis Results:');
        console.log(`   Original breakdown: ID ${analysis.original.id}, Menu ${analysis.original.menu}, Total $${(analysis.original.total/100).toFixed(2)}`);
        
        if (analysis.broken) {
            console.log(`   Broken breakdown: ID ${analysis.broken.id}, Menu ${analysis.broken.menu}, Total $${(analysis.broken.total/100).toFixed(2)}`);
        } else {
            console.log('✅ No broken breakdown found - system appears healthy');
            return;
        }
        
        if (dryRun) {
            console.log('🔍 DRY RUN - No changes will be made');
            return;
        }
        
        // Step 3: Get project to find proposal
        databaseConnection.obj.getById('groups', projectId, function(project) {
            if (!project || !project.proposal) {
                console.error('❌ Could not find project or proposal');
                return;
            }
            
            const proposalId = project.proposal.id || project.proposal;
            console.log(`📋 Found proposal ID: ${proposalId}`);
            
            // Step 4: Verify original menu has sections
            databaseConnection.obj.getById('inventory_menu', analysis.original.menu, function(originalMenu) {
                if (!originalMenu || !originalMenu.sections || originalMenu.sections.length === 0) {
                    console.error('❌ Original menu has no sections - cannot proceed');
                    return;
                }
                
                console.log(`✅ Original menu has ${originalMenu.sections.length} sections`);
                
                // Step 5: Execute the fix
                executeRepair(proposalId, analysis.original.menu, analysis.broken);
                
            }, 2);
        }, 1);
    });
}

/**
 * Analyze pricing breakdowns to identify original vs broken records
 */
function analyzeBreakdowns(breakdowns) {
    const original = breakdowns.find(b => b.total > 0 && b.active === "Yes");
    const broken = breakdowns.find(b => b.total === 0 && b.active === "No");
    
    if (!original) {
        return {
            isValid: false,
            error: 'No original pricing breakdown found (total > 0, active = Yes)'
        };
    }
    
    return {
        isValid: true,
        original: original,
        broken: broken,
        hasBrokenRecord: !!broken
    };
}

/**
 * Execute the repair sequence
 */
function executeRepair(proposalId, originalMenuId, brokenRecord) {
    console.log('🔧 Starting repair sequence...');
    
    // Step 1: Update proposal to point to original menu
    databaseConnection.obj.update('proposals', {
        id: proposalId,
        menu: originalMenuId
    }, function(result) {
        if (result.error) {
            console.error('❌ Failed to update proposal:', result.error);
            return;
        }
        
        console.log('✅ Proposal updated to point to original menu');
        
        if (!brokenRecord) {
            console.log('🎉 Repair completed - no broken records to archive');
            return;
        }
        
        // Step 2: Archive broken menu
        databaseConnection.obj.update('inventory_menu', {
            id: brokenRecord.menu,
            active: 'Archived'
        }, function(menuResult) {
            if (menuResult.error) {
                console.error('❌ Failed to archive broken menu:', menuResult.error);
                return;
            }
            
            console.log('✅ Broken menu archived');
            
            // Step 3: Archive broken pricing breakdown
            databaseConnection.obj.update('inventory_menu_pricing_breakdown', {
                id: brokenRecord.id,
                active: 'Archived'
            }, function(breakdownResult) {
                if (breakdownResult.error) {
                    console.error('❌ Failed to archive broken pricing breakdown:', breakdownResult.error);
                    return;
                }
                
                console.log('✅ Broken pricing breakdown archived');
                console.log('🎉 REPAIR COMPLETED SUCCESSFULLY!');
                console.log('');
                console.log('📋 Summary of changes:');
                console.log(`   - Proposal ${proposalId} now points to menu ${originalMenuId}`);
                console.log(`   - Menu ${brokenRecord.menu} archived`);
                console.log(`   - Pricing breakdown ${brokenRecord.id} archived`);
            });
        });
    });
}

/**
 * Diagnostic function - analyzes project without making changes
 */
function diagnoseProject(projectId) {
    return fixInvoiceMenuPricingBug(projectId, true);
}

/**
 * Batch diagnostic for multiple projects
 */
function diagnoseBatch(projectIds) {
    console.log(`🔍 Batch diagnosis for ${projectIds.length} projects`);
    console.log('='.repeat(50));
    
    projectIds.forEach((projectId, index) => {
        setTimeout(() => {
            console.log(`\n--- Project ${index + 1}/${projectIds.length}: ${projectId} ---`);
            diagnoseProject(projectId);
        }, index * 1000); // Stagger requests
    });
}

/**
 * System-wide health check for invoice menu pricing issues
 */
function systemHealthCheck() {
    console.log('🏥 Running system-wide health check for invoice menu pricing...');
    console.log('='.repeat(60));
    
    // Find all pricing breakdowns with zero totals in last 30 days
    const thirtyDaysAgo = new Date(Date.now() - 30*24*60*60*1000).toISOString();
    
    databaseConnection.obj.getWhere('inventory_menu_pricing_breakdown', {
        total: 0,
        date_created: { type: 'greater_than', value: thirtyDaysAgo },
        childObjs: 1
    }, function(suspiciousBreakdowns) {
        
        if (!suspiciousBreakdowns || suspiciousBreakdowns.length === 0) {
            console.log('✅ No suspicious pricing breakdowns found in last 30 days');
            return;
        }
        
        console.log(`⚠️ Found ${suspiciousBreakdowns.length} suspicious pricing breakdowns:`);
        
        const affectedProjects = [...new Set(suspiciousBreakdowns.map(b => b.space.id || b.space))];
        
        affectedProjects.forEach(projectId => {
            console.log(`   Project ${projectId}: Needs investigation`);
        });
        
        console.log('\n🔧 To fix these issues, run:');
        affectedProjects.forEach(projectId => {
            console.log(`   fixInvoiceMenuPricingBug(${projectId});`);
        });
    });
}

// Export functions for console use
window.fixInvoiceMenuPricingBug = fixInvoiceMenuPricingBug;
window.diagnoseProject = diagnoseProject;
window.diagnoseBatch = diagnoseBatch;
window.systemHealthCheck = systemHealthCheck;

console.log('📜 Invoice Menu Pricing Fix Script Loaded');
console.log('Available functions:');
console.log('  - fixInvoiceMenuPricingBug(projectId)');
console.log('  - diagnoseProject(projectId)');
console.log('  - diagnoseBatch([projectId1, projectId2, ...])');
console.log('  - systemHealthCheck()');
