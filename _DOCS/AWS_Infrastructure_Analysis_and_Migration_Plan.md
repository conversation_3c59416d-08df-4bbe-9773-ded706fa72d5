# Bento Infrastructure Analysis & AWS Migration Plan

## Executive Summary

**Current Monthly Cost:** $2,455.17 ($29,462/year)  
**Proposed VPS Cost:** $120/month ($1,440/year)  
**Annual Savings:** $28,022 (95% cost reduction)  
**Cost Per User:** $49.10/month (current) vs $2.40/month (proposed)

## Current Architecture Problems

### The Over-Engineering Disaster
Your 50-user application is running on enterprise-grade infrastructure designed for companies with thousands of microservices:

- **2 EKS Kubernetes Clusters** (dev + production)
- **2 RDS PostgreSQL Databases** (managed)
- **4 Load Balancers**
- **2 Container Registries (ECR)**
- **2 CI/CD Pipelines (CodeBuild)**
- **Complex Helm Chart Deployments**
- **Multiple ConfigMaps and Secrets**

### The Configuration Nightmare We Just Fixed

**Problem Chain:**
1. Helm values files had hardcoded CSG_FORTE credentials
2. buildspec files had `--set config.CSG_FORTE_*=${EMPTY_VARIABLE}` overrides
3. CodeBuild environment variables were empty
4. Helm deployments overwrote hardcoded values with empty strings
5. Manual kubectl patches got overwritten on next deployment

**Solution:**
- Removed CSG_FORTE `--set` overrides from buildspec-dev.yml and buildspec.yml
- Added missing CSG_FORTE variables to merge-app Helm charts
- Set proper CURRENT_ENV values ("development" for dev, "production" for prod)
- Now Helm uses hardcoded values from charts instead of empty environment variables

## Current Infrastructure Components

### Amazon EKS (Elastic Kubernetes Service)
- **Purpose:** Container orchestration for microservices
- **Your Usage:** Running 2 simple PHP applications
- **Cost:** ~$292/month for control planes + compute costs
- **Complexity:** Requires understanding pods, services, ingress, ConfigMaps, secrets, RBAC

### Helm Charts
- **Location:** `_SERVICES/helm/bento-app/` and `_SERVICES/helm/merge-app/`
- **Purpose:** Template-based Kubernetes deployments
- **Problem:** Every deployment regenerates ConfigMaps, overwriting manual changes

### AWS CodeBuild Pipelines
- **Files:** `buildspec.yml` (production), `buildspec-dev.yml` (development)
- **Process:** Build Docker images → Push to ECR → Deploy via Helm
- **Issue:** Was overriding environment variables with empty strings

### Container Registry (ECR)
- **Images:** `bento` and `bento-merge-service`
- **Cost:** ~$20/month for storage

### RDS Databases
- **Setup:** 2 separate PostgreSQL instances (dev + prod)
- **Cost:** ~$400-600/month total

## Cost Breakdown Analysis

### Current AWS Monthly Costs (~$2,455)
- **EKS Control Planes:** $146 × 2 = $292
- **EC2 Worker Nodes:** $400-600
- **RDS Databases:** $400-600
- **Load Balancers:** $50-100
- **ECR Storage:** $20
- **Data Transfer:** $100-200
- **CodeBuild:** $20-50
- **Other Services:** $100-200

### Proposed VPS Monthly Costs (~$120)
- **DigitalOcean Droplet (8GB RAM):** $48
- **Managed PostgreSQL:** $60
- **Domain + SSL:** $2/month
- **Backup Storage:** $10

## Migration Strategy

### Target Architecture
```
┌─────────────────────────────────────────┐
│           VPS (Ubuntu 22.04)           │
│                                         │
│  ┌─────────────────────────────────────┐│
│  │            nginx                    ││
│  │  ┌─────────────────────────────────┐││
│  │  │         PHP-FPM                 │││
│  │  │  ┌─────────────────────────────┐│││
│  │  │  │      Bento App              ││││
│  │  │  │  - Main application         ││││
│  │  │  │  - Merge service            ││││
│  │  │  │  - Static files             ││││
│  │  │  └─────────────────────────────┘│││
│  │  └─────────────────────────────────┘││
│  └─────────────────────────────────────┘│
│                                         │
│  ┌─────────────────────────────────────┐│
│  │          PostgreSQL                 ││
│  │  - Production database              ││
│  │  - Automated backups                ││
│  └─────────────────────────────────────┘│
└─────────────────────────────────────────┘
```

### Development Workflow
```yaml
# docker-compose.yml for local development
version: '3.8'
services:
  app:
    build: .
    ports: ["8080:80"]
    volumes: ["./:/var/www/html"]
    environment:
      - CURRENT_ENV=development
      - DATABASE_URL=****************************/bento
  
  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=bento
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes: ["./data:/var/lib/postgresql/data"]
```

### Production Deployment Script
```bash
#!/bin/bash
# Simple deployment script
git pull origin master
composer install --no-dev --optimize-autoloader
npm run build
sudo systemctl reload php8.1-fpm
sudo systemctl reload nginx
echo "Deployment complete"
```

## Migration Plan

### Phase 1: Data Export
```bash
# Export production database
kubectl exec -it <postgres-pod> -- pg_dump bento > production_backup.sql

# Export file storage
kubectl cp <app-pod>:/var/www/html/uploads ./uploads_backup/

# Export environment variables
kubectl get configmap bentoproduction -o yaml > production_config.yaml
```

### Phase 2: VPS Setup
1. **Provision DigitalOcean Droplet** (8GB RAM, Ubuntu 22.04)
2. **Install LEMP Stack**
   - nginx
   - PHP 8.1 with required extensions
   - PostgreSQL 15
3. **Configure SSL** with Let's Encrypt
4. **Import database and files**
5. **Set up automated backups**

### Phase 3: Testing & Validation
1. **Deploy application to VPS**
2. **Import production data**
3. **Test all functionality**
4. **Performance testing**
5. **Backup/restore testing**

### Phase 4: DNS Cutover
1. **Update DNS records** to point to VPS
2. **Monitor application performance**
3. **Verify all integrations work**
4. **Test payment processing (CSG Forte)**

### Phase 5: AWS Cleanup
1. **Delete EKS clusters**
2. **Delete RDS instances**
3. **Delete ECR repositories**
4. **Cancel CodeBuild projects**
5. **Remove Route53 records**
6. **Delete unused security groups and VPCs**

## Advantages & Tradeoffs

### Current AWS Setup
**Advantages:**
- ✅ Auto-scaling (unnecessary for 50 users)
- ✅ High availability (overkill)
- ✅ Managed services (at 20x cost)
- ✅ Enterprise security features

**Disadvantages:**
- ❌ $29,462/year cost
- ❌ Extreme complexity
- ❌ Vendor lock-in
- ❌ Requires DevOps expertise
- ❌ Slow deployments (5-10 minutes)
- ❌ Configuration drift issues
- ❌ Multiple failure points

### Simple VPS Setup
**Advantages:**
- ✅ $1,440/year cost (95% savings)
- ✅ Simple to understand and debug
- ✅ Fast deployments (30 seconds)
- ✅ Full control over environment
- ✅ Easy backups and restores
- ✅ No vendor lock-in
- ✅ Standard LAMP/LEMP stack knowledge applies

**Disadvantages:**
- ❌ Single point of failure (mitigated with backups)
- ❌ Manual scaling (not needed for 50 users)
- ❌ OS maintenance responsibility (automated with unattended-upgrades)

## ROI Analysis

- **Migration Effort:** ~40 hours
- **Annual Savings:** $28,022
- **Hourly Value:** $700/hour
- **Payback Period:** 2 weeks
- **Daily Cost of Delay:** $80

## Risk Assessment

### Migration Risks
- **Downtime during cutover:** 2-4 hours (mitigated with proper planning)
- **Data loss:** Low (multiple backups, tested restore procedures)
- **Performance issues:** Low (VPS specs exceed current usage)
- **Integration failures:** Medium (thorough testing required)

### Current System Risks
- **Configuration drift:** High (manual changes overwritten)
- **Cost escalation:** High (AWS costs trending upward)
- **Complexity debt:** High (difficult to maintain/debug)
- **Vendor lock-in:** High (difficult to migrate later)

## Recommended Timeline

- **Week 1:** VPS setup and testing
- **Week 2:** Application deployment and validation
- **Week 3:** Data migration and integration testing
- **Week 4:** DNS cutover and AWS cleanup

## Files Modified During CSG_FORTE Fix

### buildspec-dev.yml
- Removed CSG_FORTE `--set` overrides (lines 102-109, 142-149)
- Fixed YAML formatting issues
- Hardcoded `CURRENT_ENV=development`

### buildspec.yml  
- Removed CSG_FORTE `--set` overrides (lines 103-110, 143-150)
- Fixed YAML formatting issues
- Kept `CURRENT_ENV=${CURRENT_ENV}` for production flexibility

### Helm Charts Modified
- `_SERVICES/helm/bento-app/values-dev.yaml`: Added CURRENT_ENV="development"
- `_SERVICES/helm/bento-app/values.yaml`: Added CURRENT_ENV="production"
- `_SERVICES/helm/merge-app/values-dev.yaml`: Added CSG_FORTE variables + CURRENT_ENV="development"
- `_SERVICES/helm/merge-app/values.yaml`: Added CSG_FORTE variables + CURRENT_ENV="production"
- `_SERVICES/helm/merge-app/templates/configmap.yaml`: Added CSG_FORTE variable templates

## Next Steps

1. **Verify production CSG_FORTE fix** after current build completes
2. **Plan migration timeline** based on business requirements
3. **Set up VPS test environment**
4. **Begin data export and testing procedures**
5. **Execute migration plan**
6. **Celebrate $28k annual savings**

---

*Document created: 2025-09-24*  
*Last updated: 2025-09-24*  
*Status: CSG_FORTE fix deployed to production, migration planning ready*
