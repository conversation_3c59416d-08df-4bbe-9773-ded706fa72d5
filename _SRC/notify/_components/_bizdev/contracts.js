Factory.register("contracts", function (sb) {
  /*
	A simple, lightweight jQuery plugin for creating sortable tables.
	https://github.com/kylefox/jquery-tablesort
	Version 0.0.11
*/

  (function ($) {
    $.tablesort = function ($table, settings) {
      var self = this;
      this.$table = $table;
      this.$thead = this.$table.find("thead");
      this.settings = $.extend({}, $.tablesort.defaults, settings);
      this.$sortCells =
        this.$thead.length > 0
          ? this.$thead.find("th:not(.no-sort)")
          : this.$table.find("th:not(.no-sort)");
      this.$sortCells.on("click.tablesort", function () {
        self.sort($(this));
      });
      this.index = null;
      this.$th = null;
      this.direction = null;
    };

    $.tablesort.prototype = {
      sort: function (th, direction) {
        var start = new Date(),
          self = this,
          table = this.$table,
          rowsContainer =
            table.find("tbody").length > 0 ? table.find("tbody") : table,
          rows = rowsContainer.find("tr").has("td, th"),
          cells = rows
            .find(":nth-child(" + (th.index() + 1) + ")")
            .filter("td, th"),
          sortBy = th.data().sortBy,
          sortedMap = [];

        var unsortedValues = cells.map(function (idx, cell) {
          if (sortBy)
            return typeof sortBy === "function"
              ? sortBy($(th), $(cell), self)
              : sortBy;
          return $(this).data().sortValue != null
            ? $(this).data().sortValue
            : $(this).text();
        });
        if (unsortedValues.length === 0) return;

        //click on a different column
        if (this.index !== th.index()) {
          this.direction = "asc";
          this.index = th.index();
        } else if (direction !== "asc" && direction !== "desc")
          this.direction = this.direction === "asc" ? "desc" : "asc";
        else this.direction = direction;

        direction = this.direction == "asc" ? 1 : -1;

        self.$table.trigger("tablesort:start", [self]);
        self.log("Sorting by " + this.index + " " + this.direction);

        // Try to force a browser redraw
        self.$table.css("display");
        // Run sorting asynchronously on a timeout to force browser redraw after
        // `tablesort:start` callback. Also avoids locking up the browser too much.
        setTimeout(
          function () {
            self.$sortCells.removeClass(
              self.settings.asc + " " + self.settings.desc
            );
            for (var i = 0, length = unsortedValues.length; i < length; i++) {
              sortedMap.push({
                index: i,
                cell: cells[i],
                row: rows[i],
                value: unsortedValues[i],
              });
            }

            sortedMap.sort(function (a, b) {
              return self.settings.compare(a.value, b.value) * direction;
            });

            $.each(sortedMap, function (i, entry) {
              rowsContainer.append(entry.row);
            });

            th.addClass(self.settings[self.direction]);

            self.log(
              "Sort finished in " +
                (new Date().getTime() - start.getTime()) +
                "ms"
            );
            self.$table.trigger("tablesort:complete", [self]);
            //Try to force a browser redraw
            self.$table.css("display");
          },
          unsortedValues.length > 2000 ? 200 : 10
        );
      },

      log: function (msg) {
        if (
          ($.tablesort.DEBUG || this.settings.debug) &&
          console &&
          console.log
        ) {
          console.log("[tablesort] " + msg);
        }
      },

      destroy: function () {
        this.$sortCells.off("click.tablesort");
        this.$table.data("tablesort", null);
        return null;
      },
    };

    $.tablesort.DEBUG = false;

    $.tablesort.defaults = {
      debug: $.tablesort.DEBUG,
      asc: "sorted ascending",
      desc: "sorted descending",
      compare: function (a, b) {
        if (a > b) {
          return 1;
        } else if (a < b) {
          return -1;
        } else {
          return 0;
        }
      },
    };

    $.fn.tablesort = function (settings) {
      var table, sortable, previous;
      return this.each(function () {
        table = $(this);
        previous = table.data("tablesort");
        if (previous) {
          previous.destroy();
        }
        table.data("tablesort", new $.tablesort(table, settings));
      });
    };
  })(window.Zepto || window.jQuery);

  var ui = {},
    tableUI = {},
    components = {},
    contractId = 0,
    defaultRequestEmail =
      "The parties agree that this agreement may be electronically signed. The parties agree that the electronic signatures appearing on this agreement are the same as handwritten signatures for the purposes of validity, enforceability and admissibility.<br /><br />You may withdraw your consent to receive electronic documents, notices or disclosures at any time. In order to withdraw consent, you must notify the sending party that you wish to withdraw consent and request that your future documents, notices and disclosures be provided in paper format. To request paper copies of documents; withdraw consent to conduct business electronically and receive documents, notices or disclosures electronically; or withdraw consent to sign documents electronically, please contact the sending party by telephone, postal mail or email.",
    defaultRequestEmailSubject = "New Signature Request",
    defaultDisclaimer =
      "The parties agree that this agreement may be electronically signed. The parties agree that the electronic signatures appearing on this agreement are the same as handwritten signatures for the purposes of validity, enforceability and admissibility.<br /><br />You may withdraw your consent to receive electronic documents, notices or disclosures at any time. In order to withdraw consent, you must notify the sending party that you wish to withdraw consent and request that your future documents, notices and disclosures be provided in paper format. To request paper copies of documents; withdraw consent to conduct business electronically and receive documents, notices or disclosures electronically; or withdraw consent to sign documents electronically, please contact the sending party by telephone, postal mail or email.",
    defaultRequestEmailSubject = "New Signature Request",
    defaultRequestEmail = "",
    homeScreen = true,
    navigation = true,
    sectionTypes = {};
  var fieldsToReject = [
    "data_source",
    "closing_date",
    "data_source_id",
    "data_source",
    "external_form",
    "field_type",
    "follow_up_date",
    "is_template",
    "id",
    "stripe_id",
    "state",
    "potential_value",
    "object_uid",
    "quickbooks_id",
    "sales_person",
    "chart_of_accounts",
    "contact_info",
    "is_vendor",
    "markup_percent",
    "parent",
    "child_ids",
    "company_category",
    "default_product",
    "parent_id",
    "products",
    "approval_notes",
    "contract",
    "main_contact",
    "sections",
    "vendors",
    "company",
    "invoices",
  ];
  var MergeTags = [];
  var settingsObj = null;
  var CachedMergeTags = [];

  // Helper funcs

  function getTemplateQuery(state, callback) {
    var where = {
      is_template: 1,
      childObjs: 1,
      active: {
        type: "not_equal",
        value: "No",
      },
    };

    switch (state.pageObject.object_bp_type) {
      case "contacts":
        where.merge_type = "contact";
        where.shared_with = [state.id];
        break;
      case "entity_tool":
        if (state.parent.object_bp_type == "contact_types") {
          where.merge_type = "contact";
          where.shared_with = [state.entity];
        } else {
          where.merge_type = "space";
          where.tagged_with = [state.entity];
          if (state.team) {
            where.tagged_with = {
              type: "any",
              values: [state.entity],
              or: {
                tagged_with: {
                  type: "any",
                  values: [state.team.id],
                },
              },
            };
          }
        }
        break;
      case "groups":
        if (state.layer == "hq") {
          delete where.related_object;
        } else if (state.layer == "myStuff") {
          where.merge_type = "space";
          where.tagged_with = [sb.data.cookie.userId];
        } else if (state.layer == "team") {
          var teamTags = [state.team.id];
          if (state.hasOwnProperty("pageObject")) {
            if (state.pageObject) {
              if (state.pageObject.hasOwnProperty("parent")) {
                if (state.pageObject.parent) {
                  if (state.pageObject.parent.hasOwnProperty("id")) {
                    if (state.pageObject.parent.id) {
                      teamTags.push(state.pageObject.parent.id);
                    }
                  }
                }
              }
            }
          }

          where.merge_type = "space";
          where.tagged_with = {
            type: "any",
            values: teamTags,
          };
        } else if (state.layer == "project") {
          // I commented out the stuff below temporarily //
          // We can turn this back on after batch tagging and system-wide tags are added //

          // var projectTags = [state.id];
          // if ( state.hasOwnProperty('project') ) {
          // 	if ( state.project ) {
          // 		if ( state.project.hasOwnProperty('parent') ) {
          // 			if ( state.project.parent ) {
          // 				if ( state.project.parent.hasOwnProperty('id') ) {
          // 					projectTags.push(state.project.parent.id)
          // 					if ( state.project.parent.hasOwnProperty('parent') ) {
          // 						if ( state.project.parent.parent ) {
          // 							if ( _.isNumber(state.project.parent.parent) ) {
          // 								projectTags.push(state.project.parent.parent)
          // 							}
          // 						}
          // 					}
          // 				}
          // 			}
          // 		}
          // 	}
          // }
          // if ( state.hasOwnProperty('team') ) {
          // 	if ( state.team ) {
          // 		if ( state.team.hasOwnProperty('id') ) {
          // 			projectTags.push(state.team.id)
          // 		}
          // 	}
          // }

          where.merge_type = "proposal";
          // where.tagged_with = {
          // 	type: 'any',
          // 	values: projectTags,
          // 	or: {
          // 		shared_with: {
          // 			type: 'any',
          // 			values: projectTags
          // 		}
          // 	}
          // }
        } else {
          where.merge_type = "space";
          where.tagged_with = [state.id];
        }
        break;
    }

    if (window.location.href.includes("#hq")) {
      delete where.tagged_with;
    }

    if (typeof callback === "function") {
      callback(where);
    } else {
      return where;
    }
  }

  function createWorkflow(ui, state, obj_info, onComplete) {
    var modal = ui;

    function chooseOrCreateDocument(ui, state, obj_info, onComplete) {
      // Get contracts (checking here to see if we need to show the modal to choose a contract)
      sb.data.db.obj.getWhere(
        "contracts",
        getTemplateQuery(state),
        function (contracts) {
          if (!_.isEmpty(contracts)) {
            chooseATemplate(contracts, ui, obj_info, onComplete);
          } else {
            createNewBlankDocument(obj_info);
          }
        }
      );
    }

    // Set variables
    obj_info.layer = state.layer;
    obj_info.merge_type = "space";
    obj_info.related_object = state.pageObject.id;
    obj_info.parent = state.pageObject.id;
    obj_info.tagged_with = [state.pageObject.id];

    obj_info.is_public = true;

    if (state.layer === "myStuff") {
      obj_info.space = state.related_object;
      obj_info.related_object = state.where.tagged_with[0];
      obj_info.parent = state.where.tagged_with[0];
      obj_info.tagged_with = [state.where.tagged_with[0]];
    } else if (state.layer === "team") {
      obj_info.space = state.related_object;
      obj_info.related_object = state.team.id;
      obj_info.parent = state.team.id;
      obj_info.tagged_with = [state.team.id];
    } else if (state.layer === "object") {
      obj_info.related_object = state.id;
      obj_info.parent = state.id;
      obj_info.tagged_with = [state.id];

      if (state.pageObject.type.object_bp_type === "contact_types") {
        obj_info.merge_type = "contact";
      }
    } else if (state.entity > 0) {
      obj_info.related_object = state.entity;
      obj_info.parent = state.entity;
      obj_info.tagged_with = [state.entity];

      if (state.pageObject.entityType.object_bp_type === "contact_types") {
        obj_info.merge_type = "contact";
      }
    } else if (state.layer === "project") {
      obj_info.merge_type = "proposal";
    }

    obj_info.name = obj_info.is_template
      ? "Untitled Document Template"
      : "Untitled Document";

    if (state.team) {
      obj_info.tagged_with.push(state.team.id);
    }

    if (obj_info.merge_type === "contact") {
      obj_info.shared_with = [obj_info.related_object];
      delete obj_info.tagged_with;
      if (state.team) {
        obj_info.tagged_with = [state.team.id];
      }
    }

    if (state.layer === "project" && !state.entity > 0) {
      if (state.pageObject.main_contact || state.pageObject.is_template) {
        if (state.pageObject.main_contact) {
          obj_info.main_contact = state.pageObject.main_contact.id;
        }

        chooseOrCreateDocument(ui, state, obj_info, onComplete);
      } else {
        searchForAContact(
          ui,
          state,
          function (dom) {
            dom.patch();
          },
          function (contact) {
            obj_info.main_contact = contact.id;

            ui.empty();

            chooseOrCreateDocument(ui, state, obj_info, onComplete);
          }
        );
      }
    } else {
      chooseOrCreateDocument(ui, state, obj_info, onComplete);
    }

    modal.show();
  }

  function archive_selected_items(itemId, options, onComplete) {
    // This is a slimmed down version of archive_selected_items found in _SRC\notify\_components\_core\_collections\_collections.js
    if (itemId === undefined) {
      sb.dom.alerts.alert(
        "No items selected",
        "Select some items first.",
        "warning"
      );
      return false;
    }

    var msgTxt = "Send to archive?";
    var txt = "You can always un-archive this later.";
    var updateFunc = "erase";
    if (options.is_archieved) {
      updateFunc = "restore";
      msgTxt = "Restore from archive?";
      txt = "This will return this back where it came from.";
    }

    sb.dom.alerts.ask(
      {
        title: msgTxt,
        text: txt,
      },
      function (response) {
        swal.disableButtons();

        if (response) {
          sb.data.db.obj[updateFunc](
            options.objectType,
            itemId,
            function (response) {
              swal.close();

              if (typeof onComplete === "function") {
                onComplete(response);
              }
            }
          );
        } else {
          swal.close();

          if (typeof onComplete === "function") {
            onComplete(response);
          }
        }
      }
    );
  }

  function addContractView(bp, dom, setup) {
    var buttonState = setup.buttonState;

    dom.makeNode("cont", "div", { css: "" });

    dom.cont.makeNode("title", "headerText", {
      text: "Select a document template",
      size: "small",
      css: "text-center",
    });

    dom.cont
      .makeNode("contracts", "div", {})
      .makeNode("loading", "headerText", {
        text: '<i class="fa fa-circle-o-notch fa-spin"></i> Loading documents',
        css: "text-center",
        size: "x-small",
      });

    dom.patch();

    sb.data.db.obj.getWhere(
      "contract_types",
      { contract_object_type: buttonState.contractType },
      function (contractTypes) {
        sb.data.db.obj.getWhere(
          "contracts",
          {
            related_object: 0,
            merge_type: "proposal",
            childObjs: 1,
          },
          function (contracts) {
            delete dom.cont.contracts.loading;

            if (contracts.length == 0) {
              dom.cont.contracts.makeNode("title", "div", {
                text: "No document templates",
                css: "ui medium header",
              });

              dom.cont.contracts.makeNode("btns", "buttonGroup", {});

              dom.cont.contracts.btns
                .makeNode("go", "button", {
                  text: 'Create Blank Document Template <i class="fa fa-arrow-right"></i>',
                  css: "pda-btn-green",
                })
                .notify(
                  "click",
                  {
                    type: "contracts-run",
                    data: {
                      run: function () {
                        dom.cont.contracts.btn.go.loading();

                        window.location.href = sb.data.url.createPageURL(
                          "object",
                          {
                            type: "contract",
                            id: 0,
                            name: "Untitled Document Template",
                          }
                        );
                      },
                    },
                  },
                  sb.moduleId
                );

              dom.cont.contracts.btns
                .makeNode("newDocument", "button", {
                  text: 'Create Blank Document <i class="fa fa-arrow-right"></i>',
                  css: "pda-btn-green",
                })
                .notify(
                  "click",
                  {
                    type: "contracts-run",
                    data: {
                      run: function () {
                        sb.data.db.obj["create"](
                          "contracts",
                          obj,
                          function (obj) {
                            dom.cont.contracts.btns.newDocumentText.loading();

                            window.location.href = sb.data.url.createPageURL(
                              "object",
                              {
                                edit: true,
                                type: "contracts",
                                id: 0,
                                name: "Untitled Document",
                              }
                            );
                          }
                        );
                      },
                    },
                  },
                  sb.moduleId
                );
            } else {
              dom.cont.contracts.makeNode("table", "table", {
                css: "fixed orange",
                columns: {
                  name: "Name",
                  btns: "",
                },
              });

              _.each(contracts, function (c) {
                if (c.contract_types) {
                  dom.cont.contracts.table.makeRow("row-" + c.id, [c.name, ""]);
                  dom.cont.contracts.table.body["row-" + c.id].btns
                    .makeNode("use", "button", {
                      text: '<i class="fa fa-plus"></i> Use This Template',
                      css: "pda-btn-green",
                    })
                    .notify(
                      "click",
                      {
                        type: "contracts-run",
                        data: {
                          run: function (contract) {
                            this.cont.contracts.table.body[
                              "row-" + contract.id
                            ].btns.use.loading();

                            var originalTemplateId = contract.id;
                            delete contract.id;

                            contract.parent = buttonState.objectId;
                            contract.related_object = buttonState.objectId;
                            contract.main_contact = buttonState.contactId;
                            contract.active = "Yes";
                            contract.name = contract.name + "-COPY";
                            contract.data_source_id = originalTemplateId;

                            sb.data.db.obj.create(
                              "contracts",
                              contract,
                              function (created) {
                                if (buttonState.afterCreate) {
                                  buttonState.afterCreate(
                                    created,
                                    function (created) {
                                      if (buttonState.objectView) {
                                        singleView(
                                          created,
                                          buttonState.objectView,
                                          buttonState,
                                          function (draw) {
                                            draw.dom.patch();
                                            draw.after(draw.dom);
                                          }
                                        );
                                      }
                                    }
                                  );
                                } else {
                                  tableUI.state.show();
                                }
                              }
                            );
                          }.bind(dom, c),
                        },
                      },
                      sb.moduleId
                    );
                }
              });
            }

            dom.cont.contracts.patch();
          }
        );
      }
    );
  }

  function checkSettingsObject(callback, options) {

    if (settingsObj) {
      callback(settingsObj);
    } else {

        sb.data.db.obj.getAll("contract_system", function (settingsObjDB) {

        settingsObj = {
            signature_disclaimer: defaultDisclaimer,
            request_email: defaultRequestEmail,
            request_email_subject: defaultRequestEmailSubject,
          };

        if (settingsObjDB.length == 0) {

          sb.data.db.obj.create(
            "contract_system",
            settingsObj,
            function (created) {
              settingsObj = created;

              callback(settingsObj);
            }
          );

        } else {

          settingsObj = settingsObjDB[0];


          if ( appConfig.instance == 'dreamcatering' ) {

            ///Dream has two contract_system settings objs based on project type.
            /// One for Event Management (Dream) and one for Daily Dish Event Management (Daily Dish)
            settingsObj = _.find(settingsObjDB, {id: 1036073});

            if ( options.project ) {

                var projectType = options.project.type;

                if (projectType.id == 12033227 || projectType.id  == 12261255) {
                    ///{id: 12261386, object_bp_type: 'contract_systems} - DEV (DREAMCATERING)
                    // var dreamConSys = 12261386;

                    ///{id: 12384223, object_bp_type: 'contract_systems} - PROD (DREAMCATERING)
                    var dreamConSys = 12384223;
                    var dailyDishConSys = _.find(settingsObjDB, {id: dreamConSys});

                    if ( dailyDishConSys ){
                        settingsObj = dailyDishConSys;
                    }

                }
            }


          }

          callback(settingsObj);
        }
      });
    }
  }

  function checkProjectSettingsObject(callback) {
    sb.data.db.obj.getAll("workorder_system", function (settingsObj) {
      if (settingsObj.length == 0) {
        var defaultOppNoteText = "";
        var settingsObj = {
          signature_disclaimer:
            "The parties agree that this agreement may be electronically signed. The parties agree that the electronic signatures appearing on this agreement are the same as handwritten signatures for the purposes of validity, enforceability and admissibility.<br /><br />You may withdraw your consent to receive electronic documents, notices or disclosures at any time. In order to withdraw consent, you must notify the sending party that you wish to withdraw consent and request that your future documents, notices and disclosures be provided in paper format. To request paper copies of documents; withdraw consent to conduct business electronically and receive documents, notices or disclosures electronically; or withdraw consent to sign documents electronically, please contact the sending party by telephone, postal mail or email.",
          request_email: defaultRequestEmail,
          request_email_subject: defaultRequestEmailSubject,
          default_opp_note: defaultOppNoteText,
          follow_up_time: 1,
          follow_up_type: "days",
        };

        sb.data.db.obj.create(
          "workorder_system",
          settingsObj,
          function (created) {
            callback(created);
          }
        );
      } else {
        callback(settingsObj[0]);
      }
    });
  }

  // creates initial ui objects and sets the intial comp state
  function createUI(domObj, contractType, objectId, contactId, setup) {
    ui = sb.dom.make(domObj.selector);
    tableUI = ui.makeNode("table", "column", { width: 12 });

    ui.table.makeNode("loading", "div", { css: "ui basic segment" });
    ui.table.loading
      .makeNode("loader", "div", { css: "ui active inverted dimmer" })
      .makeNode("loading", "div", {
        text: "Loading contracts",
        css: "ui text loader",
      });

    tableUI.state = tableUIState;

    ui.build();
    tableUI.state(contractType, objectId, contactId, setup);
  }

  // merges the object data with the contrac HTML. Returns an HTML string
  function createMergedHTML(obj, callback, payNow, customString, opts) {
    var mergeForPdf = true;
    var mergeVars = {};

    if (opts && opts.hasOwnProperty("mergeForPdf")) {
      mergeForPdf = opts.mergeForPdf;
    }
    if (opts && opts.mergeVars) {
      mergeVars = opts.mergeVars;
    }

    var post = obj;

    if (!obj.id && obj.state && obj.state.entity && obj.state.entity.id) {
      post = {
        objId: obj.state.entity.id,
      };
    }

    if (typeof post === "string") {
      post = {};
    }

    sb.data.db.controller(
      "getMergeTagData",
      {
        obj: post,
      },
      function (data) {
        var contactBP = data.blueprints.contact;
        var companyBP = data.blueprints.company;
        var proposalBP = data.blueprints.proposal;
        var company_logo = !_.isEmpty(data.company_logo)
          ? data.company_logo[0]
          : [];
        var logo = !_.isEmpty(data.company_logo) ? data.company_logo[0] : [];
        var company = data.company;
        var contact = data.contact;
        var invoices = data.invoices;
        var invoiceSysObjs = data.invoice_system;
        var menu = data.inventory_menu;
        var pricingBreakdown = !_.isEmpty(data.inventory_menu_pricing_breakdown)
          ? data.inventory_menu_pricing_breakdown[0]
          : [];
        var project = data.project;
        var schedule = data.schedule;

        function prepForRegex(string) {
          var ret = string;

          // Escape special chars for regex
          if (!_.isEmpty(ret)) {
            ret = ret.split("|").join("\\|");
            ret = ret.split("(").join("\\(");
            ret = ret.split(")").join("\\)");
            ret = ret.split("&").join("&amp;");
            ret = ret.split("?").join("\\?");
          }

          return ret;
        }

        function getEntityDocData(obj, onComplete) {
          var toMerge = [];
          var templatePropertyName = "html_string";

          _.each(MergeTags, function (tag) {
            if (obj[templatePropertyName]) {
              var tagInstance = _.clone(tag);

              if (
                obj[templatePropertyName].indexOf("{{" + tag.tag + "}}") !== -1
              ) {
                var tagOpts = getMergeTagOptions(
                  tag,
                  obj[templatePropertyName]
                );
                _.each(tagOpts, function (opt, i) {
                  tagInstance = _.clone(tagInstance);
                  tagInstance.name += i;
                  tagInstance.options = opt;

                  toMerge.push(tagInstance);
                });
              }
            }
          });

          getMergeTagData(obj, toMerge, function (mergeData) {
            onComplete(mergeData);
          });
        }

        function getMergeTagOptions(i, htmlString) {
          var ret = [];
          var chunks = [];
          var updatedHtml = htmlString;
          htmlString.replace(
            new RegExp(prepForRegex(i.tag), "g"),
            function (a, index, c) {
              index = index + i.tag.length + 2;
              var opts = {};
              var chunk = "";
              var nextStr = "";
              var mergeTagOptionsStr = "";
              var mergeTagOptions = [];

              if (updatedHtml[index] === "(") {
                chunk = updatedHtml.slice(index + 1);
                updatedHtml.replace(chunk, "");

                chunk = chunk.slice(0, chunk.indexOf(")"));
                chunks.push(chunk);

                opts = _.compact(chunk.split(","));

                // Won't need to do this anymore once ui enforces good syntax.
                _.each(mergeTagOptions, function (t, i) {
                  opts[i] = t.replace("&amp;", "");
                  opts[i] = mergeTagOptions[i].split("<")[0];
                });
              }

              ret.push(opts);

              if (
                !_.isEmpty(chunk) &&
                typeof i.data !== "function" &&
                typeof i.parse === "function"
              ) {
                var parsedHtml = "";
                try {
                  i.parse(mergeTagOptions);
                } catch (err) {
                  console.log(
                    "Error parsing merge tag html: ",
                    err,
                    i,
                    mergeTagOptions
                  );
                }
                return parsedHtml;
              }

              return i.value;
            }
          );

          return ret;
        }

        function getMergeTagData(obj, toMerge, onComplete, memo) {
          if (memo === undefined) {
            memo = {
              i: 0,
            };
          }

          if (!_.isEmpty(toMerge[memo.i])) {
            try {
              var opts = toMerge[memo.i].options;
              opts.mergeVars = mergeVars;
              toMerge[memo.i].data(
                obj,
                function (data) {
                  memo[toMerge[memo.i].name] = {
                    _uid: toMerge[memo.i].options._uid,
                    tag: toMerge[memo.i].tag,
                    data: data,
                    parse: toMerge[memo.i].parse,
                    options: toMerge[memo.i].options,
                  };
                  memo.i++;

                  return getMergeTagData(obj, toMerge, onComplete, memo);
                },
                opts
              );
            } catch (err) {
              console.log("Error getting data for merge tag: ", err, toMerge);
              memo[toMerge[memo.i].name] = {
                _uid: toMerge[memo.i].options._uid,
                tag: toMerge[memo.i].tag,
                data: [],
                parse: toMerge[memo.i].parse,
                options: toMerge[memo.i].options,
              };
              memo.i++;

              return getMergeTagData(obj, toMerge, onComplete, memo);
            }
          } else {
            delete memo.i;
            return onComplete(memo);
          }
        }

        function getPropertyString(key, obj, propDef) {
          // !REFACTOR THIS OUT (also in entities)
          function isStateful(type) {
            // Checks is an entity type has any workflows.

            var ret = false;
            _.each(type.blueprint, function (field, key) {
              if (!field.is_archived && field.fieldType === "state") {
                ret = true;
              }
            });

            return ret;
          }

          function formatListAsHtml(data, obj, opts) {
            if (_.isEmpty(data)) {
              return "<i>Not set</i>";
            }

            var type = {
              blueprint: false,
            };

            sb.data.db.obj.getBlueprint(data[0].object_bp_type, function (bp) {
              type.blueprint = bp;

              if (
                _.isEmpty(opts.fields) &&
                propDef &&
                propDef[key] &&
                propDef[key].options &&
                propDef[key].options.includeInSelect
              ) {
                opts.fields = propDef[key].options.includeInSelect;
              }

              if (!_.isEmpty(data)) {
                var ret = "<ul>";
                var checklist = isStateful(type);
                if (checklist) {
                  ret = "";
                }

                _.each(data, function (e, i) {
                  var checked = "false";
                  var name = e.name;
                  if (_.isEmpty(name)) {
                    name = "<i>Untitled</i>";
                  }
                  if (e.status === "done") {
                    checked = "true";
                  }
                  var link = sb.data.url.createPageURL("object", {
                    name: e.name,
                    type: e.object_bp_type,
                    id: e.id,
                  });

                  // (PDF lib does not read this style of checklist)
                  if (checklist && opts && opts.mergeForPdf) {
                    if (checked === "true") {
                      ret +=
                        '<span><strong>☑</strong> 	<a href="' +
                        link +
                        '" target="_blank">' +
                        name +
                        "</span></a><br />";
                    } else {
                      ret +=
                        '<span><strong>☐</strong> 	<a href="' +
                        link +
                        '">' +
                        name +
                        "</span></a><br />";
                    }
                  } else {
                    if (checklist) {
                      ret += '<ul data-checked="' + checked + '">';
                    }

                    if (opts.field && opts.field === "name") {
                      ret += '<li><a href="' + link + '">' + name + "</a></li>";
                    } else if (!_.isEmpty(opts.fields)) {
                      ret += '<li><a href="' + link + '">' + name + "</a>";
                      ret += "<ul>";
                      _.each(opts.fields, function (fieldKey) {
                        if (fieldKey === "date_created") {
                          ret +=
                            "<li>" +
                            moment(e.date_created).format("MM/DD/YY") +
                            "</li>";
                        } else {
                          ret +=
                            "<li>" +
                            getFieldHtml(
                              e,
                              fieldKey,
                              type.blueprint[fieldKey]
                            ) +
                            "</li>";
                        }
                      });
                      ret += "</ul>";
                    } else if (opts.field) {
                      ret +=
                        '<li><a href="' +
                        link +
                        '">' +
                        name +
                        "</a>, " +
                        getFieldHtml(
                          e,
                          opts.field,
                          type.blueprint[opts.field]
                        ) +
                        "</li>";
                    } else {
                      ret += '<li><a href="' + link + '">' + name + "</a></li>";
                    }

                    if (checklist) {
                      ret += "</ul>";
                    }
                  }
                });

                if (!checklist) {
                  ret += "</ul>";
                }

                return ret;
              } else {
                return "Nothing to display";
              }
            });
          }

          function getFieldHtml(entity, key, fieldDef) {
            var ret = "";
            if (_.isEmpty(entity)) {
              return "";
            }

            if (!_.isEmpty(fieldDef)) {
              switch (fieldDef.fieldType) {
                case "title":
                  var link = sb.data.url.createPageURL("object", {
                    name: entity.name,
                    type: entity.object_bp_type,
                    id: entity.id,
                  });
                  ret = '<a href="' + link + '">' + entity[key] + "</a>";
                  break;

                case "address":
                  if (!_.isEmpty(entity[key])) {
                    ret =
                      entity[key].street +
                      " " +
                      entity[key].add2 +
                      "<br />" +
                      entity[key].city +
                      " " +
                      entity[key].state +
                      ", " +
                      entity[key].zip +
                      " " +
                      entity[key].country;
                  }
                  break;

                case "attachment":
                  function getSingleAttachmentHTML(attachment) {
                    var ret = "";
                    if (attachment.document_type === "share") {
                      ret =
                        '<a href="' +
                        attachment.share_link +
                        '">' +
                        attachment.name +
                        "</a>";
                    } else {
                      ret = attachment.name;
                    }

                    return ret;
                  }

                  if (
                    typeof entity[key] === "object" &&
                    !_.isEmpty(entity[key])
                  ) {
                    if (Array.isArray(entity[key])) {
                      _.each(entity[key], function (attachment, i) {
                        if (i > 0) {
                          ret += ", ";
                        }
                        ret += getSingleAttachmentHTML(attachment);
                      });
                    } else {
                      ret += getSingleAttachmentHTML(entity[key]);
                    }
                  } else {
                    ret = '<i class="text-muted">Not set</i>';
                  }
                  break;

                case "checklist":
                  _.each(entity[key], function (v, i) {
                    if (i > 0) {
                      ret += ", ";
                    }
                    if (
                      fieldDef.options &&
                      fieldDef.options.options &&
                      _.findWhere(fieldDef.options.options, {
                        value: v.toString(),
                      })
                    ) {
                      ret += _.findWhere(fieldDef.options.options, {
                        value: v.toString(),
                      }).name;
                    }
                  });
                  break;

                case "contacts":
                  if (entity[key]) {
                    if (Array.isArray(entity[key])) {
                      _.each(entity[key], function (c, i) {
                        if (c) {
                          if (i > 0) {
                            ret += ", ";
                          }

                          ret += c.fname + " " + c.lname;
                        }
                      });
                    } else {
                      ret = entity[key].fname + " " + entity[key].lname;
                    }
                  } else {
                    ret = "";
                  }

                  break;

                case "date":
                  var m = moment(entity[key], "YYYY-MM-DD HH:mm:ss.SS");
                  if (m.isValid()) {
                    if (
                      fieldDef.options &&
                      fieldDef.options.dateType === "date"
                    ) {
                      ret += m.local().format("MM/DD/YY");
                    } else {
                      ret += m.local().format("MM/DD/YY, h:mm a");
                    }
                  } else {
                    ret += "Not set";
                  }
                  break;

                case "image":
                  if (!_.isEmpty(entity[key])) {
                    _.each(entity[key], function (img, i) {
                      if (img && img.loc) {
                        ret += '<img src="' + sb.data.files.getURL(img) + '"">';
                      }
                    });

                    return ret;
                  } else {
                    return ret;
                  }
                  break;

                case "edge":
                case "user":
                case "users":
                case "companies":
                  if (entity[key]) {
                    if (Array.isArray(entity[key])) {
                      _.each(entity[key], function (c, i) {
                        if (c) {
                          if (i > 0) {
                            ret += ", ";
                          }

                          if (c && c.object_bp_type) {
                            var link = sb.data.url.createPageURL("object", {
                              name: c.name,
                              type: c.object_bp_type,
                              id: c.id,
                            });

                            ret += '<a href="' + link + '">' + c.name + "</a>";
                          } else {
                            ret += c.name;
                          }
                        }
                      });
                    } else {
                      if (entity[key] && entity[key].object_bp_type) {
                        var link = sb.data.url.createPageURL("object", {
                          name: entity[key].name,
                          type: entity[key].object_bp_type,
                          id: entity[key].id,
                        });

                        ret =
                          '<a href="' + link + '">' + entity[key].name + "</a>";
                      } else {
                        ret = entity[key].name;
                      }
                    }
                  } else {
                    ret = '<i class="text-muted">Not set</i>';
                  }
                  break;

                case "select":
                  if (
                    entity[key] &&
                    _.findWhere(fieldDef.options.options, {
                      value: entity[key].toString(),
                    })
                  ) {
                    ret += _.findWhere(fieldDef.options.options, {
                      value: entity[key].toString(),
                    }).name;
                  } else {
                    ret += "Not set";
                  }
                  break;

                case "timer":
                  var tempTime = moment.duration(entity[key] * 1000);
                  if (entity[key]) {
                    ret += tempTime.hours() + "h " + tempTime.minutes() + "m";
                  } else {
                    ret += "0h 0m";
                  }

                  if (entity[key + "_est"]) {
                    tempTime = moment.duration(entity[key + "_est"] * 1000);
                    ret +=
                      " / " +
                      tempTime.hours() +
                      "h " +
                      tempTime.minutes() +
                      "m";
                  }
                  break;

                default:
                  ret += entity[key];
                  break;
              }
            }

            return ret;
          }
          // !END REFACTOR THIS OUT

          function getLocationHtml(location) {
            if (_.isEmpty(location)) {
              return "N/A";
            }

            return (
              location.street +
              "<br />" +
              location.city +
              " " +
              location.state +
              ", " +
              location.zip +
              " " +
              location.country
            );
          }

          var ret = obj[key];

          if (typeof obj[key] === Object && obj[key] != null) {
            ret = obj[key].name;
          } else {
            ret = obj[key];
          }

          if (
            !_.isEmpty(propDef) &&
            !_.isEmpty(propDef[key]) &&
            !_.isEmpty(propDef[key].fieldType)
          ) {
            if (
              propDef[key].fieldType === "edge" &&
              propDef[key].options &&
              propDef[key].options.multi &&
              propDef[key].options.listView &&
              Array.isArray(obj[key])
            ) {
              ret = formatListAsHtml(
                obj[key],
                {},
                {
                  mergeForPdf: mergeForPdf,
                }
              );
            } else {
              ret = getFieldHtml(obj, key, propDef[key]);
            }
          } else {
            switch (propDef.type) {
              case "date":
                ret = moment(obj[key], "YYYY-MM-DD HH:mm:ss")
                  .local()
                  .format("MM/DD/YY");
                break;

              case "objectIds":
                ret = "";
                if (!_.isEmpty(obj[key])) {
                  _.each(obj[key], function (v, i) {
                    if (i > 0) {
                      ret += ", ";
                    }
                    ret += v.name;
                  });
                }
                break;
            }

            switch (key) {
              case "location":
                ret = getLocationHtml(obj.locations[0]);
                break;

              case "locations":
                ret = "";
                _.each(obj.locations, function (l) {
                  ret += getLocationHtml(l);
                });
                break;

              case "start_time":
                if (
                  obj.related_object &&
                  moment(obj.related_object.start_date).isValid()
                ) {
                  ret = moment(obj.related_object.start_date)
                    .local()
                    .format("h:mm a");
                } else {
                  ret = '<i class="text-muted">Not set</i>';
                }
                break;

              case "end_time":
                if (
                  obj.related_object &&
                  moment(obj.related_object.end_date).isValid()
                ) {
                  ret = moment(obj.related_object.end_date)
                    .local()
                    .format("h:mm a");
                } else {
                  ret = '<i class="text-muted">Not set</i>';
                }
                break;

              case "created_by":
                if (
                  ret !== null &&
                  !_.isUndefined(ret) &&
                  ret.hasOwnProperty("object_bp_type") &&
                  ret.object_bp_type == "users"
                ) {
                  ret = ret.fname + " " + ret.lname;
                }

                break;

              case "manager_signature":
                if (
                  obj &&
                  obj.related_object &&
                  obj.related_object.managers &&
                  obj.related_object.managers[0] &&
                  obj.related_object.managers[0].doc_signature
                ) {
                  ret = obj.related_object.managers[0].doc_signature;
                } else {
                  ret = "";
                }
                break;

              case "manager_link":
                if (
                  obj &&
                  obj.related_object &&
                  obj.related_object.managers &&
                  obj.related_object.managers[0] &&
                  obj.related_object.managers[0].doc_link
                ) {
                  ret =
                    '<a href="' +
                    obj.related_object.managers[0].doc_link +
                    '">' +
                    obj.related_object.managers[0].doc_link +
                    "</a>";
                } else {
                  ret = "";
                }
                break;

              case "invoice_value":
                if (
                  obj.hasOwnProperty("invoice_value") &&
                  (typeof obj.invoice_value == "number" ||
                    parseInt(obj.invoice_value))
                ) {
                  ret =
                    "$ " + (parseInt(obj.invoice_value) / 100).formatMoney(2);
                } else {
                  ret = "<i> Not set </i>";
                }

                break;
            }
          }

          return ret;
        }

        function getObjectData(objectType, obj, callback) {
          function getMergeTagOptionsFromTagHtml(tag, htmlString) {
            var opts = [];
            var doc = new DOMParser().parseFromString(htmlString, "text/html");
            doc
              .querySelectorAll("[tag-id]")
              .forEach(function (node, index, nodeList) {
                if (node.getAttribute("tag-id") === tag.tag) {
                  var tmp = {
                    _uid: node.getAttribute("tag-uid"),
                  };
                  _.each(tag.options, function (option, key) {
                    tmp[key] = node.getAttribute(key);
                  });

                  opts.push(tmp);
                }
              });

            return opts;
          }

          var toMerge = [];
          var templatePropertyName = obj.is_template
            ? "template"
            : "html_string";
          if (obj && obj.justMerge && !_.isEmpty(obj.template)) {
            templatePropertyName = "template";
          } else if (_.isEmpty(obj.template) && !_.isEmpty(obj.html_string)) {
            templatePropertyName = "html_string";
          }

          _.each(MergeTags, function (tag) {
            if (obj[templatePropertyName]) {
              var tagInstance = _.clone(tag);
              if (tagInstance.options) {
                if (
                  obj[templatePropertyName].indexOf(
                    'tag-id="' + tag.tag + '"'
                  ) !== -1
                ) {
                  var tagOpts = getMergeTagOptionsFromTagHtml(
                    tag,
                    obj[templatePropertyName]
                  );

                  _.each(tagOpts, function (opt, i) {
                    tagInstance = _.clone(tagInstance);
                    tagInstance.name += i;
                    tagInstance.options = opt;

                    toMerge.push(tagInstance);
                  });
                }
              } else if (
                obj[templatePropertyName].indexOf("{{" + tag.tag + "}}") !== -1
              ) {
                var tagOpts = getMergeTagOptions(
                  tag,
                  obj[templatePropertyName]
                );

                _.each(tagOpts, function (opt, i) {
                  tagInstance = _.clone(tagInstance);
                  tagInstance.name += i;
                  tagInstance.options = opt;

                  toMerge.push(tagInstance);
                });
              }
            }
          });

          getMergeTagData(obj, toMerge, function (mergeData) {
            if (obj.related_object && !obj.space) {
              if (
                obj.related_object.object_bp_type.charAt(0) === "#" ||
                obj.related_object.object_bp_type === "users"
              ) {
                return callback(obj.related_object);
              }

              switch (obj.merge_type) {
                case "contact":
                  callback({
                    contactObj: contact,
                    company: company,
                    _mergeData: mergeData,
                  });
                  break;

                case "portal":
                case "proposal":
                case "space":
                  switch (obj.status) {
                    case "Approval Requested":
                    case "Approved":
                      callback(false);
                      break;

                    default:
                      if (
                        project &&
                        project.hasOwnProperty("proposal") &&
                        project.proposal
                      ) {
                        function getSchedulePricing(schedule, onComplete) {
                          if (!_.isEmpty(schedule) && schedule.length > 0) {
                            sb.notify({
                              type: "get-schedule-price",
                              data: {
                                schedule: schedule[0].id,
                                price: function (pricingObj) {
                                  onComplete(pricingObj);
                                },
                              },
                            });
                          } else {
                            onComplete({});
                          }
                        }

                        getSchedulePricing(schedule, function (pricingObj) {
                          menu._labor = pricingObj;
                          menu._pricingBreakdown = pricingBreakdown;

                          sb.notify({
                            type: "get-menu-total-pricing",
                            data: {
                              menu: menu,
                              obj: obj,
                              onComplete: function (response) {

                                var categories = response.categories;
                                var pricelist = response.pricelist;

                                  sb.notify({
                                    type: "get-menu-line-item-pricing",
                                    data: {
                                      menu: menu,
                                      obj: obj,
                                      workorder: project,
                                      company: company,
                                      schedule: schedule,
                                      cache: false,
                                      callback: function (pricedMenu) {

                                        var items = [];
                                        _.each(pricedMenu.sections, function (section) {
                                          _.each(section.items, function (item) {
                                            if(item.item && item.item.id) {
                                              items.push(item.item.id);
                                            }
                                          });

                                        });

                                        sb.data.db.controller('getFullRecipes', {itemsId: items}, function(fullrecipes) {

                                          callback({
                                            projectObj: project,
                                            contactObj: contact,
                                            menu: menu,
                                            pricedMenu: pricedMenu,
                                            pricelist: pricelist,
                                            categories: categories,
                                            invoices: invoices,
                                            logo: company_logo,
                                            _mergeData: mergeData,
                                            company: company,
                                            pricingBreakdown: pricingBreakdown,
                                            recipes: fullrecipes || []
                                          });

                                        });
                                      },
                                    },
                                  });

                              },
                            },
                          });
                        });
                      } else {
                        callback(false);
                      }
                  }

                  break;
              }

              // For general use, not tied to an object directly,
              // or if treating the space obj as the root obj here.
            } else if (
              (obj.justMerge && obj.template) ||
              (obj.space &&
                (obj.space.object_bp_type === "users" ||
                  (obj.space.object_bp_type === "groups" &&
                    obj.space.group_type === "Team") ||
                  (typeof obj.space.object_bp_type === "string" &&
                    obj.space.object_bp_type.substr(0, 1) === "#")))
            ) {
              callback(mergeData);
            } else {
              callback(false);
            }
          });
        }

        function mergeEntityDoc(obj, mergeData) {
          function replaceMergeTagNodeWithHTML(
            // Replaces the node containing the ui/option data in the template
            // with the html produced by the tag/view.
            // . If this becomes slow with large docs, we can batch this to
            //   avoid translating from string to dom to string.
            mergeData, // [Object] 	| a list of the meta data containing
            // 			 	  identifying info about the tag, as
            // 			 	  well as the html to replace the
            // 			 	  node w/.
            htmlString // String 		| the template html
            // => 			   String 		| the updated html string
          ) {
            // Translate to dom obj
            var doc = new DOMParser().parseFromString(htmlString, "text/html");

            // Replace html of tag node w/html produced by tag.
            doc
              .querySelectorAll("[tag-id]")
              .forEach(function (node, index, nodeList) {
                var merge = _.findWhere(mergeData, {
                  _uid: node.getAttribute("tag-uid"),
                });

                if (merge && merge.tag === node.getAttribute("tag-id")) {
                  try {
                    var opts = merge.options;
                    opts.mergeVars = mergeVars;
                    node.outerHTML = merge.parse(merge.data, obj, opts);
                  } catch (err) {
                    node.outerHtml = "";
                    console.log("Error parsing merge tag html: ", err, merge);
                  }
                }
              });

            return doc.documentElement.outerHTML;
          }

          var mentions = [];
          var entityType = {};

          if (obj.related_object && obj.related_object.object_bp_type) {
            entityType = _.findWhere(EntityTypes, {
              bp_name: obj.related_object.object_bp_type.substr(1),
            });
          }

          var htmlString = obj.html_string || "";
          var fieldReplaceString = "________";

          if (!_.isEmpty(entityType)) {
            _.each(entityType.blueprint, function (field, key) {
              fieldReplaceString = getPropertyString(
                key,
                obj.related_object,
                entityType.blueprint
              );

              mentions.push({
                objectType: "{{" + entityType.name + "." + field.name + "}}",
                name: entityType.name + "." + field.name,
                value: fieldReplaceString,
              });
            });

            // Inherited fields in subsets from parent set
            if (entityType._class) {
              var parentClass = _.findWhere(appConfig.Types, {
                id: entityType._class,
              });

              if (parentClass) {
                _.each(parentClass.blueprint, function (field, key) {
                  fieldReplaceString = getPropertyString(
                    key,
                    obj.related_object,
                    parentClass.blueprint
                  );

                  mentions.push({
                    objectType:
                      "{{" + parentClass.name + "." + field.name + "}}",
                    name: parentClass.name + "." + field.name,
                    value: fieldReplaceString,
                  });
                });
              }
            }
          }

          _.each(mentions, function (i) {
            htmlString = htmlString.replace(
              new RegExp(prepForRegex(i.objectType), "g"),
              i.value
            );
          });

          // Only do doc-translation if tags of that type exist.
          // For non-doc like merging (like plain text in title)
          // TODO: This should instead trigger off of a flag in startup of this
          // 		 view, so that merge tags can be used to template values for
          // 		 different kinds of fields. Find for now, since only text field
          // 		 has access to these kinds of tags so far.
          var isDoc = false;

          /*
				if (mergeData) {

					_.each(mergeData, function (merge) {

						if (
							(
								merge.tag === 'sum'
								|| merge.tag === 'count'
								|| merge.tag === 'listset'
								|| merge.tag === 'breakout'
							)
							//! TODO: Have this check for just 'options'--first will
							//  need to port over older tags here.
							&& !_.isEmpty(merge.options)
						) {

							isDoc = true;

						} else {

							var tagTxt = '{{'+ merge.tag +'}}';

							// Remove args, if they exist
							if (
								Array.isArray(merge.options)
								&& !_.isEmpty(merge.options)
							) {
								tagTxt += '('+ merge.options.join(',') +')';
							}

							tagTxt = prepForRegex(tagTxt);

							htmlString = htmlString.replace(
								new RegExp(
									tagTxt
									, "g"
								)
								, merge.parse(
									merge.data, obj
								)
							);

						}

					});

				}
	*/

          if (mergeData) {
            _.each(mergeData, function (merge) {
              if (
                (merge.tag === "sum" ||
                  merge.tag === "count" ||
                  merge.tag === "listset" ||
                  merge.tag === "breakout") &&
                //! TODO: Have this check for just 'options'--first will
                //  need to port over older tags here.
                !_.isEmpty(merge.options)
              ) {
                isDoc = true;
              } else {
                var tagTxt = "{{" + merge.tag + "}}";

                // Remove args, if they exist
                if (Array.isArray(merge.options) && !_.isEmpty(merge.options)) {
                  tagTxt += "(" + merge.options.join(",") + ")";
                }

                tagTxt = prepForRegex(tagTxt);
                var parsedHtml = "";
                try {
                  var opts = {
                    mergeVars: mergeVars,
                  };
                  parsedHtml = merge.parse(
                    merge.data,
                    obj.related_object,
                    opts
                  );
                } catch (err) {
                  console.log("Error parsing merge tag html: ", err, merge);
                }

                htmlString = htmlString.replace(
                  new RegExp(tagTxt, "g"),
                  parsedHtml
                );
              }
            });
          }

          if (isDoc) {
            htmlString = replaceMergeTagNodeWithHTML(mergeData, htmlString);
          }

          return htmlString;
        }

        function mergeBackEndViews(obj, onComplete) {
          var mergeVarsPost = _.clone(mergeVars);
          if (
            mergeVarsPost &&
            mergeVarsPost.today &&
            moment.isMoment(mergeVarsPost.today)
          ) {
            mergeVarsPost.today = mergeVarsPost.today.format("YYYY-MM-DD");
            // mergeVarsPost.today = mergeVarsPost.today.format('YYYY-MM-DD HH:mm:ss.SS');
          }

          if (
            !_.isEmpty(obj.related_object) &&
            obj.related_object.id
            // && typeof obj.html_string === 'string'
          ) {
            if (
              !obj.id &&
              obj.state &&
              obj.state.entity &&
              obj.state.entity.id
            ) {
              var contextId = obj.state.entity.id;
            } else {
              var contextId = obj.related_object.id;
            }

            var Data = {
              contextId: contextId,
              templateId: obj.id,
              mergeVars: mergeVarsPost,
            };
            sb.data.db.obj.getById('', obj.id, function(res){
              console.log('MERGE FLOW: Res', res);
              onComplete(res.html_string);
            });
            // databaseConnection.mergeEndpoint(Data, function (html) {
            //   obj.html_string = html;
            //   onComplete(html);
            // });
          } else if (
            obj.justMerge &&
            !_.isEmpty(obj.state) &&
            typeof obj.template === "string"
          ) {
            var mergeSpace = +sb.data.cookie.get("uid");
            if (obj.state) {
              if (obj.state.team && obj.state.team.id > 0) {
                mergeSpace = obj.state.team.id;
              }
              if (obj.state.project && obj.state.project.id > 0) {
                mergeSpace = obj.state.project.id;
              }
              if (obj.state.entity && obj.state.entity > 0) {
                mergeSpace = obj.state.entity;
              }
            }

            sb.data.db.obj.runSteps(
              {
                merge: {
                  template: obj.template,
                  parent: mergeSpace,
                  format: obj.state.format || "html",
                  // , state: 	mergeState
                  // , context: 	{
                  // team: obj.state.team.id
                  // , project: obj.state.project.id
                  // , type: obj.entityType.id
                  // }
                  mergeVars: mergeVarsPost,
                },
              },
              mergeSpace,
              function (merged) {
                if (merged && merged["msg"] && merged["msg"]["memo"]) {
                  obj.template = merged["msg"]["memo"];
                }

                onComplete(obj.template);
              },
              true // receive the memo in the response
            );
          } else {
            onComplete(obj.html_string);
          }
        }

        mergeBackEndViews(obj, function (mergedHtml) {
          console.log('MERGE FLOW: Merged HTML', mergedHtml);
          // For custom entities
          if (
            obj.related_object &&
            obj.related_object.object_bp_type.charAt(0) === "#"
          ) {
            getEntityDocData(obj, function (mergeData) {
              callback(mergeEntityDoc(obj, mergeData));
            });
            return;

            //!! Paths for merge process
          } else if (
            (obj.justMerge && obj.state && !_.isEmpty(obj.template)) ||
            // If a space is set, and the space is a user, team,
            // or custom entity.
            (obj.space &&
              (obj.space.object_bp_type === "users" ||
                (obj.space.object_bp_type === "groups" &&
                  obj.space.group_type === "Team") ||
                (typeof obj.space.object_bp_type === "string" &&
                  obj.space.object_bp_type.substr(0, 1) === "#")))
          ) {
            var objType = "#";
            var templateStringPropertyName = "template";
            if (obj.space && obj.space.object_bp_type) {
              objType = obj.space.object_bp_type;
              templateStringPropertyName = "html_string";
            }

            getObjectData("#", obj, function (mergeData) {
              callback(
                mergeEntityDoc(
                  {
                    related_object: obj.state,
                    html_string: obj[templateStringPropertyName],
                  },
                  mergeData
                )
              );
            });
            return;
          }
console.log('MERGE FLOW: Switch', obj.merge_type);
          switch (obj.merge_type) {
            case "contact":
              getObjectData(obj.merge_type, obj, function (objectData) {
                var mentions = [];
                var allBlueprints = [];
                allBlueprints.push(
                  {
                    type: "contact",
                    blueprints: contactBP,
                  },
                  {
                    type: "company",
                    blueprints: companyBP,
                  }
                );

                var fieldReplaceString = "";
                _.each(allBlueprints, function (bp) {
                  _.each(bp.blueprints, function (bpObj, key) {
                    if (fieldsToReject.indexOf(key) == -1) {
                      if (!obj.main_contact) {
                        mentions.push({
                          objectType:
                            "{{" +
                            bp.type.toUpperCase() +
                            "." +
                            bpObj.name.toUpperCase().replace(" ", "_") +
                            "}}",
                          name: bpObj.name,
                          value: "__________________________",
                        });
                      } else {
                        switch (bp.type) {
                          case "contact":
                            if (objectData.contactObj) {
                              if (
                                typeof objectData.contactObj[key] === "object"
                              ) {
                                fieldReplaceString =
                                  objectData.contactObj[key] != null
                                    ? objectData.contactObj[key].name
                                    : "";
                              } else {
                                fieldReplaceString = objectData.contactObj[key];
                              }

                              mentions.push({
                                objectType:
                                  "{{" +
                                  bp.type.toUpperCase() +
                                  "." +
                                  bpObj.name.toUpperCase().replace(" ", "_") +
                                  "}}",
                                name: bpObj.name,
                                value: fieldReplaceString,
                              });
                            }

                            break;

                          case "company":
                            fieldReplaceString = "";

                            if (
                              objectData.contactObj.contactObj &&
                              objectData.contactObj.contactObj.company &&
                              typeof objectData.contactObj.company[key] ===
                                "object"
                            ) {
                              fieldReplaceString =
                                objectData.contactObj.company[key] != null
                                  ? objectData.contactObj.company[key].name
                                  : "";
                            } else {
                              if (
                                objectData.company &&
                                !_.isEmpty(objectData) &&
                                !_.isEmpty(objectData.company) &&
                                typeof objectData.company[key] === "object"
                              ) {
                                if (
                                  bpObj.objectType == "file_meta_data" &&
                                  objectData.company[key].hasOwnProperty("id")
                                ) {
                                  fieldReplaceString =
                                    '<img src="' +
                                    sb.data.files.getURL(
                                      objectData.company[key]
                                    ) +
                                    '" style="max-width:100px;">';
                                } else if (objectData.company[key] != null) {
                                  fieldReplaceString =
                                    objectData.company[key].name;
                                }
                              } else {
                                if (
                                  bpObj.type == "date" &&
                                  objectData.company[key] != null
                                ) {
                                  objectData.company[key] = moment(
                                    objectData.company[key]
                                  ).format("M/D/YYYY");
                                }

                                fieldReplaceString =
                                  objectData.company != null &&
                                  objectData.company[key] != null
                                    ? objectData.company[key]
                                    : "";
                              }
                            }

                            mentions.push({
                              objectType:
                                "{{" +
                                bp.type.toUpperCase() +
                                "." +
                                bpObj.name.toUpperCase().replace(" ", "_") +
                                "}}",
                              name: bpObj.name,
                              value: fieldReplaceString,
                            });

                            break;
                        }
                      }
                    }
                  });
                });

                if (obj.status == "Signed") {
                  mentions.push({
                    objectType: "{{PLEASE SIGN HERE}}",
                    name: "signature",
                    value:
                      '<img width="150px" src="' +
                      sb.data.files.getURL(obj.signatures) +
                      '"><br />' +
                      obj.signer_name +
                      " @ " +
                      moment(obj.signed_on).local().format("LLLL"),
                  });
                } else {
                  mentions.push({
                    objectType: "{{PLEASE SIGN HERE}}",
                    name: "signature",
                    value:
                      "<small>PLEASE SIGN HERE</small> ___________________________________________",
                  });
                }

                var htmlString = obj.html_string || "";

                if (htmlString) {
                  _.each(mentions, function (i) {
                    htmlString = htmlString.replace(
                      new RegExp(i.objectType, "g"),
                      i.value
                    );
                  });
                }

                callback(htmlString);
              });
              break;

            case "proposal":
              getObjectData(obj.merge_type, obj, function (objectData) {
                var mentions = [];
                var allBlueprints = [];
                allBlueprints.push(
                  {
                    type: "proposal",
                    blueprints: proposalBP,
                  },
                  {
                    type: "contact",
                    blueprints: contactBP,
                  },
                  {
                    type: "company",
                    blueprints: companyBP,
                  }
                );

                var fieldReplaceString = "";
                _.each(allBlueprints, function (bp) {
                  _.each(bp.blueprints, function (bpObj, key) {
                    if (fieldsToReject.indexOf(key) == -1) {
                      if (!objectData) {
                        mentions.push({
                          objectType:
                            "{{" +
                            bp.type.toUpperCase() +
                            "." +
                            bpObj.name.toUpperCase().replace(" ", "_") +
                            "}}",
                          name: bpObj.name,
                          value: "__________________________",
                        });
                      } else {
                        objectData.payNow = payNow;
                        objectData.invoiceSystem = invoiceSysObjs[0];

                        switch (bp.type) {
                          case "contact":
                            if (objectData.contactObj) {
                              fieldReplaceString = "";

                              if (objectData.contactObj[key]) {
                                if (
                                  typeof objectData.contactObj[key] === "object"
                                ) {
                                  fieldReplaceString =
                                    objectData.contactObj[key] != null
                                      ? objectData.contactObj[key].name
                                      : "";
                                } else {
                                  fieldReplaceString =
                                    objectData.contactObj[key];
                                }
                              }
                              var output = {
                                objectType:
                                  "{{" +
                                  bp.type.toUpperCase() +
                                  "." +
                                  bpObj.name.toUpperCase().replace(" ", "_") +
                                  "}}",
                                name: bpObj.name,
                                value: fieldReplaceString,
                              };

                              mentions.push(output);
                            }

                            break;

                          case "company":
                            if (objectData.contactObj) {
                              fieldReplaceString = "";

                              if (
                                objectData.contactObj.contactObj &&
                                objectData.contactObj.contactObj.company &&
                                typeof objectData.contactObj.company[key] ===
                                  "object"
                              ) {
                                fieldReplaceString =
                                  objectData.contactObj.company[key] != null
                                    ? objectData.contactObj.company[key].name
                                    : "";
                              } else {
                                if (
                                  objectData.company &&
                                  typeof objectData.company[key] === "object" &&
                                  !_.isEmpty(objectData.company[key])
                                ) {
                                  if (
                                    bpObj.objectType == "file_meta_data" &&
                                    objectData.company[key].hasOwnProperty("id")
                                  ) {
                                    fieldReplaceString =
                                      '<img src="' +
                                      sb.data.files.getURL(
                                        objectData.company[key]
                                      ) +
                                      '" style="max-width:100px;">';
                                  } else if (objectData.company[key] != null) {
                                    fieldReplaceString =
                                      objectData.company[key].name;
                                  }
                                } else if (!_.isEmpty(objectData.company)) {
                                  if (
                                    bpObj.type == "date" &&
                                    objectData.company[key] != null
                                  ) {
                                    objectData.company[key] = moment(
                                      objectData.company[key]
                                    ).format("M/D/YYYY");
                                  }

                                  fieldReplaceString =
                                    objectData.company != null &&
                                    objectData.company[key] != null
                                      ? objectData.company[key]
                                      : "";
                                }
                              }

                              var output = {
                                objectType:
                                  "{{" +
                                  bp.type.toUpperCase() +
                                  "." +
                                  bpObj.name.toUpperCase().replace(" ", "_") +
                                  "}}",
                                name: bpObj.name,
                                value: fieldReplaceString,
                              };

                              mentions.push(output);
                            }

                            break;

                          case "proposal":
                            if (objectData.projectObj) {
                              fieldReplaceString = getPropertyString(
                                key,
                                objectData.projectObj,
                                bpObj
                              );

                              mentions.push({
                                objectType:
                                  "{{" +
                                  bp.type.toUpperCase() +
                                  "." +
                                  bpObj.name.toUpperCase().replace(" ", "_") +
                                  "}}",
                                name: bpObj.name,
                                value: fieldReplaceString,
                              });
                            }

                            break;
                        }
                      }
                    }
                  });
                });

                if (obj.status == "Signed") {
                  mentions.push({
                    objectType: "{{PLEASE SIGN HERE}}",
                    name: "signature",
                    value:
                      '<img width="150px" src="' +
                      sb.data.files.getURL(obj.signatures) +
                      '"><br />' +
                      obj.signer_name +
                      " @ " +
                      moment(obj.signed_on).local().format("LLLL"),
                  });
                } else {
                  if (opts) {
                    mentions.push({
                      objectType: "{{PLEASE SIGN HERE}}",
                      name: "signature",
                      value:
                        "<small>PLEASE SIGN HERE</small> ___________________________________________",
                    });
                  } else {
                    mentions.push({
                      objectType: "{{PLEASE SIGN HERE}}",
                      name: "signature",
                      value:
                        '<div class="align left"><p><small>PLEASE SIGN HERE</small></p><p>___________________________________________</p><a id="startSignature" class="ui green left floated compact button">Click To Sign</a></div>',
                    });
                  }
                }

                mentions.push({
                  objectType: "{{PROPOSAL.START_TIME}}",
                  name: "start_time",
                  value: getPropertyString("start_time", obj, {}),
                });
                mentions.push({
                  objectType: "{{PROPOSAL.END_TIME}}",
                  name: "end_time",
                  value: getPropertyString("end_time", obj, {}),
                });
                mentions.push({
                  objectType: "{{PROPOSAL.MANAGER_SIGNATURE}}",
                  name: "start_time",
                  value: getPropertyString("manager_signature", obj, {}),
                });
                mentions.push({
                  objectType: "{{PROPOSAL.MANAGER_LINK}}",
                  name: "end_time",
                  value: getPropertyString("manager_link", obj, {}),
                });

                // page break
                if (!objectData) {
                  // add menu html
                  mentions.push({
                    objectType: "{{PAGEBREAK}}",
                    name: "pagebreak",
                    value: "INVOICE NOTE GOES HERE",
                  });
                } else {
                  // add menu html
                  var itemListString = "";
                  itemListString += "";
                  mentions.push({
                    objectType: "{{PAGEBREAK}}",
                    name: "pagebreak",
                    value: itemListString,
                  });
                }

                if (!objectData) {
                  // add menu html
                  mentions.push({
                    objectType: "{{PROPOSAL.INVOICE_NOTE}}",
                    name: "menu_note",
                    value: "INVOICE NOTE GOES HERE",
                  });
                } else {
                  // add menu html
                  var itemListString = "";

                  itemListString += sectionTypes.menuNote.getHTML(
                    objectData.pricedMenu,
                    {},
                    {},
                    {
                      includeDiscounts: true,
                      includeLineItemPrice: true,
                      includeLineItemQty: true,
                      pricingBreakdown: objectData.pricingBreakdown,
                    }
                  );
                  mentions.push({
                    objectType: "{{PROPOSAL.INVOICE_NOTE}}",
                    name: "menu_note",
                    value: itemListString,
                  });
                }

                // Item list, grouped by menu section.
                if (!objectData) {
                  // add menu html
                  mentions.push({
                    objectType: "{{PROPOSAL.ITEM_LIST}}",
                    name: "item_list",
                    value: "ITEM LIST GOES HERE",
                  });
                } else {
                  // add menu html
                  var itemListString = "";
                  itemListString += sectionTypes.menu.getHTML(
                    objectData.pricedMenu,
                    {},
                    {},
                    {
                      includeDiscounts: true,
                      includeLineItemPrice: true,
                      includeLineItemQty: true,
                      includeRate: true,
                      includeDescription: true,
                      includeNote: false,
                    }
                  );
                  mentions.push({
                    objectType: "{{PROPOSAL.ITEM_LIST}}",
                    name: "item_list",
                    value: itemListString,
                  });
                }

                // Item list, grouped by category.
                if (!objectData) {
                  // add menu html
                  mentions.push({
                    objectType: "{{PROPOSAL.ITEM_LIST_BY_CAT}}",
                    name: "item_list_by_cat",
                    value: "ITEM LIST GOES HERE",
                  });
                } else {
                  // add menu html
                  var itemListString = "";
                  itemListString += sectionTypes.menu.getHTML(
                    objectData.pricedMenu,
                    {},
                    {},
                    {
                      includeDiscounts: true,
                      includeLineItemPrice: true,
                      includeLineItemQty: true,
                      groupByCat: true,
                      recipes: objectData.recipes || []
                    }
                  );
                  mentions.push({
                    objectType: "{{PROPOSAL.ITEM_LIST_BY_CAT}}",
                    name: "item_list_by_cat",
                    value: itemListString,
                  });
                }

                // Item list, no pricing.
                if (!objectData) {
                  // add menu html
                  mentions.push({
                    objectType: "{{PROPOSAL.SIMPLE_ITEM_LIST}}",
                    name: "simple_item_list",
                    value: "SIMPLE ITEM LIST GOES HERE",
                  });
                } else {
                  // add menu html
                  var itemListString = "";
                  itemListString += sectionTypes.menu.getHTML(
                    objectData.pricedMenu,
                    {},
                    {},
                    {
                      includeDiscounts: false, //usman
                      includeLineItemPrice: false,
                      includeLineItemQty: true,
                      includeRate: false,
                      showPricing: false,
                      showSections: false,
                      recipes: objectData.recipes || []
                    }
                  );
                  mentions.push({
                    objectType: "{{PROPOSAL.SIMPLE_ITEM_LIST}}",
                    name: "simple_item_list",
                    value: itemListString,
                  });
                }

                // Item list, just qty, no pricing, no sections.
                if (!objectData) {
                  // add menu html
                  mentions.push({
                    objectType: "{{PROPOSAL.ITEM_LIST_JUST_QTY}}",
                    name: "item_list_just_qty",
                    value: "SIMPLE ITEM LIST GOES HERE",
                  });
                } else {
                  // add menu html
                  var itemListString = "";
                  itemListString += sectionTypes.menu.getHTML(
                    objectData.pricedMenu,
                    {},
                    {},
                    {
                      includeDiscounts: true,
                      includeLineItemPrice: false,
                      includeLineItemQty: true,
                      showPricing: false,
                      showSections: true,
                      includeRate: false,
                      recipes: objectData.recipes || []
                    }
                  );
                  mentions.push({
                    objectType: "{{PROPOSAL.ITEM_LIST_JUST_QTY}}",
                    name: "item_list_just_qty",
                    value: itemListString,
                  });
                }

                if (!objectData) {
                  // add menu html
                  mentions.push({
                    objectType: "{{PROPOSAL.ITEM_LIST_TOTAL}}",
                    name: "item_list_total",
                    value: "SIMPLE ITEM LIST W/ TOTAL GOES HERE",
                  });
                } else {
                  // add menu html
                  var itemListString = "";
                  itemListString += sectionTypes.menu.getHTML(
                    objectData.pricedMenu,
                    {},
                    {},
                    {
                      includeDiscounts: false, //usman
                      includeLineItemPrice: true,
                      includeLineItemQty: false,
                      includeRate: false,
                      showPricing: false,
                      showSections: false,
                      recipes: objectData.recipes || []
                    }
                  );

                  mentions.push({
                    objectType: "{{PROPOSAL.ITEM_LIST_TOTAL}}",
                    name: "item_list_total",
                    value: itemListString,
                  });
                }

                if (!objectData) {
                  // add menu html
                  mentions.push({
                    objectType: "{{PROPOSAL.INVOICE_HEADER}}",
                    name: "invoice_header",
                    value: "INVOICE GOES HERE",
                  });
                } else {
                  // add menu html
                  var itemListString = "";
                  itemListString += createMergedInvoiceHeaderHTML(objectData);
                  mentions.push({
                    objectType: "{{PROPOSAL.INVOICE_HEADER}}",
                    name: "invoice_header",
                    value: itemListString,
                  });
                }

                if (!objectData) {
                  // add menu html
                  mentions.push({
                    objectType: "{{PROPOSAL.INVOICE}}",
                    name: "invoice",
                    value: "INVOICE GOES HERE",
                  });
                } else {

                  //here add the fullitems

                  // add menu html
                  var itemListString = "";
                  itemListString += createMergedInvoiceHTML(objectData, false, false, callback);
                  //callback(htmlString);

                  mentions.push({
                    objectType: "{{PROPOSAL.INVOICE}}",
                    name: "invoice",
                    value: itemListString,
                  });
                }

                if (!objectData) {
                  // add menu html
                  mentions.push({
                    objectType: "{{PROPOSAL.INVOICE_NLP}}",
                    name: "invoice_nlp",
                    value: "NLP INVOICE GOES HERE",
                  });
                } else {
                  // add menu html
                  var itemListString = "";

                  itemListString += createMergedInvoiceHTML(objectData, null, {
                    showPayments: true,
                    showSections: {
                      sectionBorder: false,
                      showDesc: true,
                      sectionNotes: { color: "black" },
                    },
                    //, showBreakdown: 	{ sectionBorder: false, inTable: false }
                    showDiscountsPerLineItem: false,
                    absolutePrice: true,
                    includeLineItemPrice: false,
                    //, includeDiscounts: false
                  });

                  mentions.push({
                    objectType: "{{PROPOSAL.INVOICE_NLP}}",
                    name: "invoice_nlp",
                    value: itemListString,
                  });
                }

                if (!objectData) {
                  // add invoice html
                  mentions.push({
                    objectType: "{{PROPOSAL.PAYMENT_SCHEDULE}}",
                    name: "invoice_list",
                    value: "PAYMENT SCHEDULE LIST GOES HERE",
                  });
                } else {
                  // add invoice html
                  var invoiceListString = createMergedInvoiceHTML(objectData);

                  mentions.push({
                    objectType: "{{PROPOSAL.PAYMENT_SCHEDULE}}",
                    name: "invoice_list",
                    value: invoiceListString,
                  });
                }

                if (!objectData) {
                  var companyLogoTxt = "";
                  if (logo[0] && logo[0].company_logo) {
                    companyLogoTxt = sb.data.files.getURL(logo[0].company_logo);
                  }

                  // add company logo html
                  mentions.push({
                    objectType: "{{PROPOSAL.LOGO}}",
                    name: "logo",
                    value:
                      '<img src="' +
                      companyLogoTxt +
                      '" style="max-width:200px;">',
                  });
                } else {
                  var companyLogoTxt = "";
                  if (objectData.logo && objectData.logo.company_logo) {
                    companyLogoTxt = sb.data.files.getURL(
                      objectData.logo.company_logo
                    );
                  }
                  // add company logo html
                  mentions.push({
                    objectType: "{{PROPOSAL.LOGO}}",
                    name: "logo",
                    value:
                      '<img src="' +
                      companyLogoTxt +
                      '" style="max-width:200px;">',
                  });
                }

                htmlString = "";
                if (obj.html_string) {
                  htmlString = obj.html_string;
                } else {
                  if (customString) {
                    htmlString = customString;
                  }
                }

                _.each(objectData._mergeData, function (merge) {
                  var parsedHtml = "";
                  try {
                    parsedHtml = merge.parse(merge.data, objectData);
                  } catch (err) {
                    console.log("Error parsing merge tag html: ", err, merge);
                  }

                  mentions.push({
                    objectType: "{{" + merge.tag + "}}",
                    name: merge.name,
                    value: parsedHtml,
                    parse: merge.parse.bind({}, merge.data, objectData),
                  });
                });

                var reg = "";

                _.each(mentions, function (i) {
                  var chunks = [];

                  // Started on passing options through to merge tags.
                  var updatedHtml = htmlString;
                  htmlString = htmlString.replace(
                    new RegExp(i.objectType, "g"),
                    function (a, index, c) {
                      index = index + i.objectType.length;
                      var chunk = "";
                      var nextStr = "";
                      var mergeTagOptionsStr = "";
                      var mergeTagOptions = [];

                      if (updatedHtml[index] === "(") {
                        chunk = updatedHtml.slice(index + 1);
                        updatedHtml.replace(chunk, "");

                        chunk = chunk.slice(0, chunk.indexOf(")"));
                        chunks.push(chunk);

                        mergeTagOptions = _.compact(chunk.split(","));

                        // Won't need to do this anymore once ui enforces good syntax.
                        _.each(mergeTagOptions, function (t, i) {
                          mergeTagOptions[i] = t.replace("&amp;", "");
                          mergeTagOptions[i] = mergeTagOptions[i].split("<")[0];
                        });
                      }

                      if (!_.isEmpty(chunk) && typeof i.parse === "function") {
                        var parsedHtml = "";
                        try {
                          parsedHtml = i.parse(mergeTagOptions);
                        } catch (err) {
                          console.log(
                            "Error parsing merge tag html: ",
                            err,
                            i,
                            mergeTagOptions
                          );
                        }
                        return parsedHtml;
                      }

                      return i.value;
                    }
                  );

                  _.each(chunks, function (chunk) {
                    if (!_.isEmpty(chunk)) {
                      htmlString = htmlString.replace("(" + chunk + ")", "");
                    }
                  });
                });

                callback(htmlString);
              });
              break;

            case "space":
              getObjectData(obj.merge_type, obj, function (objectData) {
                var mentions = [];
                var allBlueprints = [];
                var fieldReplaceString = "";
                _.each(allBlueprints, function (bp) {
                  _.each(bp.blueprints, function (bpObj, key) {
                    if (fieldsToReject.indexOf(key) == -1) {
                      if (!objectData) {
                        mentions.push({
                          objectType:
                            "{{" +
                            bp.type.toUpperCase() +
                            "." +
                            bpObj.name.toUpperCase().replace(" ", "_") +
                            "}}",
                          name: bpObj.name,
                          value: "__________________________",
                        });
                      }
                    }
                  });
                });

                if (obj.status == "Signed") {
                  mentions.push({
                    objectType: "{{PLEASE SIGN HERE}}",
                    name: "signature",
                    value:
                      '<div class="align left"><img width="150px" src="' +
                      sb.data.files.getURL(obj.signatures) +
                      '"><br /><p class="left floated">' +
                      obj.signer_name +
                      " @ " +
                      moment(obj.signed_on).local().format("LLLL") +
                      "</p></div>",
                  });
                } else {
                  if (opts) {
                    mentions.push({
                      objectType: "{{PLEASE SIGN HERE}}",
                      name: "signature",
                      value:
                        "<small>PLEASE SIGN HERE</small> ___________________________________________",
                    });
                  } else {
                    mentions.push({
                      objectType: "{{PLEASE SIGN HERE}}",
                      name: "signature",
                      value:
                        '<div class="align left"><p><small>PLEASE SIGN HERE</small></p><p>___________________________________________</p><a id="startSignature" class="ui green left floated compact button">Click To Sign</a></div>',
                    });
                  }
                }

                htmlString = "";
                if (obj.html_string) {
                  htmlString = obj.html_string;
                } else {
                  if (customString) {
                    htmlString = customString;
                  }
                }

                var reg = "";

                _.each(mentions, function (i) {
                  var chunks = [];

                  // Started on passing options through to merge tags.
                  var updatedHtml = htmlString;
                  htmlString = htmlString.replace(
                    new RegExp(i.objectType, "g"),
                    function (a, index, c) {
                      index = index + i.objectType.length;
                      var chunk = "";
                      var nextStr = "";
                      var mergeTagOptionsStr = "";
                      var mergeTagOptions = [];

                      if (updatedHtml[index] === "(") {
                        chunk = updatedHtml.slice(index + 1);
                        updatedHtml.replace(chunk, "");

                        chunk = chunk.slice(0, chunk.indexOf(")"));
                        chunks.push(chunk);

                        mergeTagOptions = _.compact(chunk.split(","));

                        // Won't need to do this anymore once ui enforces good syntax.
                        _.each(mergeTagOptions, function (t, i) {
                          mergeTagOptions[i] = t.replace("&amp;", "");
                          mergeTagOptions[i] = mergeTagOptions[i].split("<")[0];
                        });
                      }

                      if (!_.isEmpty(chunk) && typeof i.parse === "function") {
                        var parsedHtml = "";
                        try {
                          parsedHtml = i.parse(mergeTagOptions);
                        } catch (err) {
                          console.log(
                            "Error parsing merge tag html: ",
                            err,
                            i,
                            mergeTagOptions
                          );
                        }
                        return parsedHtml;
                      }

                      return i.value;
                    }
                  );

                  _.each(chunks, function (chunk) {
                    if (!_.isEmpty(chunk)) {
                      htmlString = htmlString.replace("(" + chunk + ")", "");
                    }
                  });
                });

                callback(htmlString);
              });

              break;

            default:
              sb.data.db.obj.update(
                "contracts",
                { merge_type: "space", id: obj.id },
                function (contract) {
                  createMergedHTML(contract, callback);
                },
                2
              );

              break;
          }
        });
      }
    );
  }

  function createMergedInvoiceHTML(setup, callback, options, onCompleteFullItems) {
    var logoString;

    if (setup.logo) {
      if (_.isObject(setup.logo)) {
        logoString = sb.data.files.getURL(setup.logo.company_logo);
      } else if (_.isArray(setup.logo)) {
        logoString = sb.data.files.getURL(setup.logo[0].company_logo);
      }
    }

    function generateInvoiceString(setup) {
      var billedtoCompany = "<i>No client selected</i>";
      var billedtoContact = "<i>No Contact selected</i>";
      var billingAddress = "<i>No billing address</i>";
      var total = 0;
      var balance = 0;
      var totalPaid = 0;
      var totalFees = 0;
      var payments = [];
      var htmlString = "";

      var invoices = setup.invoices;
      var pricedMenu = setup.pricedMenu;
      var projectObj = setup.projectObj;
      var billingAddressObj = setup.billingAddress;
      var payNow = setup.payNow;
      var hq = setup.hq;
      var instanceEmail = "";

      if (appConfig.instance === "infinity") {
        instanceEmail = "<EMAIL>";
      }

      if (appConfig.instance === "dreamcatering") {
        instanceEmail = "<EMAIL>";
      }

      if (
        projectObj.main_contact != null &&
        projectObj.main_contact !== false
      ) {
        billedtoContact = `${projectObj.main_contact.fname} ${projectObj.main_contact.lname}`;

        if (
          projectObj.main_contact.company != null &&
          projectObj.main_contact.company !== false
        ) {
          billedtoCompany = projectObj.main_contact.company.name;
        }
      }

      billingAddress = hq.name;

      if (billingAddressObj) {
        billingAddress =
          billingAddress +
          "<br/>" +
          billingAddressObj.street +
          "<br />" +
          billingAddressObj.city +
          ", " +
          billingAddressObj.state +
          " " +
          billingAddressObj.zip +
          "<br/>" +
          instanceEmail;
      }

      htmlString += '<div id="invoice-container">';

      htmlString += '<div style="text-align: center;"><h2>Invoice</h2></div>';

      htmlString += '<table width="100%">';

      htmlString += "<tr>";

      htmlString += '<td width="50%">' + billingAddress + "</td>";
      if (!_.isEmpty(logoString)) {
        htmlString +=
          '<td width="50%" style="text-align:right;"><img style="max-width: 200px;" src="' +
          logoString +
          '"></td>';
      }

      htmlString += "</tr>";

      htmlString += "</table>";

      htmlString += "<br/>";

      htmlString += '<div style="width: 50%;">';

      htmlString += '<table style="border: 1px solid lightgrey;">';

      htmlString += "<thead>";

      htmlString += "<tr>";

      htmlString +=
        '<th style="padding:5px; background-color: #F2F3F4; font-weight: bold;">BILL TO</th>';

      htmlString += "</tr>";

      htmlString += "</thead>";

      htmlString += "<tbody>";

      htmlString += "<tr>";

      if (billedtoCompany) {
        htmlString +=
          '<td style="text-align: center;">' + billedtoCompany + "</td>";
      }

      htmlString += "</tr>";

      htmlString += "</tbody>";

      htmlString += "</table>";

      htmlString += "</div>";

      htmlString += "<br/><br/>";

      htmlString += "<div>";

      htmlString += '<table style="border: 1px solid lightgrey;">';

      htmlString += "<thead>";

      htmlString += "<tr>";

      htmlString +=
        '<th style="padding:5px; background-color: #F2F3F4; font-weight: bold;">INVOICE#</th>';
      htmlString +=
        '<th style="padding:5px; background-color: #F2F3F4; font-weight: bold;">DATE</th>';
      htmlString +=
        '<th style="padding:5px; background-color: #F2F3F4; font-weight: bold;">TOTAL DUE</th>';
      htmlString +=
        '<th style="padding:5px; background-color: #F2F3F4; font-weight: bold;">DUE DATE</th>';
      htmlString +=
        '<th style="padding:5px; background-color: #F2F3F4; font-weight: bold;">TERMS</th>';

      htmlString += "</tr>";

      htmlString += "</thead>";

      htmlString += "<tbody>";

      _.each(_.sortBy(invoices, "due_date"), function (inv) {
        total += inv.amount;
        balance += inv.balance;

        // Only include non-returned payments in the total
        if (inv.payments && inv.payments.length > 0) {
          var validPayments = _.filter(inv.payments, function(payment) {
            return payment.status !== 'Returned' && payment.invoice !== 0;
          });

          totalPaid += _.reduce(validPayments, function(sum, payment) {
            return sum + (payment.amount || 0);
          }, 0);

          payments = payments.concat(validPayments);
        }

        htmlString += "<tr>";

        htmlString +=
          '<td style="text-align: center;">#' +
          inv.related_object.id +
          "-" +
          inv.id +
          "</td>";
        htmlString +=
          '<td style="text-align: center;">' +
          moment(inv.date_created).format("M/D/YYYY") +
          "</td>";
        htmlString +=
          '<td style="text-align: center;">$' +
          (inv.balance / 100).formatMoney() +
          "</td>";
        htmlString +=
          '<td style="text-align: center;">' +
          moment(inv.due_date).format("M/D/YYYY") +
          "</td>";
        htmlString += '<td style="text-align: center;">Due on receipt</td>';

        htmlString += "</tr>";
      });

      htmlString += "</tbody>";

      htmlString += "</table>";

      htmlString += "</div>";

      htmlString += "<br/><br/>";

      htmlString += "<div>";

      htmlString += '<table style="border: 1px solid lightgrey;">';

      htmlString += "<thead>";

      htmlString += "<tr>";

      htmlString +=
        '<th style="padding:5px; background-color: #F2F3F4; font-weight: bold; text-align: left;">DESCRIPTION</th>';
      htmlString +=
        '<th style="padding:5px; background-color: #F2F3F4; font-weight: bold; text-align: right;">AMOUNT</th>';

      htmlString += "</tr>";

      htmlString += "</thead>";

      htmlString += "<tbody>";

      _.each(_.sortBy(invoices, "due_date"), function (inv) {
        htmlString += "<tr>";

        if (inv.name && inv.related_object.main_object.name) {
          htmlString +=
            "<td>" +
            inv.name +
            " - " +
            inv.related_object.main_object.name +
            "</td>";
        }

        if (inv.amount) {
          htmlString +=
            '<td style="text-align: right;">$' +
            (inv.amount / 100).formatMoney() +
            "</td>";
        }

        htmlString += "</tr>";
      });

      htmlString += "</tbody>";

      htmlString += "</table>";

      htmlString += "</div>";

      htmlString += "<br/><br/>";

      totalDue = total - totalPaid;

      htmlString += '<table width="100%">';

      htmlString += "<tr>";

      htmlString +=
        '<td width="75%" style="text-align: right;">PAYMENT (USD)</td>';
      htmlString +=
        '<td width="25%" style="text-align: right;">$' +
        (totalPaid / 100).formatMoney(2) +
        "</td>";

      htmlString += "</tr>";

      htmlString += "<tr>";

      htmlString +=
        '<td width="75%" style="text-align: right;">BALANCE DUE</td>';
      htmlString +=
        '<td width="25%" style="text-align: right;">$' +
        (totalDue / 100).formatMoney(2) +
        "</td>";

      htmlString += "</tr>";

      htmlString += "</table>";

      htmlString += "</div>";

      return htmlString;
    }

    function generateString(invoices, pricedMenu, projectObj, payNow, recipes) {
      if (projectObj === undefined) {
        return;
      }

      var htmlString = "";
      var paymentString = "";

      var showPayments = true;

      ///options from nlp merge tag
      if (options && options.hasOwnProperty("showPayments"))
        showPayments = options.showPayments;

      var getHTMLSetup = { includeDiscounts: true, recipes: recipes };

      if (options && _.isObject(options))
        getHTMLSetup = Object.assign({}, getHTMLSetup, options);

        htmlString += sectionTypes.menu.getHTML(pricedMenu, {}, {}, getHTMLSetup);

      if (showPayments) {
        var total = 0;
        var balance = 0;
        var totalPaid = 0;
        var totalFees = 0;
        var paymentLink;
        var payments = [];

        htmlString +=
          '<br /><div style="page-break-inside: avoid; border: 1px solid lightgray; padding: 15px;" >';

        htmlString +=
          '<h3 style="text-align:left; font-weight:bold; padding-bottom: 15px;">Payment Schedule</h3>';

        htmlString +=
          '<table class="medium-editor-table" style="width:100%; border-collapse: collapse;">';
        htmlString +=
          '<tr style="font-size:12px;font-weight:bold; background-color: #F2F3F4; color:#B9BEC4;"> <td style="color:#B9BEC4;padding:5px;"></td> <td style="padding:5px;color:#B9BEC4; text-align:right;">Due Date</td> <td style="padding:5px;color:#B9BEC4; text-align:right;">Total</td> <td style="padding:5px;color:#B9BEC4; text-align:right;">Paid</td> <td style="padding:5px;color:#B9BEC4; text-align:right;">Balance</td> </tr>';

        _.each(_.sortBy(invoices, "due_date"), function (inv) {
          paymentLink = "";

          if (setup.payNow === true) {
            paymentLink =
              '<div class="ui mini green compact button payButton" data-id="' +
              inv.id +
              '" data-balance="' +
              inv.balance +
              '">Pay Now</div> ';
          }

          if (setup.payNow === "noButton") {
            paymentLink = "";
          }

          total += inv.amount;
          balance += inv.balance;
          totalPaid += inv.paid;

          if (inv.balance <= 0) {
            paymentLink = "";
          }

          payments = payments.concat(inv.payments);

          htmlString +=
            '<tr><td style="padding:5px;">' +
            paymentLink +
            "" +
            inv.name +
            '</td> <td style="padding:5px; text-align:right;">' +
            moment(inv.due_date).format("M/D/YYYY") +
            '</td> <td style="padding:5px; text-align:right;">$' +
            (inv.amount / 100).formatMoney() +
            '</td> <td style="padding:5px; text-align:right;">$' +
            (inv.paid / 100).formatMoney() +
            '</td> <td style="padding:5px; text-align:right;">$' +
            (inv.balance / 100).formatMoney() +
            "</td> </tr>";
        });

        htmlString +=
          '<tr style="font-weight:900; border-top:1px solid lightgray;"> <td style="padding:5px; text-align:right;"></td> <td style="padding:5px; text-align:right;"></td> <td style="padding:5px; text-align:right;">$' +
          (total / 100).formatMoney() +
          '</td> <td style="padding:5px; text-align:right;">$' +
          (totalPaid / 100).formatMoney() +
          '</td> <td style="padding:5px; text-align:right;">$' +
          (balance / 100).formatMoney() +
          "</td> </tr>";

        htmlString += "</table>";

        htmlString += "</div><br />";

        // PAYMENT HISTORY

        paymentString +=
          '<div style="page-break-inside: avoid; border: 1px solid lightgray; padding: 15px;" >';

        paymentString +=
          '<h3 style="text-align:left; font-weight:bold; padding-bottom: 15px;">Payment History</h3>';

        paymentString +=
          '<table class="medium-editor-table" style="width:100%; border-collapse: collapse;">';
        paymentString +=
          '<tr style="font-size:12px;font-weight:bold; background-color: #F2F3F4; color:#B9BEC4;"> <td style="padding:5px; text-align:right;color:#B9BEC4;">Transaction ID</td> <td style="padding:5px; text-align:right;color:#B9BEC4;">Payment Date</td> <td style="padding:5px; text-align:right;color:#B9BEC4;">Paid on Invoice</td> <td style="padding:5px; text-align:right;color:#B9BEC4;">Fees</td> <td style="padding:5px; text-align:right;color:#B9BEC4;">Total Paid</td> </tr>';

        totalPaid = 0;

        payments = _.uniq(payments, false, function (payment) {
          return payment.id;
        });

        _.each(_.sortBy(payments, "date_created"), function (inv) {
          var transxId = inv.id;
          if (inv.details.id) {
            transxId = inv.details.id;
          }

          var dateString = moment(inv.date_created).local().format("M/D/YYYY");
          if (inv.details && inv.details.payment_date) {
            dateString = moment(inv.details.payment_date)
              .local()
              .format("M/D/YYYY");
          }

          if (!inv.fee) {
            inv.fee = 0;
          }

          totalPaid += inv.amount;
          totalFees += inv.fee;

          if (setup.payNow === true) {
            paymentLink =
              '<div class="ui mini green button payButton" data-id="' +
              inv.id +
              '" data-balance="' +
              inv.balance +
              '">Pay Now</div> ';
          }

          paymentString +=
            '<tr style="text-align:right;"> <td style="padding:5px;">' +
            projectObj.id +
            "-" +
            transxId +
            '</td> <td style="padding:5px; text-align:right;">' +
            dateString +
            '</td> <td style="padding:5px; text-align:right;">$' +
            (inv.amount / 100).formatMoney() +
            '</td> <td style="padding:5px; text-align:right;">$' +
            (inv.fee / 100).formatMoney() +
            '</td> <td style="padding:5px; text-align:right;">$' +
            ((inv.amount + inv.fee) / 100).formatMoney() +
            "</td> </tr>";
        });

        paymentString +=
          '<tr style="font-weight:900; border-top:1px solid lightgray;"> <td style="padding:5px; text-align:right;"></td> <td style="padding:5px; text-align:right;"></td> <td style="padding:5px; text-align:right;">$' +
          (totalPaid / 100).formatMoney() +
          '</td> <td style="padding:5px; text-align:right;">$' +
          (totalFees / 100).formatMoney() +
          '</td> <td style="padding:5px; text-align:right;">$' +
          ((totalPaid + totalFees) / 100).formatMoney() +
          "</td> </tr>";

        paymentString += "</table></div>";

        if (totalPaid > 0) {
          htmlString += paymentString;
        }

        return htmlString;
      } else {
        return htmlString;
      }
    }

    if (callback) {
      sb.data.db.controller(
        "getMergeTagData",
        {
          objId: setup.id,
        },
        function (data) {
          var menus = data.inventory_menu;
          var invoices = data.invoices;

          sb.notify({
            type: "get-menu-line-item-pricing",
            data: {
              menu: menus[0],
              obj: setup.project,
              workorder: setup.project,
              callback: function (pricedMenu) {
                htmlString = generateString(
                  invoices,
                  pricedMenu,
                  setup.project,
                  setup.payNow,
                  setup.recipes
                );

                callback(htmlString);
              },
            },
          });
        }
      );
    } else if (setup.invoice === true) {
      return generateInvoiceString(setup);
    } else {
      return generateString(
        setup.invoices,
        setup.pricedMenu,
        setup.projectObj,
        setup.payNow,
        setup.recipes
      );
    }
  }

  function createMergedInvoiceHeaderHTML(setup) {
    // Looks like this function will error out in the documents tool on HQ
    // The setup obj being passed looks like a user obj which does not contain the
    // required vars.
    // Putting a stop here if a user obj is detected.

    if (setup.hasOwnProperty("id")) {
      return;
    }

    var billedtoCompany = "<i>No client selected</i>";
    var billedtoContact = "<i>No Contact selected</i>";
    var billingAddress = "<i>No billing address</i>";
    var headCount = "";

    var lineItems = "",
      subtotal = 0,
      taxRate = 0,
      tax = 0,
      total = 0,
      amountPaid = 0,
      totalDue = 0;

    var billedtoCompany = "<i>No client selected</i>",
      billedtoContact = "<i>No Contact selected</i>";

    if (setup) {
      if (setup.projectObj) {
        if (setup.projectObj.head_count) {
          headCount = "Head Count: " + setup.projectObj.head_count;
        }

        if (
          setup.projectObj.main_contact != null &&
          setup.projectObj.main_contact !== false
        ) {
          billedtoContact = `${setup.projectObj.main_contact.fname} ${setup.projectObj.main_contact.lname}`;

          if (
            setup.projectObj.main_contact.company != null &&
            setup.projectObj.main_contact.company !== false
          ) {
            billedtoCompany = setup.projectObj.main_contact.company.name;
          }
        }
      }
    }

    _.each(setup.invoices, function (item, k) {
      _.each(item.payments, function (p) {
        amountPaid += p.amount;
      });

      subtotal += item.amount;
    });

    total = subtotal;

    total = Math.round(total);

    totalDue = total - amountPaid;

    if (setup.contactObj != null && setup.contactObj !== false) {
      billedtoContact = `${setup.contactObj.fname} ${setup.contactObj.lname}`;

      if (
        setup.contactObj.company != null &&
        setup.contactObj.company !== false
      ) {
        billedtoCompany = setup.contactObj.company.name;
      }
    }

    if (setup.invoiceSystem) {
      if (setup.invoiceSystem.billing_address) {
        billingAddress =
          setup.invoiceSystem.billing_address.street +
          "<br />" +
          setup.invoiceSystem.billing_address.city +
          ", " +
          setup.invoiceSystem.billing_address.state +
          " " +
          setup.invoiceSystem.billing_address.zip;
      }
    }

    var logoHtml = "";
    if (setup && setup.logo && setup.logo.company_logo) {
      logoHtml = sb.data.files.getURL(setup.logo.company_logo);
    }

    return (
      "" +
      '   <div id="invoice-container" style="width: 100%;">  ' +
      '		<table width="100%">' +
      "			<tr>" +
      '				<td width="50%">' +
      "   				<p>" +
      appConfig.systemName +
      "<br />" +
      billingAddress +
      "</p>  " +
      "				</td>" +
      '				<td width="50%" style="text-align:right;">' +
      ' 				  	<h2 id="" style="text-align:right;"><img style="max-width:100px;" src="' +
      logoHtml +
      '"></h2>  ' +
      "				</td>" +
      "			</tr>" +
      "		</table>" +
      "   	  " +
      "   	<br /><hr>" +
      "<h1>" +
      setup.projectObj.name +
      '<br /><span style="font-weight:normal;"><small><small>' +
      headCount +
      "</small></small></span></h1>" +
      "   	  " +
      "   	<br />" +
      '   	<table width="100%">  ' +
      "   	  " +
      "   		<tr>  " +
      "   			  " +
      '   			<td width="50%">' +
      "   				" +
      '   				<table width="100%">  ' +
      "   					  " +
      "   					<tr>  " +
      "   						  " +
      '   						<td style="font-weight: bold;">Billed to:</td>  ' +
      "   						  " +
      "   					</tr>  " +
      "     " +
      "   					<tr>  " +
      "   						  " +
      '   						<td style="font-size: 16px;">' +
      billedtoCompany +
      "</td>  " +
      "   						  " +
      "   					</tr>  " +
      "     " +
      "   					<tr>  " +
      "   						  " +
      "   						<td>" +
      billedtoContact +
      "</td>  " +
      "   						  " +
      "   					</tr>  " +
      "   					  " +
      "   				</table>  " +
      "	                    " +
      "   			</td>  " +
      "   			  " +
      '   			<td width="50%">  ' +
      "   				  " +
      '   				<table width="100%">  ' +
      "   					  " +
      "   					<tr>  " +
      "   						  " +
      '   						<td width="50%" style="padding: 10px 8px;">Invoice #</td>  ' +
      '   						<td style="text-align: right; padding: 10px 8px;">' +
      setup.projectObj.object_uid +
      "</td>  " +
      "   						  " +
      "   					</tr>  " +
      "     " +
      "     " +
      '   					<tr class="bold-table-row">  ' +
      "   						  " +
      '   						<td width="50%" style="padding: 10px 8px;">Balance Due (USD)</td>  ' +
      '   						<td style="text-align: right; padding: 10px 8px;" class="totalDue">$' +
      (totalDue / 100).formatMoney(2) +
      "</td>  " +
      "   						  " +
      "   					</tr>  " +
      "   					  " +
      "   				</table>  " +
      "   				  " +
      "   			</td>  " +
      "   			  " +
      "   		</tr>  " +
      "   		  " +
      "   	</table>  "
    );
  }

  function createMergedClientStatementPortal(setup) {
    var billedtoCompany = "<i>No client selected</i>";
    var billedtoContact = "<i>No Contact selected</i>";
    var billingAddress = "<i>No billing address</i>";
    var logoString = "";
    var headCount = "";
    var paymentLink = "";
    var balance = 0;
    var totalPaid = 0;
    var payments = [];
    var htmlString = "";

    var lineItems = "",
      subtotal = 0,
      taxRate = 0,
      tax = 0,
      total = 0,
      amountPaid = 0,
      totalDue = 0;

    var billedtoCompany = "<i>No client selected</i>",
      billedtoContact = "";

    if (setup) {
      if (setup.projectObj) {
        if (setup.projectObj.head_count) {
          headCount = "Head Count: " + setup.projectObj.head_count;
        }

        if (
          setup.projectObj.main_contact != null &&
          setup.projectObj.main_contact !== false
        ) {
          billedtoContact = `${setup.projectObj.main_contact.fname} ${setup.projectObj.main_contact.lname}`;

          if (
            setup.projectObj.main_contact.company != null &&
            setup.projectObj.main_contact.company !== false
          ) {
            billedtoCompany = setup.projectObj.main_contact.company.name;
          }
        }
      }
    }


    var isDaily = false;
    var projectType = setup?.project?.type?.id || setup?.project?.type || null;

    ///check to see if project type is "Daily Dish Event Management"
    /// {id: 12033227} production, {id:12261255} dev
    if ( projectType == 12033227 ) {
        isDaily = true;
    }
    if ( appConfig.instance == 'dreamcatering' && isDaily) {
        appConfig.systemName = 'The Market Place';
    }

    if (setup.logo && !isDaily) {
      logoString = sb.data.files.getURL(setup.logo.company_logo);
    }

    _.each(setup.invoices, function (item, k) {
      _.each(item.payments, function (p) {
        amountPaid += p.amount;
      });

      subtotal += item.amount;
    });

    // 		total = subtotal;
    //
    // 		total = Math.round(total);

    //totalDue = total - amountPaid;

    if (setup.client != null && setup.client !== false) {
      billedtoCompany = setup.client.name;

      if (setup.client.contact_info.length > 0) {
        _.each(setup.client.contact_info, function (info) {
          if (info.type.data_type == "address") {
            billedtoCompany +=
              "<br />" +
              info.street +
              "<br />" +
              info.city +
              ", " +
              info.state +
              " " +
              info.zip;
          }
        });
      }
    }

    if (setup.invoiceSystem) {
      if (setup.invoiceSystem.billing_address) {
        billingAddress =
          setup.invoiceSystem.billing_address.street +
          "<br />" +
          setup.invoiceSystem.billing_address.city +
          ", " +
          setup.invoiceSystem.billing_address.state +
          " " +
          setup.invoiceSystem.billing_address.zip;
      }
    }

    htmlString +=
      '<div id="bentoDocumentEditorPreviewer" style="min-height:700px; max-width:800px; display:block; margin:0 auto;">';

    htmlString +=
      "<style>" +
      "</style>" +
      "</head>" +
      '   <div id="invoice-container" class="ui basic segment" style="width: 100%;">  ' +
      '		<table width="100%">' +
      "			<tr>" +
      '				<td width="50%">' +
      "   				<p><b>" +
      appConfig.systemName +
      "</b><br />" +
      billingAddress +
      "</p>  " +
      "				</td>" +
      '				<td width="50%" style="text-align:right;">' +
      ' 				  	<h2 id="" style="text-align:right;"><img style="max-width:100px;" src="' +
      logoString +
      '"></h2>  ' +
      "				</td>" +
      "			</tr>" +
      "		</table>" +
      "   	  " +
      "   	<br />" +
      '   	<table width="100%">  ' +
      "   	  " +
      "   		<tr>  " +
      "   			  " +
      '   			<td width="50%">' +
      "   				" +
      '   				<table width="100%">  ' +
      "   					  " +
      "   					<tr>  " +
      "   						  " +
      '   						<td style="font-weight: bold;">Billed to:</td>  ' +
      "   						  " +
      "   					</tr>  " +
      "     " +
      "   					<tr>  " +
      "   						  " +
      '   						<td style="font-size: 16px;">' +
      billedtoCompany +
      "</td>  " +
      "   						  " +
      "   					</tr>  " +
      "     " +
      "   					<tr>  " +
      "   						  " +
      "   						<td>" +
      billedtoContact +
      "</td>  " +
      "   						  " +
      "   					</tr>  " +
      "   					  " +
      "   				</table>  " +
      "	                    " +
      "   			</td>  " +
      "   			  " +
      "   		</tr>  " +
      "   		  " +
      "   	</table>  ";

    htmlString += "<h2>Invoices</h2>";

    htmlString += '<table class="ui table" width="100%">';

    htmlString +=
      '<thead><tr style="font-weight:bold;">' +
      '<th style="padding:5px;"></tH>' +
      '<th style="padding:5px; text-align:right;">DUE DATE</th>' +
      '<th style="padding:5px; text-align:right;">TOTAL</th>' +
      '<th style="padding:5px; text-align:right;">TOTAL PAID</th>' +
      '<th style="padding:5px; text-align:right;">BALANCE</th>' +
      "</tr></thead>";

    htmlString += "<tbody>";

    _.each(_.sortBy(setup.invoices, "due_date"), function (inv) {
      paymentLink = "";
      if (setup.payNow === true) {
        paymentLink =
          '<div id="' +
          inv.id +
          '" class="ui mini green compact button payButton" data-id="' +
          inv.id +
          '" data-balance="' +
          inv.balance +
          '">Pay Now</div> ';
      }

      if (setup.payNow === "noButton") {
        paymentLink = "";
      }

      total += inv.amount;
      balance += inv.balance;
      totalPaid += inv.paid;

      if (inv.balance <= 0) {
        paymentLink = "";
      }

      payments = payments.concat(inv.payments);

      htmlString +=
        '<tr style="">' +
        '<td style="padding:5px;">' +
        inv.name +
        " " +
        paymentLink +
        "</td>" +
        '<td style="padding:5px; text-align:right;">' +
        moment(inv.due_date).local().format("M/D/YYYY") +
        "</td>" +
        '<td style="padding:5px; text-align:right;">$' +
        (inv.amount / 100).formatMoney() +
        "</td>" +
        '<td style="padding:5px; text-align:right;">$' +
        (inv.paid / 100).formatMoney() +
        "</td>" +
        '<td style="padding:5px; text-align:right;">$' +
        (inv.balance / 100).formatMoney() +
        "</td>" +
        "</tr>";
    });

    htmlString += "</tbody>";

    htmlString +=
      '<tfoot><tr style="font-weight:bold;">' +
      "<td></td>" +
      "<td></td>" +
      "<td></td>" +
      '<td class="" style="text-align:right;">Total Paid Due (USD)</td>  ' +
      '<td class="" style="text-align: right; padding: 10px 5px;" class="totalDue">$' +
      (totalPaid / 100).formatMoney(2) +
      "</td>  " +
      "</tr>";

    totalDue = total - totalPaid;

    htmlString +=
      '<tr style="font-weight:bold;">' +
      "<td></td>" +
      "<td></td>" +
      "<td></td>" +
      '<td class="" style="text-align:right;">Total Balance Due (USD)</td>  ' +
      '<td class="" style="text-align: right; padding: 10px 5px;" class="totalDue">$' +
      (totalDue / 100).formatMoney(2) +
      "</td>  " +
      "</tr></tfoot>";

    htmlString += "</table>";

    htmlString += "</div>";

    return htmlString;
  }

  function createTemplateTableSetup() {
    return {
      objectType: "contracts",
      childObjs: 3,
      tableTitle: '<i class="fa fa-file-text-o"></i> Contract Templates',
      searchObjects: false,
      navigation: navigation,
      filters: function (callback, bp) {
        callback({
          contract_type: {
            name: "Contract Types",
            type: "checkbox",
            field: "contract_types",
            options: _.map(
              bp.contract_types.options,
              function (v, k) {
                return {
                  name: "contract_types",
                  label: v,
                  value: k,
                };
              },
              []
            ),
          },
        });
      },
      download: false,
      headerButtons: {
        reload: {
          name: "Reload",
          css: "pda-btn-blue",
          action: function () {},
        },
        newTemplate: {
          name: '<i class="fa fa-plus"></i> New Template',
          css: "pda-btn-green",
          domType: "none",
          action: function () {
            sb.notify({
              type: "app-navigate-to",
              data: {
                itemId: "self",
                viewId: {
                  id: "newTemplate",
                  type: "custom",
                  title: "New Contract Template",
                  color: "green",
                  icon: '<i class="fa fa-plus"></i>',
                  removable: true,
                  dom: createContractView,
                },
              },
            });
          },
        },
      },
      rowSelection: true,
      multiSelectButtons: {
        erase: {
          name: '<i class="fa fa-times"></i> Delete',
          css: "pda-btn-red",
          domType: "erase",
          action: "erase",
        },
      },
      rowLink: {
        type: "view_object",
        header: function (obj) {
          return obj.name;
        },
        action: singleView,
        viewObj: function (obj) {
          return {
            id: "singleTemplate",
            type: "custom",
            title: obj.name,
            color: "orange",
            icon: '<i class="fa fa-pencil"></i>',
            removable: true,
            dom: singleView.bind({}, obj),
          };
        },
      },
      visibleCols: {
        name: "Name",
        type: "Type",
        date_created: "Date Created",
      },
      cells: {
        name: function (obj) {
          return obj.name;
        },
        type: function (obj) {
          var type;

          if (obj.contract_types != null) {
            type = obj.contract_types.name;
          } else {
            type = "No Type Selected";
          }

          return type;
        },
        date_created: function (obj) {
          return moment(obj.date_created).format("M/D/YYYY h:mm a");
        },
      },
      data: function (paged, callback) {
        sb.data.db.obj.getWhere(
          "contracts",
          {
            related_object: 0,
            childObjs: {
              name: true,
              contract_types: {
                name: true,
              },
              last_updated_by: {
                fname: true,
                lname: true,
              },
              last_updated: true,
              date_created: true,
            },
            paged: paged,
          },
          function (ret) {
            callback(ret);
          }
        );
      },
    };
  }

  function editSystemSettings(dom) {
    checkSettingsObject(function (settingsObj) {
      dom.empty();

      dom.makeNode("title", "div", {
        text: "3. Electronic Signature Disclaimer",
        css: "ui huge header",
      });

      //dom.makeNode('break', 'div', {text:'<br />'});

      dom.makeNode("col", "column", { w: 16 });

      dom.makeNode("break", "div", { text: "<br />" });

      dom.makeNode("btns", "div", { css: "one ui buttons" });

      dom.col.makeNode("form", "form", {
        text: {
          name: "text",
          type: "textbox",
          label: "Disclaimer Text",
          value: settingsObj.signature_disclaimer,
          wysiwyg: {
            height: null,
            minHeight: 300,
            maxHeight: null,
            focus: true,
            toolbar: true,
          },
        },
      });

      dom.btns
        .makeNode("save", "button", {
          text: '<i class="fa fa-check"></i> Save Changes',
          css: "pda-btn-green",
        })
        .notify(
          "click",
          {
            type: "contracts-run",
            data: {
              run: function (settingsObj) {
                var dom = this,
                  formInfo = dom.col.form.process();

                if (formInfo.completed == false) {
                  sb.dom.alerts.alert(
                    "Error",
                    "Disclaimer text is required.",
                    "error"
                  );
                  return;
                }

                dom.btns.save.loading();

                settingsObj.signature_disclaimer = formInfo.fields.text.value;

                sb.data.db.obj.update(
                  "contract_system",
                  settingsObj,
                  function (updated) {
                    setTimeout(function () {
                      dom.btns.save.loading(false);
                      dom.btns.save.text("Saved!");

                      setTimeout(function () {
                        editSystemSettings(dom);
                      }, 1000);
                    }, 700);
                  }
                );
              }.bind(dom, settingsObj),
            },
          },
          sb.moduleId
        );

      dom.patch();
    });
  }

  function emailTemplateSettings(dom) {
    checkSettingsObject(function (settingsObj) {
      dom.empty();

      dom.makeNode("title", "div", {
        text: "2. Signature Request Email Template",
        css: "ui huge header",
      });

      dom.makeNode("form", "form", {
        subject: {
          name: "subject",
          type: "text",
          label: "Subject",
          value: settingsObj.request_email_subject,
        },
        text: {
          name: "text",
          type: "textbox",
          label: "Body",
          value: settingsObj.request_email,
          wysiwyg: {
            height: null,
            minHeight: 300,
            maxHeight: null,
            focus: true,
            toolbar: true,
          },
        },
      });

      dom.makeNode("formBreak", "div", { text: "<br />" });

      dom
        .makeNode("save", "button", {
          text: '<i class="fa fa-check"></i> Save Changes',
          css: "pda-btn-green pda-pull-left",
        })
        .notify(
          "click",
          {
            type: "contracts-run",
            data: {
              run: function (settingsObj) {
                var dom = this,
                  formInfo = dom.form.process();

                if (formInfo.completed == false) {
                  sb.dom.alerts.alert(
                    "Error",
                    "Disclaimer text is required.",
                    "error"
                  );
                  return;
                }

                dom.save.loading();

                settingsObj.request_email = formInfo.fields.text.value;
                settingsObj.request_email_subject =
                  formInfo.fields.subject.value;

                sb.data.db.obj.update(
                  "contract_system",
                  settingsObj,
                  function (updated) {
                    setTimeout(function () {
                      dom.save.loading(false);
                      dom.save.text("Saved!");

                      setTimeout(function () {
                        emailTemplateSettings(dom);
                      }, 1000);
                    }, 700);
                  }
                );
              }.bind(dom, settingsObj),
            },
          },
          sb.moduleId
        );

      dom.patch();
    });
  }

  function getAllBluprints(mainBP, callback) {
    var bpsToGet = _.compact(
      _.map(mainBP, function (o, name) {
        if (o.type == "objectId" || o.type == "objectIds") {
          o.bp_name = name;

          return o;
        }
      })
    );

    function getBlueprints(list, callback, count, ret) {
      if (!count) {
        count = 0;
      }

      if (!ret) {
        ret = [];
      }

      if (list[count]) {
        sb.data.db.obj.getBlueprint(list[count].objectType, function (bp) {
          ret.push({
            type: list[count].objectType,
            blueprint: bp,
            bp_name: list[count].bp_name,
          });

          count++;

          getBlueprints(list, callback, count, ret);
        });
      } else {
        callback(ret);
      }
    }

    getBlueprints(bpsToGet, function (allBlueprints) {
      callback(allBlueprints);
    });
  }

  function processUpdatedPricing(
    dom,
    obj,
    price,
    menuDom,
    priceDom,
    paymentsDom
  ) {
    var total = price._total;

    // 		total = _.chain(price).reduce(function(memo, v, k){ return memo + v; }).value();

    if (!_.isEmpty(priceDom)) {
      priceDom.empty();

      priceDom.makeNode("header", "div", {
        css: "description",
        text: "Total Price",
      });
      priceDom.makeNode("cont", "div", {
        css: "header",
        text: "$" + (total / 100).formatMoney(),
      });
      priceDom.makeNode("meta", "div", {
        css: "meta",
        text: "Estimated Value $" + (obj.potential_value / 100).formatMoney(2),
      });

      priceDom.patch();
    }

    sb.data.db.obj.update(
      "proposals",
      { id: obj.proposal.id, pricing: price, status: "Editing" },
      function (done) {
        //dom.empty();
        /*
			var invSetup = {
					dom: dom,
					state:{
						objectId:obj.proposal.id,
						contactId:obj.main_contact.id,
						clientId:obj.main_contact.company,
						objectType:obj.object_bp_type,
						price:price,
						onUpdate:function(callback, invoices, deleteInvoices){

							processUpdatedPricing(dom, obj, price, menuDom, priceDom, paymentsDom);

							if (callback && typeof callback == 'function') {
								callback(true);
							}

						}
					},
					draw:function(){

					},
					objectId: obj.proposal.id,
					contactId:obj.main_contact.id,
					clientId:obj.main_contact.company,
					objectType:obj.object_bp_type,
					dueDate: obj.proposal.event_start_date,
					price: price,
					balanceDom:priceDom,
					paymentsDom:paymentsDom,
					project: obj,
					onUpdate:function(callback, invoices, deleteInvoices){


					}
			};

			sb.notify({
				type: 'view-all-invoices2',
				data: invSetup
			});
*/
      }
    );
  }

  function savePayment(inv, pay, refreshAction) {
    if (Array.isArray(pay)) {
      _.each(pay, function (updPay) {
        var invoice = _.findWhere(inv, { id: updPay.invoice });
        var updInvoice = {
          payments: [],
          paid: 0,
        };

        if (!invoice.fees) {
          invoice.fees = 0;
        }

        if (invoice.paid) {
          updInvoice.paid = invoice.paid;
        }

        if (invoice.payments) updInvoice.payments = invoice.payments;

        updInvoice.paid += +updPay.amount;
        updInvoice.payments.push(updPay);
        updInvoice.balance = invoice.balance - +updPay.amount;
        updInvoice.fees = +updPay.fee + invoice.fees;
        updInvoice.id = invoice.id;

        updPay.main_object = invoice.related_object;
        updPay.main_contact = invoice.main_contact;
        updPay.main_client = invoice.main_client;

        sb.data.db.obj.update(
          "invoices",
          updInvoice,
          function (updatedInv) {
            var noteObj = {
              type_id: updatedInv.id,
              type: "invoices",
              note:
                "Payment for $" +
                (updPay.amount / 100).formatMoney(2) +
                " was made.",
              record_type: "log",
              author: sb.data.cookie.get("uid"),
              notifyUsers: [],
            };

            sb.data.db.obj.create("notes", noteObj, function (newNote) {});
          },
          4
        );
      });

      sb.data.db.obj.update("payments", pay, function (updatedPayments) {
        sb.dom.alerts.alert("Success!", "", "success");

        if (refreshAction) {
          refreshAction();
        }
      });
    } else {
      //var updInvoice = inv;
      var updInvoice = {
        payments: [],
        paid: 0,
      };

      if (!inv.fees) {
        inv.fees = 0;
      }

      if (inv.paid) {
        updInvoice.paid = inv.paid;
      }

      if (inv.payments) updInvoice.payments = inv.payments;

      updInvoice.paid += +pay.amount;
      updInvoice.payments.push(pay);
      updInvoice.balance = inv.balance - pay.amount;
      updInvoice.fees = +pay.fee + +inv.fees;
      updInvoice.id = inv.id;

      sb.data.db.obj.update(
        "invoices",
        updInvoice,
        function (updatedInv) {
          var noteObj = {
            type_id: updatedInv.id,
            type: "invoices",
            note:
              "Payment for $" +
              (pay.amount / 100).formatMoney(2) +
              " was made.",
            record_type: "log",
            author: sb.data.cookie.get("uid"),
            notifyUsers: [],
          };

          sb.data.db.obj.create("notes", noteObj, function (newNote) {
            sb.dom.alerts.alert("Success!", "", "success");

            if (refreshAction) {
              refreshAction();
            }
          });
        },
        4
      );
    }
  }

  // creates the table ui object and is responsible for printing and updating the table on the dom
  function tableUIState(contractType, objectId, contactId, setup) {
    var modifiedSetup = {};

    // builds the curdTable instantiation object

    function buildTableSetup(
      dom,
      contractType,
      objectId,
      contactId,
      drawComplete
    ) {
      var crudTableSetup = {
        domObj: dom,
        objectType: "contracts",
        drawComplete: drawComplete,
        childObjs: 4,
        tableTitle: false,
        searchObjects: false,
        navigation: false,
        download: false,
        buttonState: {
          contactType: contractType,
          objectId: objectId,
          contactId: contactId,
          afterCreate: setup.afterCreate,
          onUpdate: setup.onUpdate,
          objectView: setup.objectView,
        },
        noObjects: {
          action: function (dom, objectType, blueprint, setup) {
            dom.empty();

            dom.patch();

            addContractView(blueprint, dom, setup);

            //drawComplete(true);
          },
        },
        headerButtons: {
          reload: {
            name: "Reload",
            css: "pda-btn-blue",
            action: function () {},
          },
          create: {
            name: '<i class="fa fa-plus"></i> Add A Contract',
            css: "pda-btn-green",
            domType: "full",
            action: addContractView,
            contactType: contractType,
            objectId: objectId,
            contactId: contactId,
          },
        },
        rowSelection: true,
        multiSelectButtons: {
          erase: {
            name: '<i class="fa fa-times"></i> Delete',
            css: "pda-btn-red",
            domType: "erase",
            action: "erase",
          },
        },
        settings: false,
        home: false,
        rowLink: {
          type: "app_component",
          header: function (obj) {
            return obj.name;
          },
          action: singleView,
        },
        visibleCols: {
          name: "Name",
          type: "Type",
          date_created: "Date Created",
          last_updated: "Last Updated",
        },
        cells: {
          name: function (obj) {
            return obj.name;
          },
          type: function (obj) {
            var type;

            if (obj.contract_types != null) {
              type = obj.contract_types.name;
            } else {
              type = "No Type Selected";
            }

            return type;
          },
          date_created: function (obj) {
            return moment(obj.date_created).format("M/D/YYYY h:mm a");
          },
          last_updated: function (obj) {
            return (
              moment(obj.last_updated).format("M/D/YYYY h:mm a") +
              " by " +
              obj.last_updated_by.fname +
              " " +
              obj.last_updated_by.lname
            );
          },
        },
      };

      if (objectId > 0 && objectId != contactId) {
        crudTableSetup.data = function (paged, callback) {
          sb.data.db.obj.getWhere(
            "contracts",
            { related_object: objectId, childObjs: 4, paged: paged },
            function (ret) {
              delete dom.loading;

              if (_.isArray(ret)) {
                callback(ret);
              } else {
                if (ret.data.length == 1 && setup.onlyOne) {
                  singleView(ret.data[0], dom, setup, setup.draw, false);
                  drawComplete(true);
                } else {
                  callback(ret);
                }
              }
            }
          );
        };
      }

      if (objectId == 0 && contactId == 0) {
        delete crudTableSetup.noObjects;

        crudTableSetup.data = function (paged, callback) {
          sb.data.db.obj.getWhere(
            "contracts",
            { active: "Yes", childObjs: 4, paged: paged },
            function (ret) {
              callback(ret);
            }
          );
        };
      }

      if (contactId > 0 && contractType == "contacts") {
        crudTableSetup.data = function (paged, callback) {
          sb.data.db.obj.getWhere(
            "contracts",
            { main_contact: contactId, childObjs: 4, paged: paged },
            function (ret) {
              callback(ret);
            }
          );
        };
      }

      _.each(modifiedSetup, function (o, k) {
        crudTableSetup[k] = o;
      });

      return crudTableSetup;
    }

    function show(contractType, objectId, contactId) {
      this.makeNode("tableCont", "div", {});

      this.patch();

      var dom = this;

      var tableSetup = buildTableSetup(
        dom.tableCont,
        contractType,
        objectId,
        contactId,
        function () {
          delete dom.loading.loader;
          dom.loading.patch();
          dom.loading.css("");
        }
      );

      components.table.notify({
        type: "show-table",
        data: tableSetup,
      });
    }

    function change(newSetup) {
      modifiedSetup = newSetup;
    }

    this.state.changeSetup = change;
    this.state.show = show.bind(this, contractType, objectId, contactId);
  }

  // SYSTEM VIEWS

  // menu and invoices view
  function invoicesView(dom, state, draw, callback) {
    var object = state.project.proposal;
    var objectId = state.project.proposal.id;
    var mainContactId = 0;
    var objectType = "projects";

    function checkBalance(dom) {
      var color = "";

      if (invData.balanced == false) {
        color = "yellow";
        dom.makeNode("balance", "div", {
          css: "ui top attached negative icon message",
        });

        dom.balance.makeNode("icon", "div", {
          tag: "i",
          css: "exclamation icon",
        });

        dom.balance.makeNode("content", "div", { css: "content" });

        dom.balance.content.makeNode("header", "div", {
          css: "header",
          text: "Out of balance by $" + (invData.balance / 100).formatMoney(2),
        });
        dom.balance.content.makeNode("p", "div", {
          tag: "p",
          text: "You can add this amount to an existing invoice or create a new invoice.",
        });

        /*
				dom.makeNode('balance', 'div', {css:'ui big negative message'})
					.makeNode('header', 'div', {css:'header', text:'Please adjust the payment schedule.'});
*/
        dom.makeNode("balanceCont", "div", {});
      }
    }

    function drawView(setup) {
      setup.completeCallback = callback;

      draw({
        dom: dom,
        after: function (dom) {
          sb.notify({
            type: "show-menu-builder",
            data: setup,
          });
        },
      });
    }

    if (object.main_contact) {
      mainContactId = object.main_contact.id;
    }

    if (state.project) {
      object.main_object = state.project;
    }

    dom.makeNode("cont", "div", { css: "" });

    draw({
      dom: dom,
      after: function (dom) {
        var setup = {
          domObj: dom.cont,
          invoicesDom: true,
          object: state.pageObject.proposal,
          menuId: state.pageObject.proposal.menu,
          isInAccordion: false,
          pricing: function (updatedPrice) {
            price = Object.assign(updatedPrice);
          },
        };

        setup.completeCallback = callback;

        sb.notify({
          type: "show-menu-builder",
          data: setup,
        });
      },
    });
  }

  // single view
  function chooseATemplate(contracts, dom, state, onCreate) {
    dom.makeNode("body", "div", {});

    var modal = dom;

    var headerText = state.is_template
      ? "New Document Template"
      : "New Document";
    modal.body.makeNode("title", "headerText", {
      text: headerText,
      size: "large",
    });

    modal.body.makeNode("newDocDiv", "div", {
      style: "display:flex; justify-content: flex-end;",
    });
    var newDocumentText = state.is_template
      ? 'Create Blank Document Template <i class="fa fa-arrow-right"></i>'
      : 'Create Blank Document <i class="fa fa-arrow-right"></i>';
    modal.body.newDocDiv
      .makeNode("newDocument", "button", {
        text: newDocumentText,
        css: "pda-btn-green clearing",
      })
      .notify(
        "click",
        {
          type: "contracts-run",
          data: {
            run: function () {
              modal.body.newDocDiv.newDocument.loading();

              createNewBlankDocument(state);
            },
          },
        },
        sb.moduleId
      );

    modal.body.makeNode("newDocBreak", "lineBreak", {});

    modal.body.makeNode("searchLabel", "div", {
      tag: "label",
      text: "Search Document Templates",
    });
    modal.body.makeNode("inputContainer", "div", {
      css: "ui huge fluid action input",
    });
    modal.body.inputContainer
      .makeNode("input", "div", {
        tag: "input",
        type: "text",
        placeholder: "Search...",
      })
      .notify(
        "change",
        {
          type: "headquarters-run",
          data: {
            run: function (modal, state) {
              searchDocumentTemplates.call({}, modal, state, onCreate);
            }.bind({}, modal, state),
          },
        },
        sb.moduleId
      );

    modal.body.inputContainer
      .makeNode("btn", "div", {
        text: "Search",
        css: "ui button",
      })
      .notify(
        "click",
        {
          type: "headquarters-run",
          data: {
            run: function (modal, state) {
              searchDocumentTemplates.bind(modal, state, onCreate);
            }.bind({}, modal, state),
          },
        },
        sb.moduleId
      );

    modal.body.makeNode("results", "div", {
      css: "sixteen wide column",
      style: "margin-top:0px !important; padding:0 !important;",
    });
    modal.body.makeNode("templateBreak1", "lineBreak", {});

    modal.body.makeNode("loading", "div", {});
    modal.body.loading.makeNode("text", "text", {
      text: "Loading templates...",
      css: "text-center",
    });
    modal.body.loading.makeNode("loader", "loader", {});

    dom.patch();

    modal.body.makeNode("allTemplateLabel", "div", {
      tag: "label",
      text: "All Available Templates",
    });
    modal.body.makeNode("col", "column", { w: 16 }).makeNode("table", "table", {
      css: "table-hover table-condensed",
      columns: {
        name: "Template Name",
        btns: "",
      },
    });

    _.each(contracts, function (c) {
      modal.body.col.table.makeRow("row-" + c.id, [c.name, ""]);
      modal.body.col.table.body["row-" + c.id].btns
        .makeNode("use", "button", {
          text: '<i class="fa fa-plus"></i> Use This Template',
          css: "pda-btn-green",
          style: "float:right;",
        })
        .notify(
          "click",
          {
            type: "contracts-run",
            data: {
              run: function (contract, obj, data, dom) {
                this.body.col.table.body[
                  "row-" + contract.id
                ].btns.use.loading();

                obj.html_string = contract.html_string;
                obj.name = contract.name + "-COPY";
                obj.active = "Yes";
                obj.merge_type = contract.merge_type;
                obj.requires_approval = contract.requires_approval;
                obj.notify_list = contract.notify_list;
                obj.after_signature = contract.after_signature;
                obj.related_object = state.related_object;
                obj.parent = state.parent;
                obj.contract_types = contract.contract_types;

                sb.data.db.obj.erase("contracts", obj.id, function (updated) {
                  var toCreate = _.clone(obj);
                  delete toCreate.setTemplateProps;
                  delete toCreate.options;

                  sb.data.db.obj.create(
                    "contracts",
                    toCreate,
                    function (created) {
                      onCreate(created);

                      var edit = created.is_template ? true : false;

                      window.location.href = sb.data.url.createPageURL(
                        "object",
                        {
                          edit: edit,
                          type: "contracts",
                          id: created.id,
                          name: created.name,
                        }
                      );
                    },
                    2
                  );
                });
              }.bind(modal, c, state, dom),
            },
          },
          sb.moduleId
        );
    });

    delete modal.body.loading;

    modal.body.patch();

    modal.show();
  }

  function searchDocumentTemplates(modal, state, onCreate) {
    // variables
    var searchString = $(modal.body.inputContainer.input.selector).val();

    var collectionsSetup = {
      actions: {},
      domObj: modal.body.results,
      fields: {
        name: {
          title: "Template Name",
          view: function (ui, template) {
            ui.makeNode("row-" + template.id, "div", {
              style: "font-weight:bold",
              text: template.name,
            });
          },
        },
        select: {
          title: "",
          view: function (ui, template) {
            ui.makeNode("row-" + template.id, "div", {});
            ui["row-" + template.id]
              .makeNode("select", "button", {
                text: '<i class="fa fa-plus"></i> Use This Template',
                css: "pda-btn-green",
                style: "float:right;",
              })
              .notify(
                "click",
                {
                  type: "headquarters-run",
                  data: {
                    run: function () {
                      ui["row-" + template.id].select.loading();

                      var obj = state;
                      obj.html_string = template.html_string;
                      obj.name = template.name + "-COPY";
                      obj.active = "Yes";
                      obj.merge_type = template.merge_type;
                      obj.requires_approval = template.requires_approval;
                      obj.notify_list = template.notify_list;
                      obj.after_signature = template.after_signature;
                      obj.contract_types = template.contract_types;

                      sb.data.db.obj.erase(
                        "contracts",
                        obj.id,
                        function (updated) {
                          var toCreate = _.clone(obj);
                          delete toCreate.setTemplateProps;
                          delete toCreate.options;

                          sb.data.db.obj.create(
                            "contracts",
                            toCreate,
                            function (created) {
                              onCreate(created);

                              var edit = created.is_template ? true : false;

                              window.location.href = sb.data.url.createPageURL(
                                "object",
                                {
                                  edit: edit,
                                  type: "contracts",
                                  id: created.id,
                                  name: created.name,
                                }
                              );
                            },
                            2
                          );
                        }
                      );
                    }.bind(ui, template, state),
                  },
                },
                sb.moduleId
              );
          },
        },
      },
      objectType: "contracts",
      selectedView: "table",
      subviews: {
        table: {
          hideSelectionBoxes: true,
          hideRowActions: true,
        },
      },
      state: state,
      sortCol: "name",
      sortDir: "asc",
      menu: false,
      submenu: false,
      tags: false,
      searchAboveCollection: false,
      where: {
        name: {
          type: "contains",
          value: searchString,
        },
        is_template: 1,
      },
    };

    sb.notify({
      type: "show-collection",
      data: collectionsSetup,
    });

    modal.body.results.patch();
  }

  function createNewBlankDocument(state) {
    sb.data.db.obj.create("contracts", state, function (obj) {
      window.location.href = sb.data.url.createPageURL("object", {
        edit: true,
        type: "contracts",
        id: obj.id,
        name: state.name,
      });
    });
  }

  function projectView(dom, state, draw) {
    sb.notify({
      type: "show-collection",
      data: {
        domObj: dom,
        state: state,
        objectType: "contracts",
        singleView: {
          view: function (ui, obj, draw) {
            // !TODO - Needs to update table if edits occur in single view

            singleView(obj, ui, state, draw, undefined);
          },
        },
        actions: {
          view: true,
          create: function (ui, obj_info, onComplete) {
            createWorkflow(ui, state, obj_info, onComplete);
          },
        },
        fields: {
          name: {
            title: "Name",
          },
          status: {
            title: "Status",
            view: function (dom, obj) {
              var statusString;
              switch (obj.status) {
                case "Approved":
                  statusString =
                    '<div class="ui blue label">' + obj.status + "</div>";

                  break;

                case "Declined":
                  statusString =
                    '<div class="ui red label">' + obj.status + "</div>";

                  break;

                case "Approval Requested":
                  statusString =
                    '<div class="ui orange label">' + obj.status + "</div>";

                  break;

                case "Signed":
                  statusString =
                    '<div class="ui green label">' + obj.status + "</div>";

                  break;

                case "Signing In Progress":
                  statusString =
                    '<div class="ui blue label"><nobr>' +
                    obj.status +
                    "</nobr></div>";

                  break;

                default:
                  statusString = '<div class="ui grey label">Unsigned</div>';
              }

              dom.makeNode("status", "div", { text: statusString });
            },
          },
          after_signature: {
            title: "After Signature",
            view: function (dom, obj) {
              if (obj.after_signature == "invoices") {
                dom.makeNode("last_updated", "div", {
                  text: "Send to invoices",
                });
              } else {
                dom.makeNode("last_updated", "div", { text: "Do Nothing" });
              }
            },
          },
          last_updated: {
            title: "Last Updated",
            view: function (dom, obj) {
              dom.makeNode("last_updated", "div", {
                text:
                  "<nobr>" +
                  moment(obj.last_updated).local().format("M/D/YYYY h:mm a") +
                  (obj.last_updated_by
                    ? " by " +
                      obj.last_updated_by.fname +
                      " " +
                      obj.last_updated_by.lname +
                      "</nobr>"
                    : ""),
              });
            },
          },
        },
        groupings: {
          type: "merge_type",
        },
        where: {
          related_object: state.id,
          childObjs: {
            name: true,
            merge_type: true,
            status: true,
            after_signature: true,
            date_created: true,
            last_updated: true,
            last_updated_by: {
              fname: true,
              lname: true,
            },
          },
        },
      },
    });
  }

  function viewPDF(obj, callback) {
    var options = {};

    if (obj.orientation) {
      options.orientation = obj.orientation;
    }

    sb.data.db.obj.getById("contracts", obj.id, function (contract) {
      if (obj.related_object == 0) {
        sb.data.makePDF(contract.html_string, "D", options);

        callback();
      } else {
        createMergedHTML(obj, function (mergedHTML) {
          sb.data.makePDF(mergedHTML, "D", options);

          callback();
        });
      }
    });
  }

  function notify_Approval_Status_Update(setup, approvalStatus, callback) {
    var doc = setup.doc;
    var docStructure = setup.docHtml;
    var docComments = notify_getCommentsHtml(setup.docComments);
    var emailBatch = [];
    var docUrl = sb.data.url.getObjectPageParams(doc, {});
    var subjectHeadline = setup.doc.name + " has been " + approvalStatus + ".";

    // Notify group managers as well

    var notifyIds = _.pluck(doc.notify_list, "id");

    _.each(doc.related_object.managers, function (managerObj) {
      if (!_.contains(notifyIds, managerObj.id)) {
        doc.notify_list.push(managerObj);
      }
    });

    _.each(doc.notify_list, function (user) {
      emailBatch.push({
        to: user.email,
        from: "<EMAIL>",
        subject: subjectHeadline,
        mergevars: {
          TITLE: "Hello, " + user.fname + " " + user.lname + "!",
          BODY:
            "Hello, " +
            user.fname +
            " " +
            user.lname +
            '!<br /><div><h2>A document (<a style="color: blue;" href="' +
            docUrl +
            '">' +
            doc.name +
            "</a>) for " +
            doc.related_object.name +
            " has been " +
            approvalStatus +
            ".</h2><div>" +
            docComments,
        },
      });
    });

    sb.comm.sendEmail(emailBatch, function (ret) {
      var emailObj = {
        to: doc.sent_by.email,
        from: "<EMAIL>",
        subject: subjectHeadline,
        mergevars: {
          TITLE: "Hello, " + doc.sent_by.fname + " " + doc.sent_by.lname + "!",
          BODY:
            "Hello, " +
            doc.sent_by.fname +
            " " +
            doc.sent_by.lname +
            '!<br /><div><h2>A document (<a style="color: blue;" href="' +
            docUrl +
            '">' +
            doc.name +
            "</a>) for " +
            doc.related_object.name +
            " has been " +
            approvalStatus +
            ".</h2><div>" +
            docComments,
        },
      };

      sb.comm.sendEmail(emailObj, function (ret) {
        if (callback) {
          callback(true);
        }

        var systemNotifications = [];

        _.each(doc.notify_list, function (user) {
          systemNotifications.push({
            title:
              'A <a style="color: blue;" href="' +
              docUrl +
              '">document</a> for ' +
              doc.related_object.name +
              " has been " +
              approvalStatus,
            producer: doc.related_object.id,
            color: "green",
            link: sb.data.url.createPageURL("object", {
              type: "contracts",
              id: doc.id,
              name: doc.name,
            }),
            user: user.id,
            icon: "file signature",
            type: "General",
          });
        });

        sb.data.db.obj.create(
          "notification",
          systemNotifications,
          function (res) {}
        );
      });
    });
  }
  function notify_getCommentsHtml(comments) {
    let commentHtml = "";
    if (comments && comments.length) {
      commentHtml += `
<div style="font-family: Lato,'Helvetica Neue',Arial,Helvetica,sans-serif;font-size: 14px;line-height: 16.8px;color: rgba(0,0,0,.87);"">
	<hr style="border-color:rgb(245,245,245);">
	<h3 style="font-size:18px;font-weight:700;padding-bottom:5px;">Document Comments</h3>
`;

      comments.forEach((comment, index) => {
        let background = index % 2 === 0 ? "rgb(245,245,245)" : "#fff";
        let created_date = moment(comment.date_created)
          .local()
          .format("MMM Do YYYY, h:mm a");
        let htmlToAdd = `
	<div style="background:${background};border:1px solid lightgrey;margin-top:5px;padding:15px;">
		<div>
			<span style="font-size:14px;font-weight:700">${comment.author?.name}</span><span style="font-size:12.25px;margin-left:6.125px;color:#1E7ACA">${comment.note_type?.name}</span><span style="font-size:12.25px;margin-left:5px;">${created_date}</span>
		</div>
		<div style="font-size:14px;height:35px;line-height:20px;padding:0 0 0 8px;">
			${comment.note}
		</div>
	</div>
`;
        commentHtml += htmlToAdd;
      });
      commentHtml += "</div>";
    }
    return commentHtml;
  }

  function notify_forApproval(setup, callback) {
    // fn signature
    // setup.doc
    // setup.docHtml
    // callback -> true || false

    var doc = setup.doc;
    var docStructure = setup.docHtml;
    var docComments = notify_getCommentsHtml(setup.docComments);
    var emailBatch = [];
    var docUrl = sb.data.url.getObjectPageParams(doc, {});

    // Notify group managers as well

    var notifyIds = _.pluck(doc.notify_list, "id");

    _.each(doc.related_object.managers, function (managerObj) {
      if (!_.contains(notifyIds, managerObj.id)) {
        doc.notify_list.push(managerObj);
      }
    });

    _.each(doc.notify_list, function (user) {
      emailBatch.push({
        to: user.email,
        from: "<EMAIL>",
        subject:
          "Approval for " +
          setup.doc.name +
          " for " +
          doc.related_object.name +
          " has been requested by " +
          appConfig.user.fname +
          " " +
          appConfig.user.lname +
          ".",
        mergevars: {
          TITLE: "Hello, " + user.fname + " " + user.lname + "!",
          BODY:
            '<div><h2><a style="color: blue;" href="' +
            docUrl +
            '">' +
            doc.name +
            "</a> for " +
            doc.related_object.name +
            " is ready for your approval. Requested by " +
            appConfig.user.fname +
            " " +
            appConfig.user.lname +
            ".</h2>" +
            docComments +
            "<h3>Here is a preview of the document below.</h3><div>" +
            '<div style="border: 1px solid lightgrey; padding: 20px;">' +
            docStructure +
            '<div style="clear: both;"></div><div>' +
            '<br /><br /><a style="color: blue;" href="' +
            docUrl +
            '">Click here to view it online</a>',
        },
      });
    });

    sb.comm.sendEmail(emailBatch, function (ret) {
      if (ret !== false) {
        callback(true);
      } else {
        callback(false);
      }

      var systemNotifications = [];

      _.each(doc.notify_list, function (user) {
        systemNotifications.push({
          title:
            "Approval for " +
            setup.doc.name +
            " for " +
            doc.related_object.name +
            " has been requested by " +
            appConfig.user.fname +
            " " +
            appConfig.user.lname +
            ".",
          producer: doc.related_object.id,
          color: "green",
          link: sb.data.url.createPageURL("object", {
            type: "contracts",
            id: doc.id,
            name: doc.name,
          }),
          user: user.id,
          icon: "file signature",
          type: "General",
        });
      });

      sb.data.db.obj.create(
        "notification",
        systemNotifications,
        function (res) {}
      );
    });
  }

  function Loader(ui, loaderText) {
    ui.makeNode("loadWrap", "div", {});

    ui.loadWrap.makeNode("loader", "loader", {});
    ui.loadWrap.makeNode("loaderText", "div", {
      css: "text-center",
      text: loaderText,
    });
  }

  function getRelatedObject(obj, onComplete) {
    // It its false, its probable been archived
    if (obj.related_object === false) {
      sb.data.db.obj.getById(
        "contracts",
        obj.id,
        function (noChildObjs) {
          if (noChildObjs.related_object) {
            sb.data.db.obj.getById(
              "",
              noChildObjs.related_object,
              function (related) {
                obj.related_object = related;
                onComplete(obj);
              },
              1,
              true
            );
          } else {
            onComplete(obj);
          }
        },
        undefined,
        true,
        true
      );

      // Most of the time, we don't need to do anything
    } else {
      onComplete(obj);
    }
  }

  function saveContract(obj, callback) {
    // Add cell widths to cells that are empty to prevent issues in mPDF
    $("table.medium-editor-table tr").each(function () {
      var amountOfTDs = $(this).children("td").length;
      var percentageOfEachTD = parseFloat(100 / amountOfTDs).toFixed(2);
      var hasWidth = 0;
      $(this)
        .children("td")
        .each(function () {
          if (!_.isEmpty($(this).attr("width"))) {
            hasWidth = 1;
          }
        });
      if (!hasWidth) {
        $(this)
          .children("td")
          .each(function () {
            $(this).attr("width", percentageOfEachTD + "%");
          });
      }
    });

    var bentoEditor = $("#bentoDocumentEditor .editable");
    obj.html_string =
      bentoEditor.length > 0
        ? sb.dom.removeScriptTags(bentoEditor.html())
        : obj.html_string;

    // Get tags
    sb.data.db.obj.getById(
      "",
      obj.id,
      function (contract) {
        obj.tagged_with = contract.tagged_with;

        // Update contract
        sb.data.db.obj.update(
          "contracts",
          {
            id: obj.id,
            html_string: obj.html_string,
            merge_type: obj.merge_type,
            after_signature: obj.after_signature,
            orientation: obj.orientation,
            requires_approval: obj.requires_approval,
            notify_list: obj.notify_list,
          },
          function (updatedObj) {
            // Show alert
            sb.notify({
              type: "display-alert",
              data: {
                header: "Saved",
                body: "Document saved successfully!",
                color: "green",
              },
            });

            // Unbind event to prompt user to save
            window.onbeforeunload = null;
            $("#isWaitingToSave").val("");

            // Callback
            callback(updatedObj);
          }
        );
      },
      1,
      true
    );
  }

  function buildDocumentEditor(ui, obj, mappedMentions, callback) {
    var state = {
      pageObject: obj,
    };

    sb.data.db.obj.getWhere(
      "contracts",
      getTemplateQuery(state),
      function (contracts) {
        _.each(contracts, function (contract) {
          mappedMentions.push("Document_Template.Name:" + contract.name);
        });

        obj.html_string = obj.html_string === "null" ? "" : obj.html_string;

        sb.notify({
          type: "view-field",
          data: {
            type: "detail",
            property: "html_string",
            obj: obj,
            options: {
              edit: true,
              editing: true,
              commitUpdates: false,
              alwaysMerge: false,
              useMedium: true,
              header: false,
              labelTxt: "",
              mentions: mappedMentions,
              promptSave: true,
              previewAndEdit: false,
              css: "",
              style: "padding:0 !important;",
              onChange: function (html) {
                obj.html_string = html;
              },
            },
            ui: ui,
          },
        });

        if (callback) {
          callback();
        }
      }
    );
  }

  // this is the live one
  function singleView(obj, ui, state, draw, edit) {
    var Editor = {};
    // Clear cached merge tags
    CachedMergeTags = [];
    sb.notify({
      type: "clear-entity-cached-merge-tags",
      data: {
        callback: function () {

          var portraitWidth = "830px";
          var portraitHeight = "1253px";
          var landscapeWidth = "1253px";
          var landscapeHeight = "830px";

          // Set default orientation
          obj.orientation = !_.isEmpty(obj.orientation)
            ? obj.orientation
            : "portrait";

          function toggleOrientation(obj, init) {
            if (!init) {
              obj.orientation =
                obj.orientation === "landscape" ? "portrait" : "landscape";
            }
            var headerStyle =
              obj.orientation === "landscape"
                ? { "max-width": landscapeWidth }
                : { "max-width": portraitWidth };
            var bodyStyle =
              obj.orientation === "landscape"
                ? { "min-height": landscapeHeight, "max-width": landscapeWidth }
                : { "min-height": portraitHeight, "max-width": portraitWidth };

            $("#bentoDocumentHeader").css(headerStyle);
            $("#bentoDocumentEditorContainer, #bentoDocumentComments").css(
              bodyStyle
            );

            saveContract(obj, function (updated) {});
          }

          function contactContractType(obj, ui, state, callback) {
            var fieldsToReject = [
              "data_source",
              "closing_date",
              "data_source_id",
              "data_source",
              "external_form",
              "field_type",
              "follow_up_date",
              "is_template",
              "id",
              "stripe_id",
              "state",
              "potential_value",
              "object_uid",
              "quickbooks_id",
              "sales_person",
              "chart_of_accounts",
              "contact_info",
              "is_vendor",
              "markup_percent",
              "parent",
              "child_ids",
              "company_category",
              "default_product",
              "parent_id",
              "products",
            ];

            sb.data.db.controller(
              "getMergeTagData",
              {
                objId: obj.id,
              },
              function (data) {
                var contactBP = data.blueprints.contact;
                var companyBP = data.blueprints.company;

                var mentions = [];
                var allBlueprints = [];
                allBlueprints.push(
                  {
                    type: "contact",
                    blueprints: contactBP,
                  },
                  {
                    type: "company",
                    blueprints: companyBP,
                  }
                );

                _.each(allBlueprints, function (bp) {
                  _.each(bp.blueprints, function (obj, key) {
                    if (fieldsToReject.indexOf(key) == -1) {
                      mentions.push({
                        objectType: bp.type,
                        name: obj.name,
                      });
                    }
                  });
                });

                var mappedMentions = _.compact(
                  _.map(mentions, function (o) {
                    return (
                      o.objectType.toUpperCase() +
                      "." +
                      o.name.toUpperCase().replace(" ", "_")
                    );
                  })
                );

                _.each(MergeTags, function (tag) {
                  mappedMentions.push(tag.tag);
                });

                mappedMentions.unshift("PLEASE SIGN HERE");

                // Build document editor
                buildDocumentEditor(
                  ui.body.editor,
                  obj,
                  mappedMentions,
                  function () {
                    callback();
                  }
                );
              }
            );
          }

          function proposalContractType(obj, ui, state, callback) {
            sb.data.db.controller(
              "getMergeTagData",
              {
                objId: obj.id,
              },
              function (data) {
                var contactBP = data.blueprints.contact;
                var companyBP = data.blueprints.company;
                var proposalBP = data.blueprints.proposal;

                var mentions = [];
                var allBlueprints = [];
                allBlueprints.push(
                  {
                    type: "proposal",
                    blueprints: proposalBP,
                  },
                  {
                    type: "contact",
                    blueprints: contactBP,
                  },
                  {
                    type: "company",
                    blueprints: companyBP,
                  }
                );

                _.each(allBlueprints, function (bp) {
                  if (bp.type == "proposal") {
                    bp.blueprints.item_list = {
                      name: "item_list",
                    };

                    bp.blueprints.simple_item_list = {
                      name: "simple_item_list",
                    };

                    bp.blueprints.item_list_by_cat = {
                      name: "item_list_by_cat",
                    };

                    bp.blueprints.item_list_just_qty = {
                      name: "item_list_just_qty",
                    };

                    bp.blueprints.item_list_total = {
                      name: "item_list_total",
                    };

                    bp.blueprints.menu_note = {
                      name: "invoice_note",
                    };

                    bp.blueprints.payment_schedule = {
                      name: "payment_schedule",
                    };

                    bp.blueprints.logo = {
                      name: "logo",
                    };

                    bp.blueprints.invoice_header = {
                      name: "invoice_header",
                    };

                    bp.blueprints.invoice = {
                      name: "invoice",
                    };

                    bp.blueprints.invoice_nlp = {
                      name: "invoice_nlp",
                    };
                  }

                  _.each(bp.blueprints, function (obj, key) {
                    if (fieldsToReject.indexOf(key) == -1) {
                      mentions.push({
                        objectType: bp.type,
                        name: obj.name,
                      });
                    }
                  });
                });

                mentions.push({
                  objectType: "proposal",
                  name: "start_time",
                });
                mentions.push({
                  objectType: "proposal",
                  name: "end_time",
                });
                mentions.push({
                  objectType: "proposal",
                  name: "manager_signature",
                });
                mentions.push({
                  objectType: "proposal",
                  name: "manager_link",
                });

                var mappedMentions = _.compact(
                  _.map(mentions, function (o) {
                    return (
                      o.objectType.toUpperCase() +
                      "." +
                      o.name.toUpperCase().replace(" ", "_")
                    );
                  })
                );

                _.each(MergeTags, function (tag) {
                  mappedMentions.push(tag.tag);
                });

                mappedMentions.unshift("PLEASE SIGN HERE");

                // Build document editor
                buildDocumentEditor(
                  ui.body.editor,
                  obj,
                  mappedMentions,
                  function () {
                    callback();
                  }
                );
              }
            );
          }

          function toggleEditMode(ui, edit) {
            // Clear the UI
            ui.actions.empty();
            ui.header.title.empty();
            ui.body.editor.empty();

            // Add loader
            ui.body.editor.makeNode("loader", "loader", {
              size: "large",
            });
            ui.body.editor.patch();

            getRelatedObject(obj, function (obj) {
              var statusString;
              var canEdit =
                obj.status === "" ||
                obj.status === null ||
                obj.status === "Unsigned"
                  ? true
                  : false;
              var isTemplate = false;
              var notifyListIds = _.pluck(obj.notify_list, "id");
              var showToolbar = true;

              if (state && state.toolbar === false) {
                showToolbar = false;
              }

              if (obj.related_object) {
                obj.main_contact = obj.related_object.main_contact;
              }

              // Actions
              if (!edit) {
                var dom = ui;
                dom.makeNode("menu", "div", {});
                dom.menu.makeNode("right", "div", {});

                var actions = {
                  archive: true,
                  copyLink: true,
                  viewPDF: {
                    title: "View PDF",
                    color: "grey",
                    icon: "file pdf",
                    action: function (obj, s, onComplete) {
                      sb.notify({
                        type: "view-pdf",
                        data: {
                          obj: obj,
                          callback: function () {
                            onComplete();
                          },
                        },
                      });
                    },
                  },
                  viewPublicLink: {
                    title: "View Public Link",
                    color: "teal",
                    icon: "linkify",
                    link:
                      sb.url +
                      "/app/documents#?&i=" +
                      appConfig.instance +
                      "&wid=" +
                      obj.id,
                  },
                  sendEmail: true,
                  toggleFollow: true,
                  toggleIsPublic: true,
                  move: true,
                  toggleTemplate: true,
                };

                if (!obj.is_template) {
                  if (obj.status != "Signed" && isTemplate === false) {
                    if (obj.requires_approval == "Yes") {
                      if (
                        obj.status !== "Approval Requested" &&
                        obj.status !== "Approved"
                      ) {
                        actions.requestApproval = {
                          title: "Request Approval",
                          color: "grey",
                          icon: "question",
                          action: function (obj, s, onComplete) {
                            dom.modals.makeNode("modal", "modal", {
                              onShow: function () {
                                var modal = dom.modals.modal.body;

                                modal.makeNode("title", "div", {
                                  text: "Request Approval",
                                  css: "ui huge header",
                                });

                                modal
                                  .makeNode("btn", "div", {
                                    text: "Submit for approval",
                                    css: "ui green button",
                                  })
                                  .notify("click", {
                                    type: "contracts-run",
                                    data: {
                                      run: function () {
                                        modal.empty();

                                        Loader(modal, "Building document...");

                                        modal.patch();

                                        createMergedHTML(obj, function (html) {
                                          var selectionObj = {
                                            type_id: obj.id,
                                            childObjs: {
                                              author: true,
                                              date_created: true,
                                              note: true,
                                              note_type: true,
                                              record_type: true,
                                            },
                                          };

                                          sb.data.db.obj.getWhere(
                                            "notes",
                                            selectionObj,
                                            function (comments) {
                                              modal.empty();

                                              Loader(
                                                modal,
                                                "Sending approval emails..."
                                              );

                                              modal.patch();

                                              obj.sent_by =
                                                +sb.data.cookie.userId;

                                              notify_forApproval(
                                                {
                                                  doc: obj,
                                                  docHtml: html,
                                                  docComments: comments,
                                                },
                                                function (resp) {
                                                  modal.empty();

                                                  Loader(
                                                    modal,
                                                    "Updating document data..."
                                                  );

                                                  modal.patch();

                                                  obj.tagged_with =
                                                    obj.tagged_with.concat(
                                                      obj.notify_list
                                                    );

                                                  sb.data.db.obj.update(
                                                    "contracts",
                                                    {
                                                      id: obj.id,
                                                      html_string: html,
                                                      status:
                                                        "Approval Requested",
                                                      sent_by:
                                                        +sb.data.cookie.userId,
                                                      sent_on: moment().format(
                                                        "YYYY-MM-DD HH:mm:ss.SS"
                                                      ),
                                                      tagged_with:
                                                        obj.tagged_with,
                                                    },
                                                    function (updated) {
                                                      dom.modals.modal.hide();

                                                      sb.dom.alerts.alert(
                                                        "Submitted",
                                                        "Admins have been notified.",
                                                        "success"
                                                      );

                                                      singleView(
                                                        updated,
                                                        dom,
                                                        state,
                                                        draw
                                                      );
                                                    },
                                                    2
                                                  );
                                                }
                                              );
                                            }
                                          );
                                        });
                                      },
                                    },
                                  });

                                modal.makeNode("noteBreak", "div", {
                                  text: "<br />",
                                });

                                modal.makeNode("noteTitle", "div", {
                                  text: "Add notes before submitting.",
                                });

                                modal.makeNode("noteContBreak", "div", {
                                  text: "<br />",
                                });

                                modal.makeNode("notes", "div", { css: "" });

                                modal.patch();

                                sb.notify({
                                  type: "show-note-list-box",
                                  data: {
                                    domObj: modal.notes,
                                    objectIds: [obj.id],
                                    objectId: obj.id,
                                  },
                                });
                              },
                            });

                            dom.modals.patch();

                            dom.modals.modal.show();

                            onComplete();
                          },
                        };
                      }
                    }
                  }

                  if (
                    obj.requires_approval != "Yes" ||
                    obj.status == "Approved"
                  ) {
                    actions.signNow = {
                      title: "Sign Now",
                      color: "grey",
                      icon: "file contract",
                      action: function (obj, s, onComplete) {
                        $("#loader").fadeIn();

                        createMergedHTML(obj, function (html) {
                          sb.data.db.obj.update(
                            "contracts",
                            {
                              id: obj.id,
                              html_string: html,
                              status: "Signing In Process",
                            },
                            function (updated) {
                              window.open(
                                sb.url +
                                  "/app/contracts#?&i=" +
                                  appConfig.instance +
                                  "&wid=" +
                                  obj.id
                              );

                              singleView(updated, dom, state, draw);
                            }
                          );
                        });
                      },
                    };

                    actions.requestSignature = {
                      title: "Request Signature",
                      color: "grey",
                      icon: "signature",
                      action: function (obj, s, onComplete) {
                        var emailAddresss = [],
                          addEmails = [],
                          link =
                            '<a target="_blank" href="' +
                            sb.url +
                            "/app/contracts#?&i=" +
                            appConfig.instance +
                            "&wid=" +
                            obj.id +
                            '">CLICK HERE TO VIEW</a>';
                        var checkSettingsOptions;

                        if ( appConfig.instance == 'dreamcatering' ) {
                            if ( (obj.parent || {}).object_bp_type == 'groups' ){
                                checkSettingsOptions = {project: obj.parent};
                            }
                        }

                        if (obj.main_contact) {
                          sb.data.db.obj.getById(
                            "contacts",
                            obj.main_contact.id,
                            function (mainContact) {
                              dom.modals.makeNode("send", "modal", {});

                              dom.modals.send.body.makeNode("cont", "div", {});

                              dom.modals.patch();

                              dom.modals.send.show();

                              if (obj.hasOwnProperty("main_contact")) {
                                if (
                                  mainContact.hasOwnProperty("contact_info")
                                ) {
                                  _.each(
                                    mainContact.contact_info,
                                    function (info) {
                                      if (
                                        info.type.data_type == "email" &&
                                        info.is_primary == "yes"
                                      ) {
                                        emailAddresss.push(info.info);
                                      }
                                    }
                                  );
                                }
                              }

                              // check for additional contacts
                              var addEmails = [
                                {
                                  label:
                                    "<b>" +
                                    mainContact.fname +
                                    " " +
                                    mainContact.lname +
                                    " - Main Contact</b>",
                                  value: emailAddresss,
                                  checked: true,
                                },
                              ];

                              if (obj.additional_contacts) {
                                _.each(
                                  obj.additional_contacts,
                                  function (contact) {
                                    if (contact.contact_info) {
                                      _.each(
                                        contact.contact_info,
                                        function (info) {
                                          if (info.type.data_type == "email") {
                                            addEmails.push({
                                              label:
                                                contact.fname +
                                                " " +
                                                contact.lname +
                                                " - " +
                                                contact.type.name,
                                              value: info.info,
                                            });
                                          }
                                        }
                                      );
                                    }
                                  }
                                );
                              }

                              checkSettingsObject(function (settingsObj) {
                                var venue = "";
                                if (obj.venue) {
                                  venue = obj.venue.name;
                                }

                                var managerFname = "";
                                var managerLname = "";
                                if (obj.manager) {
                                  managerFname = obj.manager.fname;
                                  managerLname = obj.manager.lname;
                                }

                                var mergeVars = [
                                  {
                                    tag: "*|first_name|*",
                                    value: mainContact.fname,
                                  },
                                  {
                                    tag: "*|last_name|*",
                                    value: mainContact.lname,
                                  },
                                  {
                                    tag: "{{PROPOSAL.NAME}}",
                                    value: obj.related_object.name,
                                  },
                                  {
                                    tag: "{{PROJECT.NAME}}",
                                    value: obj.related_object.name,
                                  },
                                  {
                                    tag: "{{PROPOSAL.START_DATE}}",
                                    value: moment(obj.related_object.start_date)
                                      .local()
                                      .format("MMMM Do YYYY, h:mm a"),
                                  },
                                  {
                                    tag: "{{DOCUMENT.NAME}}",
                                    value: obj.name,
                                  },
                                  {
                                    tag: "*|contract_name|*",
                                    value: obj.name,
                                  },
                                  {
                                    tag: "*|work_order_name|*",
                                    value: obj.name,
                                  },
                                  {
                                    tag: "*|venue|*",
                                    value: venue,
                                  },
                                  {
                                    tag: "{{PROJECT.START_DATE}}",
                                    value: moment(obj.related_object.start_date)
                                      .local()
                                      .format("MMMM Do YYYY, h:mm a"),
                                  },
                                  {
                                    tag: "*|start_date|*",
                                    value: moment(obj.start_date).format(
                                      "M/D/YYYY h:mm a"
                                    ),
                                  },
                                  {
                                    tag: "*|manager_first_name|*",
                                    value: managerFname,
                                  },
                                  {
                                    tag: "*|manager_last_name|*",
                                    value: managerLname,
                                  },
                                ];

                                _.each(mergeVars, function (tagObj) {
                                  settingsObj.request_email =
                                    settingsObj.request_email.replace(
                                      tagObj.tag,
                                      tagObj.value
                                    );
                                  settingsObj.request_email_subject =
                                    settingsObj.request_email_subject.replace(
                                      tagObj.tag,
                                      tagObj.value
                                    );
                                });

                                sb.notify({
                                  type: "show-compose-form",
                                  data: {
                                    domObj: dom.modals.send.body.cont,
                                    objectId: obj.id,
                                    objectType: "contracts",
                                    email: {
                                      to: "",
                                      additionalEmails: addEmails,
                                      subject:
                                        settingsObj.request_email_subject,
                                      message:
                                        settingsObj.request_email +
                                        "<br />" +
                                        link,
                                    },
                                    action: function (sentEmail) {
                                      dom.modals.send.hide();

                                      $("#loader").fadeIn();

                                      createMergedHTML(obj, function (html) {
                                        sb.data.db.obj.update(
                                          "contracts",
                                          {
                                            id: obj.id,
                                            html_string: html,
                                            status: "Out For Signature",
                                          },
                                          function (updated) {
                                            var noteObj = {
                                              type_id: obj.id,
                                              type: "contracts",
                                              note: "Contract has been emailed to the client.",
                                              note_type: 0,
                                              author: +sb.data.cookie.userId,
                                              notifyUsers: [],
                                            };

                                            sb.data.db.obj.create(
                                              "notes",
                                              noteObj,
                                              function (createdNote) {
                                                singleView(
                                                  updated,
                                                  dom,
                                                  state,
                                                  draw
                                                );
                                              }
                                            );
                                          }
                                        );
                                      });
                                    },
                                  },
                                });
                              }, checkSettingsOptions);

                              onComplete();
                            },
                            2
                          );
                        } else {
                          onComplete();

                          dom.modals.makeNode("send", "modal", {});

                          dom.modals.send.body.makeNode("cont", "div", {});

                          dom.modals.patch();

                          dom.modals.send.show();

                          checkSettingsObject(function (settingsObj) {
                            var venue = "";
                            if (obj.venue) {
                              venue = obj.venue.name;
                            }

                            var managerFname = "";
                            var managerLname = "";
                            if (obj.manager) {
                              managerFname = obj.manager.fname;
                              managerLname = obj.manager.lname;
                            }

                            var mergeVars = [];

                            _.each(mergeVars, function (tagObj) {
                              settingsObj.request_email =
                                settingsObj.request_email.replace(
                                  tagObj.tag,
                                  tagObj.value
                                );
                              settingsObj.request_email_subject =
                                settingsObj.request_email_subject.replace(
                                  tagObj.tag,
                                  tagObj.value
                                );
                            });

                            sb.notify({
                              type: "show-compose-form",
                              data: {
                                domObj: dom.modals.send.body.cont,
                                objectId: obj.id,
                                objectType: "contracts",
                                email: {
                                  to: "",
                                  subject: settingsObj.request_email_subject,
                                  message:
                                    settingsObj.request_email + "<br />" + link,
                                },
                                action: function (sentEmail) {
                                  dom.modals.send.hide();

                                  $("#loader").fadeIn();

                                  createMergedHTML(obj, function (html) {
                                    sb.data.db.obj.update(
                                      "contracts",
                                      {
                                        id: obj.id,
                                        html_string: html,
                                        status: "Out For Signature",
                                      },
                                      function (updated) {
                                        var noteObj = {
                                          type_id: obj.id,
                                          type: "contracts",
                                          note: "Contract has been emailed to the client.",
                                          note_type: 0,
                                          author: +sb.data.cookie.userId,
                                          notifyUsers: [],
                                        };

                                        sb.data.db.obj.create(
                                          "notes",
                                          noteObj,
                                          function (createdNote) {
                                            singleView(
                                              updated,
                                              dom,
                                              state,
                                              draw
                                            );
                                          }
                                        );
                                      }
                                    );
                                  });
                                },
                              },
                            });
                          }, checkSettingsOptions);
                        }
                      },
                    };
                  }
                } else if (!_.isEmpty(obj.related_object)) {
                  actions.pay = {
                    title: "Pay",
                    color: "grey",
                    icon: "dollar",
                    action: function (obj, s, onComplete) {
                      window.open(
                        sb.url +
                          "/app/invoices#?&i=" +
                          appConfig.instance +
                          "&pid=" +
                          obj.related_object.id
                      );

                      onComplete();
                    },
                  };
                }

                //Begin Test Approval Document

                if (obj.status == "Approval Requested") {
                  //End test Approval Document

                  //if (obj.status == 'Approval Requested' && notifyListIds.indexOf(+sb.data.cookie.userId) > -1) {
                  actions.submitApproval = {
                    title: "Submit Approval",
                    color: "grey",
                    icon: "check",
                    action: function (obj, s, onComplete) {
                      ui.modals.makeNode("approve", "modal", {
                        onShow: function () {
                          var selectionObj = {
                            type_id: obj.id,
                            childObjs: {
                              author: true,
                              date_created: true,
                              note: true,
                              note_type: true,
                              record_type: true,
                            },
                          };

                          var modal = ui.modals.approve.body;

                          modal.makeNode("title", "div", {
                            text: "Submit approval",
                            css: "ui huge header",
                          });

                          modal.makeNode("btns", "div", {
                            css: "ui huge buttons",
                          });

                          modal.btns
                            .makeNode("approve", "div", {
                              css: "ui green button",
                              text: "Approve",
                            })
                            .notify(
                              "click",
                              {
                                type: "contracts-run",
                                data: {
                                  run: function (obj) {
                                    modal.btns.approve.loading();
                                    modal.btns.decline.loading();

                                    sb.data.db.obj.getWhere(
                                      "notes",
                                      selectionObj,
                                      function (comments) {
                                        sb.data.db.obj.update(
                                          "contracts",
                                          { id: obj.id, status: "Approved" },
                                          function (updated) {
                                            notify_Approval_Status_Update(
                                              {
                                                doc: obj,
                                                docComments: comments,
                                              },
                                              "Approved"
                                            );

                                            dom.modals.approve.hide();

                                            singleView(
                                              updated,
                                              dom,
                                              state,
                                              draw
                                            );
                                          },
                                          2
                                        );
                                      }
                                    );
                                  }.bind({}, obj),
                                },
                              },
                              sb.moduleId
                            );
                          modal.btns.makeNode("or", "div", {
                            css: "ui or",
                            text: "or",
                          });
                          modal.btns
                            .makeNode("decline", "div", {
                              css: "ui red button",
                              text: "Decline",
                            })
                            .notify(
                              "click",
                              {
                                type: "contracts-run",
                                data: {
                                  run: function (obj) {
                                    modal.btns.approve.loading();
                                    modal.btns.decline.loading();

                                    sb.data.db.obj.getWhere(
                                      "notes",
                                      selectionObj,
                                      function (comments) {
                                        sb.data.db.obj.update(
                                          "contracts",
                                          { id: obj.id, status: "Declined" },
                                          function (updated) {
                                            notify_Approval_Status_Update(
                                              {
                                                doc: obj,
                                                docComments: comments,
                                              },
                                              "Declined"
                                            );
                                            dom.modals.approve.hide();

                                            singleView(
                                              updated,
                                              dom,
                                              state,
                                              draw
                                            );
                                          },
                                          2
                                        );
                                      }
                                    );
                                  }.bind({}, obj),
                                },
                              },
                              sb.moduleId
                            );

                          var style = sb.dom.isMobile
                            ? ""
                            : "margin:5px 15px 15px 15px";
                          modal
                            .makeNode("noteContainer", "div", {
                              css: "ui one column centered grid",
                            })
                            .makeNode("col", "div", {
                              css: "sixteen wide column round-border",
                              style: style,
                            });

                          modal.makeNode("notesBreak", "div", {
                            text: "<br />",
                          });

                          modal.patch();

                          sb.notify({
                            type: "show-note-list-box",
                            data: {
                              domObj: modal.noteContainer.col,
                              objectIds: [obj.id],
                              objectId: obj.id,
                            },
                          });
                        },
                      });

                      ui.modals.patch();

                      ui.modals.approve.show();
                    },
                  };
                }

                sb.notify({
                  type: "view-field",
                  data: {
                    type: "actions-set",
                    property: "any-state",
                    obj: obj,
                    options: {
                      actions: actions,
                    },
                    ui: ui.actions.makeNode("previewEllipsis", "div", {
                      id: "bentoDocumentPreviewEllipsis",
                      css: "pull-right",
                    }),
                  },
                });
              } else {
                var oppositeOrientation =
                  obj.orientation === "landscape" ? "portrait" : "landscape";

                sb.notify({
                  type: "view-field",
                  data: {
                    type: "actions-set",
                    property: "any-state",
                    obj: obj,
                    options: {
                      actions: {
                        settings: {
                          title: "Settings",
                          color: "grey",
                          icon: "cog",
                          action: function (obj, s, onComplete) {
                            ui.modals.makeNode("modal", "modal", {
                              onShow: function () {
                                var modal = ui.modals.modal.body;

                                modal.makeNode("title", "div", {
                                  text: "Settings",
                                  css: "ui huge header",
                                });

                                function notifications(
                                  obj,
                                  ui,
                                  state,
                                  callback
                                ) {
                                  sb.data.db.obj.getAll(
                                    "users",
                                    function (users) {
                                      var users = _.map(users, function (user) {
                                        return {
                                          name: "staff",
                                          label: user.fname + " " + user.lname,
                                          value: user.id,
                                        };
                                      });

                                      modal.notificationsForm.makeNode(
                                        "user-notifications",
                                        "form",
                                        {
                                          staff: {
                                            label: "Who should be notified?",
                                            name: "staff",
                                            type: "checkbox",
                                            options: users,
                                            value: obj.notify_list,
                                          },
                                        }
                                      );

                                      modal.notificationsForm.patch();

                                      callback(true);
                                    },
                                    { fname: true, lname: true }
                                  );
                                }

                                var formObj = {
                                  merge_type: {
                                    label: "Where will this template be used?",
                                    name: "merge_type",
                                    type: "select",
                                    value: obj.merge_type,
                                    options: [
                                      {
                                        name: "Proposal Builder",
                                        value: "proposal",
                                      },
                                      {
                                        name: "Contact",
                                        value: "contact",
                                      },
                                      {
                                        name: "Space",
                                        value: "space",
                                      },
                                      {
                                        name: "Portal",
                                        value: "portal",
                                      },
                                    ],
                                  },
                                  after_signature: {
                                    label: "After signature",
                                    name: "after_signature",
                                    type: "select",
                                    value: obj.after_signature,
                                    options: [
                                      {
                                        name: "Do Nothing",
                                        value: "nothing",
                                      },
                                      {
                                        name: "Send to invoices portal",
                                        value: "invoices",
                                      },
                                    ],
                                  },
                                  requires_approval: {
                                    label: "Requires Approval",
                                    name: "requires_approval",
                                    type: "select",
                                    value: obj.requires_approval,
                                    options: [
                                      {
                                        name: "No",
                                        value: "No",
                                      },
                                      {
                                        name: "Yes",
                                        value: "Yes",
                                      },
                                    ],
                                    onChange: function (value) {
                                      $("#loader").fadeIn();

                                      notifications(
                                        obj,
                                        dom,
                                        state,
                                        function () {
                                          $("#loader").fadeOut();
                                        }
                                      );
                                    },
                                  },
                                };

                                if (!obj.is_template) {
                                  delete formObj.merge_type;
                                  delete formObj.requires_approval;
                                }

                                modal.makeNode("form", "form", formObj);

                                modal.makeNode("notificationsForm", "div", {});

                                if (obj.requires_approval === "Yes") {
                                  notifications(obj, ui, state, function () {});
                                }

                                modal
                                  .makeNode("btn", "div", {
                                    text: "Save",
                                    css: "ui large green button",
                                    style: "margin-top:30px; float:right;",
                                  })
                                  .notify("click", {
                                    type: "contracts-run",
                                    data: {
                                      run: function () {
                                        if (
                                          modal.form.process().fields.merge_type
                                        ) {
                                          obj.merge_type =
                                            modal.form.process().fields.merge_type.value;
                                        }

                                        if (
                                          modal.form.process().fields
                                            .after_signature
                                        ) {
                                          obj.after_signature =
                                            ui.modals.modal.body.form.process().fields.after_signature.value;
                                        }

                                        if (obj.is_template) {
                                          if (
                                            ui.modals.modal.body.form.process()
                                              .fields.requires_approval
                                          ) {
                                            obj.requires_approval =
                                              ui.modals.modal.body.form.process().fields.requires_approval.value;
                                          }
                                        }

                                        if (obj.requires_approval === "Yes") {
                                          if (
                                            ui.modals.modal.body.notificationsForm[
                                              "user-notifications"
                                            ].process().fields.staff
                                          ) {
                                            var notificationList =
                                              ui.modals.modal.body.notificationsForm[
                                                "user-notifications"
                                              ].process().fields.staff.value;
                                            notificationList = _.map(
                                              notificationList,
                                              function (not) {
                                                return +not;
                                              }
                                            );
                                            obj.notify_list = notificationList;
                                          }
                                        }

                                        ui.modals.modal.hide();

                                        saveContract(obj, function (updated) {
                                          updated.related_object = false;

                                          setTemplateTypeDisplay(updated);

                                          getRelatedObject(
                                            updated,
                                            function (obj) {
                                              toggleEditMode(ui, true);
                                            }
                                          );
                                        });
                                      },
                                    },
                                  });

                                modal.patch();
                              },
                            });

                            ui.modals.patch();

                            ui.modals.modal.show();

                            onComplete();
                          },
                        },
                        toggleOrientation: {
                          title: oppositeOrientation + " Mode",
                          color: "grey",
                          icon: "sync",
                          action: function (obj, s, onComplete) {
                            toggleOrientation(obj, false);

                            oppositeOrientation =
                              obj.orientation === "landscape"
                                ? "portrait"
                                : "landscape";

                            this.title = oppositeOrientation + " Mode";

                            onComplete();
                          },
                        },
                      },
                    },
                    ui: ui.actions.makeNode("editEllipsis", "div", {
                      id: "bentoDocumentEditEllipsis",
                      css: "pull-right",
                    }),
                  },
                });
              }

              if (canEdit) {
                if (!edit) {
                  ui.actions.makeNode("editBtnContainer", "div", {
                    id: "bentoDocumentEditBtnContainer",
                    css: "pull-right",
                    style: "padding-right:6px; margin-top:-8px;",
                  });
                  ui.actions.editBtnContainer
                    .makeNode("editBtn", "div", {
                      css: "ui medium basic button",
                      style: "font-size:1rem; padding: 10px;",
                      text: '<i class="fas fa-pencil"></i> Edit',
                    })
                    .notify("click", {
                      type: "contracts-run",
                      data: {
                        run: function () {
                          ui.actions.editBtnContainer.editBtn.loading();

                          toggleEditMode(ui, true);
                        },
                      },
                    });
                } else {
                  ui.actions.makeNode("saveBtnContainer", "div", {
                    id: "bentoDocumentSaveBtnContainer",
                    css: "pull-right",
                    style: "padding-right:6px; margin-top:-8px;",
                  });
                  ui.actions.saveBtnContainer
                    .makeNode("saveBtn", "div", {
                      css: "ui medium green button",
                      style: "font-size:1rem; padding: 10px;",
                      text: '<i class="fas fa-save"></i> Save & Close',
                    })
                    .notify("click", {
                      type: "contracts-run",
                      data: {
                        run: function () {
                          ui.actions.saveBtnContainer.saveBtn.loading();

                          saveContract(obj, function (updated) {
                            obj.related_object = false;
                            getRelatedObject(obj, function (obj) {
                              toggleEditMode(ui, false);
                            });
                          });
                        },
                      },
                    });
                }
              }

              // if(obj.requires_approval === 'Yes' && canEdit == true){

              // 	ui.body.makeNode('approval', 'div', {css:'ui teal label', text:'* Requires admin approval before sending.'});

              // }

              // if(obj.after_signature === 'invoices'){

              // 	ui.body.makeNode('invoices', 'div', {css:'ui teal label', text:'* Send to invoices after signature'});

              // }

              var editTitle = canEdit ? true : false;
              var margin = editTitle ? "margin-top:5px;" : "margin-top:0px;";
              sb.notify({
                type: "view-field",
                data: {
                  type: "title",
                  property: "name",
                  obj: obj,
                  options: {
                    edit: editTitle,
                    editing: editTitle,
                    fontSize: "2rem",
                    style: "padding-bottom:0px;" + margin,
                  },
                  ui: ui.header.title,
                  onUpdate: function (newVal) {
                    if (!_.isEmpty(newVal)) {
                      obj.name = newVal;
                    }
                  },
                },
              });

              var mergeForPdf = true;
              if (state && state.hasOwnProperty("mergeForPdf")) {
                mergeForPdf = state.mergeForPdf;
              }

              ui.actions.patch();
              ui.header.title.patch();

              if (canEdit) {
                if (!edit) {
                  // Set to loading initially to prevent user from going to edit mode while merging
                  ui.actions.editBtnContainer.editBtn.loading();
                } else {
                  // Set to loading initially to prevent user from going to preview mode while saving
                  ui.actions.saveBtnContainer.saveBtn.loading();
                }
              }

              if (!edit) {
                function buildPreview(string) {
                  string = string === "null" ? "" : string;

                  if (typeof getHtml === "function") {
                    getHtml({
                      html: sb.dom.removeScriptTags(string),
                    });
                  }

                  ui.body.editor.makeNode("text", "div", {
                    text: sb.dom.removeScriptTags(string),
                    id: "bentoDocumentEditorPreviewer",
                  });

                  // Hide Loader
                  delete ui.body.editor.loader;

                  // Patch the dom
                  ui.body.editor.patch();

                  setTimeout(function () {
                    // Add sortability
                    $("table").tablesort();

                    // Add links
                    //!WORKING HERE
                    $(".bento-link").each(function (index) {
                      var type = $(this).data().type;
                      var link = "";
                      switch (type) {
                        case "attachment":
                          link = sb.data.files.getURL({
                            loc: $(this).data().loc,
                          });
                          if (_.isEmpty($(this).attr("href"))) {
                            $(this).attr("href", link);
                          }
                          // sb.data.url.createPageURL(
                          // 	'object',
                          // 	{
                          // 		type:'document',
                          // 		id:newObj.id,
                          // 		name:newObj.name
                          // 	}
                          // );
                          break;

                        default:
                          link = sb.data.url.createPageURL("object", {
                            name: $(this).text(),
                            type: type,
                            id: $(this).data().entity,
                          });
                          $(this).attr("href", link);
                          break;
                      }
                    });
                  }, 1000);

                  if (ui.hasOwnProperty("loading") && ui.loading) {
                    ui.loading(false);
                  }

                  // Pass the compiled html back to the caller
                  if (typeof draw === "function") {
                    draw(string);
                  }

                  if (canEdit) {
                    if (!edit) {
                      // Enable the edit button after merging
                      ui.actions.editBtnContainer.editBtn.loading(false);
                    }
                  }
                }

                if (canEdit) {
                  createMergedHTML(
                    obj,
                    function (string) {
                      buildPreview(string);
                    },
                    undefined,
                    undefined,
                    {
                      mergeForPdf: mergeForPdf,
                    }
                  );
                } else {
                  buildPreview(obj.html_string);
                }
              } else {
                if (!obj.merge_type) {
                  obj.merge_type = "proposal";
                }

                sb.data.db.obj.getById("", obj.id, function (unmerged) {
                  obj.html_string = unmerged.html_string;

                  if (obj.merge_type == "proposal") {
                    proposalContractType(obj, ui, state, function () {
                      delete ui.body.editor.loader;
                      ui.actions.saveBtnContainer.saveBtn.loading(false);
                      ui.body.editor.patch();
                    });
                  } else {
                    contactContractType(obj, ui, state, function () {
                      delete ui.body.editor.loader;
                      ui.actions.saveBtnContainer.saveBtn.loading(false);
                      ui.body.editor.patch();
                    });
                  }
                });
              }
            });
          }

          var padding = "";
          if (!state.inCollections && !sb.dom.isMobile) {
            padding = "margin:0 auto; padding-left:54px;";
          }

          // Actions dropdown
          ui.makeNode("actions", "div", {
            id: "bentoDocumentActions",
            style: "position:relative; z-index:999; " + padding,
          });

          // Header
          var headerOrientationStyle =
            obj.orientation === "landscape"
              ? "max-width:" + landscapeWidth
              : "max-width:" + portraitWidth;
          var padding = "";
          if (!state.inCollections && !sb.dom.isMobile) {
            padding = "margin:0 auto;";
          }
          ui.makeNode("header", "div", {
            id: "bentoDocumentHeader",
            style:
              "position:relative; z-index:99; margin:0 auto; " +
              padding +
              headerOrientationStyle,
          });

          function setTemplateTypeDisplay(obj) {
            ui.header.templateLabelContainer.empty();
            ui.header.templateLabelContainer.makeNode("templateLabel", "div", {
              css: "ui grey icon",
              text:
                '<i class="window restore outline icon"></i> Document Template (<i>' +
                obj.merge_type.toUpperCase() +
                "</i>)",
            });
            ui.header.templateLabelContainer.patch();
          }

          // Template Indicator
          if (obj.is_template) {
            ui.header.makeNode("templateLabelContainer", "div", {});
            setTemplateTypeDisplay(obj);
          }

          // Status indicator
          if (!edit && !obj.is_template) {
            switch (obj.status) {
              case "Approved":
                statusString =
                  '<div class="ui green label">' + obj.status + "</div>";

                break;

              case "Declined":
                statusString =
                  '<div class="ui red label">' + obj.status + "</div>";

                break;

              case "Approval Requested":
                statusString =
                  '<div class="ui blue label">' + obj.status + "</div>";

                break;

              case "Signed":
                statusString =
                  '<div class="ui green label">' + obj.status + "</div>";

                break;

              case "Signing In Progress":
                statusString =
                  '<div class="ui orange label"><nobr>' +
                  obj.status +
                  "</nobr></div>";

                break;

              case "Out For Signature":
                statusString =
                  '<div class="ui yellow label"><nobr>' +
                  obj.status +
                  "</nobr></div>";

                break;

              default:
                statusString = '<div class="ui label">Unsigned</div>';
            }

            ui.header.makeNode("statusLabelContainer", "div", {
              id: "bentoDocumentStatusLabelContainer",
              style: "margin-bottom:10px;",
            });
            ui.header.statusLabelContainer.makeNode("statusLabel", "div", {
              style: "font-size:1rem; display:inline-block;",
              text: statusString,
            });

            // Display originating template if this contract was created from a template
            if (obj.data_source_id) {
              // Handle case where data_source_id is an integer ID - need to fetch the record
              if (typeof obj.data_source_id === 'number' || (typeof obj.data_source_id === 'string' && !isNaN(obj.data_source_id))) {

                sb.data.db.obj.getById('contracts', obj.data_source_id, function(template) {

                  if (template && template.name) {
                    var templateName = template.name;

                    ui.header.statusLabelContainer.makeNode("templateSourceLabel", "div", {
                      style: "font-size:1rem; display:inline-block; margin-left: 10px;",
                      text: '<div class="ui grey label"><i class="copy icon"></i>From: ' + templateName + '</div>',
                    });

                    ui.header.statusLabelContainer.patch();
                  }
                });
              }
            }

            if ( obj.status == "Signed" && obj.html_string ) {

                if ( obj.html_string.includes("PLEASE SIGN HERE") ) {
                    ui.header.statusLabelContainer.makeNode("statusRefresh", "div", {
                      style:
                        "display: inline-block; margin-left: 10px; font-size:1rem; cursor:pointer",
                      text: '<div class="ui blue label">Refresh Signature</div>',
                    });

                    ui.header.statusLabelContainer.statusRefresh.notify(
                      "click",
                      {
                        type: "contracts-run",
                        data: {
                          run: function (contract) {
                            /// REFRESH BUTTON FOR CONTRACT SIGNATURE
                            function scanDocumentForClickToSign(contract, next) {
                              if (
                                contract.html_string.includes(
                                  '<a id="startSignature" class="ui green left floated compact button">Click To Sign</a>'
                                )
                              ) {

                                contract.html_string_merged =
                                  contract.html_string.replace(
                                    new RegExp(
                                      '<a id="startSignature" class="ui green left floated compact button">Click To Sign</a>'
                                    ),
                                    ""
                                  );
                                contract.html_string = contract.html_string_merged;

                                scanDocumentForClickToSign(contract, next);
                              } else {
                                next(contract);
                              }
                            }

                            function scanDocumentForSignatureLines(
                              contract,
                              next,
                              count
                            ) {
                              var sigReplacedCount = count || 0;
                              var refreshMessage = contract.signer_name
                                ? "Added a signature for " +
                                  contract.signer_name +
                                  " in (" +
                                  sigReplacedCount +
                                  ") places."
                                : "Added a signature in (" +
                                  sigReplacedCount +
                                  ") places.";

                              if (contract.html_string.includes("PLEASE SIGN HERE")) {

                                contract.html_string_merged =
                                  contract.html_string.replace(
                                    new RegExp("PLEASE SIGN HERE"),
                                    '<img width="300px" src="' +
                                      sb.data.files.getURL(contract.signatures) +
                                      '"> - ' +
                                      contract.signer_name +
                                      " @ " +
                                      moment(contract.signed_on)
                                        .local()
                                        .format("M/DD/YYYY h:mm:ss a")
                                  );
                                contract.html_string = contract.html_string_merged;

                                sigReplacedCount++;

                                scanDocumentForSignatureLines(
                                  contract,
                                  next,
                                  sigReplacedCount
                                );
                              } else {
                                next(contract);
                                sb.notify({
                                  type: "display-alert",
                                  data: {
                                    header: "Signature(s) Added",
                                    body: refreshMessage,
                                    color: "green",
                                    displayTime: 7000,
                                  },
                                });
                              }
                            }

                            function scanDocumentForSignatureDate(contract, view) {
                              if (contract.html_string.includes("2022/")) {

                                contract.html_string_merged =
                                  contract.html_string.replace(
                                    new RegExp("2022/"),
                                    ""
                                  );
                                contract.html_string = contract.html_string_merged;

                                scanDocumentForClickToSign(contract, view);
                              } else {
                                view(contract);
                              }
                            }

                            function buildPreview(string) {
                              Editor.empty();

                              Editor.makeNode("text", "div", {
                                text: sb.dom.removeScriptTags(string),
                                id: "bentoDocumentEditorPreviewer",
                              });
                              // Patch the dom
                              Editor.patch();
                            }

                            scanDocumentForClickToSign(contract, function (con) {
                              scanDocumentForSignatureLines(con, function (contract) {
                                scanDocumentForSignatureDate(
                                  contract,
                                  function (con) {
                                    buildPreview(con.html_string);
                                  }
                                );
                              });
                            });
                          }.bind(null, obj),
                        },
                      },
                      sb.moduleId
                    );
                }
            }
          }

          // Title
          ui.header.makeNode("title", "div", {});

          // Tags
          var tagsNode = ui.header.makeNode("tags", "div", {
            style: "padding-top:5px; position: relative;",
          });

          // Divider
          ui.header.makeNode("br0", "div", {
            css: "ui clearing divider",
            style: "border:none;",
          });

          // Body
          ui.makeNode("body", "div", {
            id: "bentoDocumentBody",
          });

          // Editor
          var editorOrientationStyle =
            obj.orientation === "landscape"
              ? "min-height:" +
                landscapeHeight +
                "; max-width:" +
                landscapeWidth
              : "min-height:" + portraitHeight + "; max-width:" + portraitWidth;
          var additionalStyles = !state.noPadding ? "" : "";
          Editor = ui.body.makeNode("editor", "div", {
            id: "bentoDocumentEditorContainer",
            css: "ui padded loading round-border",
            style:
              "padding:40px !important; margin:0 auto; box-shadow: 0 15px 50px 0px rgba(0,0,0,0.1) !important;" +
              additionalStyles +
              editorOrientationStyle,
          });

          // Modals
          ui.makeNode("modals", "div", {});

          // Comments
          var commentsOrientationStyle =
            obj.orientation === "landscape"
              ? "max-width:" + landscapeWidth
              : "max-width:" + portraitWidth;
          ui.makeNode("comments", "div", {
            id: "bentoDocumentComments",
            style:
              "margin:0 auto; padding-top:60px;" + commentsOrientationStyle,
          });
          sb.notify({
            type: "show-note-list-box",
            data: {
              domObj: ui.comments,
              objectIds: [obj.id],
              objectId: obj.id,
            },
          });
          ui.patch();

          setTimeout(function () {
            // Tags
            sb.notify({
              type: "view-field",
              data: {
                type: "tags",
                property: "tagged_with",
                obj: obj,
                options: {
                  filter: false,
                  build: true,
                },
                ui: tagsNode,
              },
            });

            toggleEditMode(ui, edit);

            $("#loader").fadeOut();
          }, 1000);
        },
      },
    });
  }

  // create contract view
  function createContractView(dom, state, draw, onComplete, objSeed) {
    function nextForm() {
      var form = this.form.process().fields,
        formObj = this.form.process();

      if (formObj.completed == false) {
        sb.dom.alerts.alert(
          "Error",
          "Please fill out the entire Form",
          "error"
        );

        return;
      }

      this.btnGroup.saveBtn.loading();

      var contractObj = {
          name: form.name.value,
          contract_types: form.type.value,
          html_string: "",
          sent_on: moment().format(),
          related_object: 0,
        },
        dom = this;

      if (state.objectId) {
        contractObj.related_object = objectId;
        contractObj.parent = objectId;
      }

      if (state.layer) {
        switch (state.layer) {
          case "headquarters":
            contractObj.related_object = state.headquarters.id;
            break;

          case "team":
            contractObj.related_object = state.team.id;
            break;

          default:
            return;
        }
      }

      if (objSeed && objSeed.tagged_with) {
        contractObj.tagged_with = objSeed.tagged_with;
      }

      sb.data.db.obj.create(
        "contracts",
        contractObj,
        function (resp) {
          if (onComplete) {
            //onComplete(resp);

            state.onUpdate = onComplete;
          }

          singleView(resp, dom, state, draw, true);
        },
        2
      );
    }

    function setupContractSystem(callback) {
      sb.data.db.obj.getBlueprint(
        "contracts",
        function (blueprint) {
          if (blueprint.contract_types.options.length == 0) {
            sb.data.db.obj.create(
              "contract_types",
              { name: "Standard Contract" },
              function () {
                sb.data.db.obj.getBlueprint(
                  "contracts",
                  function (blueprint) {
                    callback(blueprint);
                  },
                  1
                );
              }
            );
          } else {
            callback(blueprint);
          }
        },
        1
      );
    }

    dom.empty();

    dom
      .makeNode("createPanel", "div", { css: "ui basic segment" })
      .makeNode("body", "div", { css: "" });

    dom.patch();

    var formArgs = {
      name: {
        name: "name",
        label: "Name",
        type: "text",
      },
      contract_types: {
        name: "type",
        label: "Type",
        type: "select",
        options: [],
      },
    };

    setupContractSystem(function (blueprint) {
      _.each(blueprint.contract_types.options, function (val, key, list) {
        formArgs.contract_types.options.push({
          name: val,
          value: key,
        });
      });

      if (!_.isArray(blueprint.contract_types.options)) {
        formArgs.contract_types.type = "hidden";
        formArgs.contract_types.value = Object.keys(
          blueprint.contract_types.options
        )[0];
      }

      dom.createPanel.body.makeNode("formCont", "div", { css: "" });

      dom.createPanel.body.formCont.makeNode("title", "div", {
        text: "Create a new Contract Template",
        css: "ui large header",
      });

      dom.createPanel.body.formCont.makeNode("form", "form", formArgs);

      dom.createPanel.body.formCont.makeNode("break1", "div", {
        text: "<br />",
      });

      dom.createPanel.body.formCont.makeNode("btnGroup", "div", {
        css: "one ui buttons",
      });
      dom.createPanel.body.formCont.btnGroup.makeNode("saveBtn", "button", {
        css: "pda-btn-green",
        text: 'Next Step <i class="fa fa-arrow-right"></i>',
      });

      dom.createPanel.body.formCont.btnGroup.saveBtn.notify(
        "click",
        {
          type: "contracts-run",
          data: {
            run: nextForm.bind(dom.createPanel.body.formCont),
          },
        },
        sb.moduleId
      );

      delete dom.createPanel.body.test;

      draw(dom);
    });
  }

  function searchForAContact(dom, state, draw, callback) {
    function performSearch(term, callback) {
      sb.data.db.obj.getWhere(
        "contacts",
        {
          fname: {
            type: "contains",
            value: term,
          },
          childObjs: 2,
        },
        function (fnameResults) {
          sb.data.db.obj.getWhere(
            "contacts",
            {
              lname: {
                type: "contains",
                value: term,
              },
              childObjs: 2,
            },
            function (lnameResults) {
              var results = fnameResults.concat(lnameResults);

              callback(
                _.uniq(results, function (contact) {
                  return contact.id;
                })
              );
            }
          );
        }
      );
    }

    dom.empty();

    dom.makeNode("title", "div", {
      text: "Search for a contact",
      css: "ui large header",
    });

    dom.makeNode("form", "div", { css: "ui huge fluid action input" });

    dom.form.makeNode("input", "div", { tag: "input", type: "text" });
    dom.form
      .makeNode("btn", "div", { text: "Search", css: "ui green button" })
      .notify(
        "click",
        {
          type: "contactComponent-run",
          data: {
            run: function (dom, state, draw) {
              dom.form.btn.loading();
              dom.results.css("ui basic loading segment");

              var term = $(dom.form.input.selector).val();

              if (term.length < 3) {
                sb.dom.alerts.alert("", "Type more to begin search.", "error");
                return;
              }

              performSearch(term, function (results) {
                dom.results.makeNode("count", "div", {
                  text: results.length + " total results",
                  css: "ui small header",
                });

                dom.results.makeNode("table", "table", {
                  clearCSS: true,
                  css: "ui stackable table",
                  columns: {
                    select: "",
                    name: "Name",
                  },
                  columnCSS: ["two wide", "fourteen wide"],
                });

                _.each(results, function (contact) {
                  var company = "";
                  if (contact.company) {
                    company = "<small> at " + contact.company.name + "</small>";
                  }

                  dom.results.table.makeRow("row-" + contact.id, [
                    "",
                    "<h2>" +
                      contact.fname +
                      " " +
                      contact.lname +
                      company +
                      "</h2>",
                  ]);

                  dom.results.table.body["row-" + contact.id].select.makeNode(
                    "select",
                    "div",
                    { text: "Select", css: "ui green button" }
                  );

                  dom.results.table.body[
                    "row-" + contact.id
                  ].select.select.notify(
                    "click",
                    {
                      type: "contactComponent-run",
                      data: {
                        run: function (dom, state, draw, contact) {
                          dom.results.table.body[
                            "row-" + contact.id
                          ].select.select.loading();

                          sb.data.db.obj.update(
                            "groups",
                            { id: state.project.id, main_contact: contact.id },
                            function (project) {
                              callback(contact);
                            },
                            3
                          );
                        }.bind({}, dom, state, draw, contact),
                      },
                    },
                    sb.moduleId
                  );
                });

                dom.results.patch();
                dom.results.css("ui basic segment");
                dom.form.btn.loading(false);
              });
            }.bind({}, dom, state, draw),
          },
        },
        sb.moduleId
      );

    dom.makeNode("formBreak", "div", { text: "<br />" });
    dom.makeNode("results", "div", { css: "ui basic segment" });

    draw(dom);
  }

  // CLIENT PORTAL
  function signContractView(dom, categories) {
    var instance = sb.data.url.getParams().i;
    var contract = sb.data.url.getParams().wid;
    var systemSettings;
    appConfig.instance = instance;
    var obj = {};
    var fullName = "";
    var contractHTML = "";

    function dataURItoBlob(dataURI) {
      // convert base64 to raw binary data held in a string
      // doesn't handle URLEncoded DataURIs - see SO answer #6850276 for code that does this
      var byteString = atob(dataURI.split(",")[1]);

      // separate out the mime component
      var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];

      // write the bytes of the string to an ArrayBuffer
      var ab = new ArrayBuffer(byteString.length);
      var ia = new Uint8Array(ab);
      for (var i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }

      //Old Code
      //write the ArrayBuffer to a blob, and you're done
      //var bb = new BlobBuilder();
      //bb.append(ab);
      //return bb.getBlob(mimeString);

      //New Code
      return new Blob([ab], { type: mimeString });
    }

    function getConsent() {
      var contractId = 0;
      if (obj.contract) {
        contractId = obj.contract.id;
      } else if (obj.object_bp_type == "contracts") {
        contractId = obj.id;
      }

      var dom = this;

      dom.makeNode("consent", "div", {
        css: "ui sixteen wide centered column",
      });
      dom.consent.makeNode("cont", "div", {
        css: "ui very padded basic segment",
      });
      dom.consent.cont.makeNode("break", "div", { text: "<br /><br />" });
      dom.consent.cont.makeNode("steps", "div", {
        css: "ui ordered four steps",
      });

      dom.consent.cont.steps
        .makeNode("step1", "div", { css: "active step" })
        .makeNode("content", "div", { css: "content" });
      dom.consent.cont.steps.step1.content.makeNode("title", "div", {
        css: "title",
        text: "Electronic Signature Disclaimer",
      });
      // dom.consent.cont.steps.step1.content.makeNode('description', 'div', {css:'description', text:'Give your permission to sign this document electronically.'})

      dom.consent.cont.steps
        .makeNode("step2", "div", { css: "disabled step" })
        .makeNode("content", "div", { css: "content" });
      dom.consent.cont.steps.step2.content.makeNode("title", "div", {
        css: "title",
        text: "Review",
      });
      // dom.consent.cont.steps.step2.content.makeNode('description', 'div', {css:'description', text:'View the contract and make sure you\'re ready to sign.'})

      dom.consent.cont.steps
        .makeNode("step3", "div", { css: "disabled step" })
        .makeNode("content", "div", { css: "content" });
      dom.consent.cont.steps.step3.content.makeNode("title", "div", {
        css: "title",
        text: "Sign",
      });
      // dom.consent.cont.steps.step3.content.makeNode('description', 'div', {css:'description', text:'Create a digital signature and sign the contract.'})

      dom.consent.cont.steps
        .makeNode("step4", "div", { css: "disabled step" })
        .makeNode("content", "div", { css: "content" });
      // dom.consent.cont.steps.step4.content.makeNode('title', 'div', {css:'title', text:'Done'})

      dom.makeNode("col", "div", { css: "ui eight wide centered column" });

      dom.col.makeNode("cont", "div", { css: "ui very padded basic segment" });

      if (obj.status == "Signed") {
        signedView(this, obj);
      } else {
        var disclaimerText = defaultDisclaimer;

        //delete dom.break;
        delete dom.consent.loader;
        delete dom.consent.loading;

        if (systemSettings) {
          if (systemSettings.signature_disclaimer) {
            disclaimerText = systemSettings.signature_disclaimer;
          }
        }

        dom.col.cont.makeNode("title", "div", {
          css: "ui header",
          text: "Electronic Signature Disclaimer",
        });
        dom.col.cont.makeNode("sub", "div", {
          css: "ui sub header",
          text: "for " + obj.name,
        });

        dom.col.cont.makeNode("break", "div", { text: "<br /><br />" });

        dom.col.cont.makeNode("disclaimer", "div", {
          text: disclaimerText,
          style: "text-align:justify;",
        });

        dom.col.cont.makeNode("form", "form", {
          name: {
            type: "text",
            name: "name",
            label: "Your Full Name",
          },
          email: {
            type: "text",
            name: "email",
            label:
              "Your Email Address (we will send a copy of the signed contract here)",
          },
        });

        dom.col.cont.makeNode("formBreak", "div", { text: "<br />" });

        dom.col.cont.makeNode("btns", "div", { css: "ui buttons" });

        dom.col.cont.btns
          .makeNode("accept", "button", {
            text: '<i class="fa fa-check"></i> Accept & Continue',
            css: "pda-btn-green",
          })
          .notify(
            "click",
            {
              type: "invoicesPay-run",
              data: {
                run: function () {
                  var formInfo = this.col.cont.form.process();

                  if (formInfo.completed == false) {
                    sb.dom.alerts.alert(
                      "Error",
                      "Please enter your name.",
                      "error"
                    );
                    return;
                  }

                  fullName = formInfo.fields.name.value;
                  email = formInfo.fields.email.value;

                  this.col.cont.btns.accept.loading();

                  obj.signer_email = email;
                  obj.signer_name = fullName;
                  delete this.col;

                  var updatedTaggedWith = _.uniq(
                    obj.tagged_with.concat(obj.main_contact.id)
                  );
                  var dom = this;

                  var updatedCon = {
                    id: obj.id,
                    signer_email: email,
                    signer_name: fullName,
                    tagged_with: updatedTaggedWith,
                  };
                  var childObjs = {
                    requires_approval: true,
                    notify_list: true,
                    name: true,
                    signer_email: true,
                    signer_name: true,
                    managers: {
                      id: true,
                      email: true,
                    },
                    tagged_with: true,
                  };
                  /*getChildObjs:4*/
                  sb.data.db.controller(
                    "updateObject&api_web_form=true&pagodaAPIKey=" + instance,
                    {
                      objectType: "contracts",
                      objectData: updatedCon,
                    },
                    function (contract) {
                      viewContract(dom, obj);
                      sb.data.db.service(
                        "ContractsPortal",
                        "sendComms",
                        { contract: obj.id, instance: instance },
                        function (response) {}
                      );
                    }
                  );
                }.bind(dom),
              },
            },
            sb.moduleId
          );

        dom.patch();
      }
    }

    function getSignature(contract) {
      var modal = this.modals.modal,
        dom = this;

      contract.related_object = obj;

      dom.modals.empty();

      dom.modals.makeNode("signature", "modal", {
        onShow: function () {
          dom.modals.signature.footer.makeNode("btns", "div", {
            css: "ui stackable buttons",
          });

          dom.modals.signature.body.makeNode("title", "div", {
            css: "ui huge header",
            text: "Create a signature",
          });

          dom.modals.signature.body.makeNode("cols", "div", {
            css: "ui stackable grid",
          });

          dom.modals.signature.body.cols
            .makeNode("col1", "div", { css: "three wide column" })
            .makeNode("cont", "container", { css: "ui basic segment" });
          dom.modals.signature.body.cols.makeNode("col2", "div", {
            css: "thirteen wide column",
          });

          dom.modals.signature.body.cols.col1.cont.makeNode(
            "btns",
            "buttonGroup",
            { css: "ui vertical buttons" }
          );

          dom.modals.signature.body.cols.col1.cont.btns
            .makeNode("printed", "div", {
              text: "Printed",
              css: "ui fluid blue button",
            })
            .notify(
              "click",
              {
                type: "invoicesPay-run",
                data: {
                  run: function (dom) {
                    this.empty();

                    this.makeNode("break", "lineBreak", {});

                    this.makeNode("helperText", "headerText", {
                      text: "Signing as: " + fullName,
                      size: "x-small",
                      css: "text-muted",
                    });
                    this.makeNode("cont", "div", { css: "ui padded segment" });
                    this.cont.makeNode("convas", "div", {
                      text: '<span style="font-family: \'Cedarville\', cursive;">&nbsp;</span><canvas id="myCanvas" width="200" height="100" style="">Your browser does not support the HTML5 canvas tag.</canvas>',
                    });
                    this.makeNode("saveBtn", "div", {});

                    this.patch();

                    var canvas = document.getElementById("myCanvas");
                    var ctx = canvas.getContext("2d");
                    var isFontLoaded = false;
                    ctx.font = "30px Cedarville";
                    ctx.fillText(fullName, 10, 50);

                    this.saveBtn
                      .makeNode("save", "button", {
                        text: 'Save and continue <i class="fa fa-arrow-right"></i>',
                        css: "pda-btn-green",
                      })
                      .notify(
                        "click",
                        {
                          type: "invoicesPay-run",
                          data: {
                            run: function (dom, contract, canvas) {
                              dom.modals.signature.body.cols.col2.saveBtn.save.loading();

                              useTextSignature.call(
                                dom.modals.signature,
                                dom,
                                contract,
                                canvas
                              );
                            }.bind(modal, dom, contract, canvas),
                          },
                        },
                        sb.moduleId
                      );

                    this.saveBtn.patch();
                  }.bind(dom.modals.signature.body.cols.col2, dom),
                },
              },
              sb.moduleId
            );

          dom.modals.signature.body.cols.col1.cont.btns
            .makeNode("signature", "div", {
              tag: "button",
              text: "Signature",
              css: "ui fluid blue button",
            })
            .notify(
              "click",
              {
                type: "invoicesPay-run",
                data: {
                  run: function (btns, footer) {
                    this.empty();

                    this.makeNode("btns", "buttonGroup", { css: "pull-right" })
                      .makeNode("clear", "button", {
                        text: '<i class="fa fa-repeat"></i> Clear signature and start over',
                        css: "pda-btnOutline-orange",
                      })
                      .notify(
                        "click",
                        {
                          type: "invoicesPay-run",
                          data: {
                            run: function () {
                              signaturePad.clear();
                            },
                          },
                        },
                        sb.moduleId
                      );

                    this.makeNode("btnsBreak", "lineBreak", {});

                    this.makeNode("sign", "container", {
                      css: "ui secondary segment",
                      text: "",
                    });
                    this.sign.makeNode("helperText", "headerText", {
                      text: "Sign below",
                      css: "text-muted text-left",
                      size: "x-small",
                    });
                    this.sign.makeNode("signingCanvas", "text", {
                      text: '<canvas id="signature-pad" class="signature-pad" width=700 height=200></canvas>',
                    });
                    this.makeNode("saveBtn", "div", {});
                    this.patch();

                    var signaturePad = new SignaturePad(
                      document.getElementById("signature-pad"),
                      {
                        backgroundColor: "rgba(255, 255, 255, 0)",
                        penColor: "rgb(0, 76, 198)",
                      }
                    );

                    this.saveBtn
                      .makeNode("save", "button", {
                        text: 'Save and continue <i class="fa fa-arrow-right"></i>',
                        css: "pda-btn-green",
                      })
                      .notify(
                        "click",
                        {
                          type: "invoicesPay-run",
                          data: {
                            run: function (dom, contract, signaturePad, btn) {
                              btn.saveBtn.save.loading();

                              if (signaturePad.isEmpty()) {
                                sb.dom.alerts.alert(
                                  "Error",
                                  "Please create a signature before continuing.",
                                  "error"
                                );
                                return;
                              }

                              saveSignature.call(
                                this,
                                dom,
                                contract,
                                signaturePad
                              );
                            }.bind(
                              dom.modals.signature,
                              dom,
                              contract,
                              signaturePad,
                              this
                            ),
                          },
                        },
                        sb.moduleId
                      );

                    this.saveBtn.patch();
                  }.bind(
                    dom.modals.signature.body.cols.col2,
                    dom.modals.signature.body.cols.col1.cont.btns,
                    dom.modals.signature.footer.btns
                  ),
                },
              },
              sb.moduleId
            );

          dom.modals.signature.body.makeNode("finalBreak", "lineBreak", {});

          dom.modals.signature.body.cols.col2.makeNode(
            "break",
            "lineBreak",
            {}
          );

          dom.modals.signature.body.cols.col2.makeNode(
            "helperText",
            "headerText",
            {
              text: "Signing as: " + fullName,
              size: "x-small",
              css: "text-muted",
            }
          );
          dom.modals.signature.body.cols.col2.makeNode("cont", "div", {
            css: "ui padded segment",
          });
          dom.modals.signature.body.cols.col2.cont.makeNode("convas", "div", {
            text: '<span style="font-family: \'Cedarville\', cursive;">&nbsp;</span><canvas id="sig-typed" width="400" height="100" style="">Your browser does not support the HTML5 canvas tag.</canvas>',
          });
          dom.modals.signature.body.cols.col2.makeNode("saveBtn", "div", {});

          dom.modals.signature.body.patch();

          var canvas = document.getElementById("sig-typed");
          var ctx = canvas.getContext("2d");
          var isFontLoaded = false;
          ctx.font = "30px Cedarville";
          ctx.fillText(fullName, 10, 50);

          dom.modals.signature.body.cols.col2.saveBtn
            .makeNode("save", "button", {
              text: 'Save and continue <i class="fa fa-arrow-right"></i>',
              css: "pda-btn-green",
            })
            .notify(
              "click",
              {
                type: "invoicesPay-run",
                data: {
                  run: function (dom, contract, canvas) {
                    dom.modals.signature.body.cols.col2.saveBtn.save.loading();

                    useTextSignature.call(this, dom, contract, canvas);
                  }.bind(dom.modals.signature, dom, contract, canvas),
                },
              },
              sb.moduleId
            );

          dom.modals.signature.body.cols.col2.saveBtn.patch();
        },
      });

      dom.modals.patch();

      dom.modals.signature.show();
    }

    function saveSignature(dom, contract, signaturePad) {
      var managerEmail;
      var signature = signaturePad.toData(),
        imageBlob = new File(
          [dataURItoBlob(signaturePad.toDataURL())],
          "signature"
        ),
        fileMetaData = {
          fileType: "png",
          fileName: "contract-signature-" + contract.id + ".png",
          objectType: "contracts",
          objectId: contract.id,
          isPublic: 0,
        },
        modal = this,
        emails = {
          signer: contract.signer_email,
        },
        args = {},
        emailBatch = [];

      if (fileMetaData.objectType === "") {
        fileMetaData.objectType = "root";
      }
      if (isNaN(fileMetaData.objectId)) {
        fileMetaData.objectId = 0;
      }

      sb.data.db.obj.getById(
        "groups",
        contract.related_object,
        function (project) {
          var userIds = [];
          var userEmails = [];
          var contractUserIds = [];
          var contractUserEmails = [];
          var projectUserIds = [];
          var projectUserEmails = [];

          if (
            contract.requires_approval === "Yes" &&
            _.isArray(contract.notify_list) &&
            !_.isEmpty(contract.notify_list)
          ) {
            contractUserIds = _.pluck(contract.notify_list, "id");
            contractUserEmails = _.pluck(contract.notify_list, "email");
          }
          if (_.isArray(project.managers) && !_.isEmpty(project.managers)) {
            projectUserIds = _.pluck(project.managers, "id");
            projectUserEmails = _.pluck(project.managers, "email");
          }

          userIds = _.union(contractUserIds, projectUserIds);
          userEmails = _.union(contractUserEmails, projectUserEmails);

          _.each(
            userEmails,
            function (userEmail) {
              emailBatch.push({
                to: userEmail,
                from: "<EMAIL>",
                subject: "Proposal Signed",
                mergevars: {
                  TITLE: "Proposal Signed",
                  BODY:
                    "Proposal for <strong>" +
                    contract.related_object.name +
                    "</strong> has been signed by <strong>" +
                    contract.signer_name +
                    "</strong>" +
                    " on <strong>" +
                    moment().format("MMMM Do, YYYY h:mm:ss A") +
                    "</strong>",
                },
              });
            },
            this
          );

          sb.data.files.upload(imageBlob, fileMetaData, function (savedFile) {
            /*getChildObjs:4*/
            sb.data.db.controller(
              "updateObject&api_webform=true&pagodaAPIKey=" + instance,
              {
                objectType: "contracts",
                objectData: {
                  id: contract.id,
                  signatures: savedFile.id,
                  getChildObjs: 1,
                },
              },
              function (contract) {
                contract.html_string_merged = contractHTML;
                contract.signatures = savedFile;

                modal.hide();

                $(dom.col.menu.pdf.btn.selector).addClass("disabled");
                $(dom.col.menu.sign.btn.selector).addClass("disabled");
                $(dom.col.menu.sign.btn.selector).html(
                  '<i class="fa fa-pencil"></i> Signing'
                );

                $(dom.col.stepCont.steps.step2.selector).addClass("completed");
                $(dom.col.stepCont.steps.step2.selector).removeClass("active");
                $(dom.col.stepCont.steps.step3.selector).removeClass(
                  "disabled"
                );
                $(dom.col.stepCont.steps.step3.selector).addClass("active");

                contract.html_string_merged =
                  contract.html_string_merged.replace(
                    new RegExp("PLEASE SIGN HERE", "g"),
                    "{{PLEASE SIGN HERE}}"
                  );

                scanDocumentForSignatureLines.call(this, dom, contract);
              }
            );
          });

          sb.comm.sendEmail(emailBatch, function (ret) {});
        },
        1
      );
    }

    function scanDocumentForSignatureLines(dom, contract, tour) {
      contract.html_string_merged = contract.html_string_merged.replace(
        new RegExp(
          '<a id="startSignature" class="ui green left floated compact button">Click To Sign</a>'
        ),
        ""
      );
      contract.html_string = contract.html_string_merged;
      contract.html_string_merged = contract.html_string_merged.replace(
        new RegExp("{{PLEASE SIGN HERE}}"),
        '<span id="signatureLine" class="text-warning signatureLine" style="font-weight:bold;">SIGN HERE</span>'
      );

      dom.col.cont.contractContainer.makeNode("contract", "div", {
        text:
          '<div class="contractBox ui basic segment" style="max-width:850px; display:block; margin:0 auto;">' +
          contract.html_string_merged +
          "</div>",
      });
      dom.col.cont.contractContainer.patch();

      if ($(".signatureLine").get(0)) {
        $(".mainContainerArea").animate(
          {
            scrollTop:
              $(".mainContainerArea").scrollTop() -
              120 +
              $("#signatureLine").offset().top -
              ($(".mainContainerArea").offset().top - 20),
          },
          {
            duration: 1000,
            specialEasing: {
              width: "linear",
              height: "easeOutBounce",
            },
            complete: function (e) {
              if (!tour) {
                var tour = {
                  id: "hello-hopscotch",
                  showNextButton: false,
                  smoothScroll: false,
                  steps: [
                    {
                      title: "Sign Here",
                      content:
                        '<button class="placeSignatureButton ui green button">PLACE SIGNATURE HERE</button>',
                      target: "signatureLine",
                      placement: "right",
                      onShow: function () {
                        $(".placeSignatureButton").on("click", function () {
                          $(".placeSignatureButton").html(
                            '<i class="fa fa-circle-o-notch fa-spin"></i> Placing Signature'
                          );

                          setTimeout(function () {
                            contract.html_string_merged =
                              contract.html_string.replace(
                                new RegExp("{{PLEASE SIGN HERE}}"),
                                '<img width="300px" src="' +
                                  sb.data.files.getURL(contract.signatures) +
                                  '"> - ' +
                                  fullName +
                                  " @ " +
                                  moment().local().format("M/DD/YYYY h:mm:ss a")
                              );
                            contract.html_string = contract.html_string_merged;

                            dom.col.cont.contractContainer.makeNode(
                              "contract",
                              "text",
                              {
                                text:
                                  '<div class="pda-container pda-Panel" style="max-width:850px; display:block; margin:0 auto;">' +
                                  contract.html_string_merged +
                                  "</div>",
                              }
                            );

                            dom.col.cont.contractContainer.patch();

                            hopscotch.endTour();

                            scanDocumentForSignatureLines(dom, contract, tour);
                          }, 1000);
                        });
                      },
                    },
                  ],
                };
              }

              hopscotch.startTour(tour);
            },
          }
        );
      } else {
        sb.data.db.controller(
          "getIPAddress&api_web_form=true&pagodaAPIKey=" + instance,
          {},
          function (ip) {
            var request = {
              contractId: contract.id,
              signer_ip: ip,
              signer_name: fullName,
              signer_email: email,
              signed_on: moment().format(),
              html_string: contract.html_string_merged,
            };

            sb.data.db.controller(
              "signContract&api_web_form=true&pagodaAPIKey=" + instance,
              request,
              function (updated) {
                var contract = updated;

                $(".mainCanvas").animate(
                  {
                    scrollTop: $(".contractBox").scrollTop(),
                  },
                  {
                    duration: 1000,
                    specialEasing: {
                      width: "linear",
                      height: "easeOutBounce",
                    },
                    complete: function (e) {
                      sb.data.db.obj.getById(
                        "groups",
                        updated.related_object.id,
                        function (project) {
                          var userIds = [];
                          var userEmails = [];
                          var contractUserIds = [];
                          var contractUserEmails = [];
                          var projectUserIds = [];
                          var projectUserEmails = [];

                          if (
                            contract.requires_approval === "Yes" &&
                            _.isArray(contract.notify_list) &&
                            !_.isEmpty(contract.notify_list)
                          ) {
                            contractUserIds = _.pluck(
                              contract.notify_list,
                              "id"
                            );
                            contractUserEmails = _.pluck(
                              contract.notify_list,
                              "email"
                            );
                          }
                          if (
                            _.isArray(project.managers) &&
                            !_.isEmpty(project.managers)
                          ) {
                            projectUserIds = _.pluck(project.managers, "id");
                            projectUserEmails = _.pluck(
                              project.managers,
                              "email"
                            );
                          }

                          userIds = _.union(contractUserIds, projectUserIds);
                          userEmails = _.union(
                            contractUserEmails,
                            projectUserEmails
                          );

                          var noteTitle =
                            "Contract " + updated.name + " has been signed.";
                          var noteBody =
                            "Contract " +
                            updated.name +
                            " has been signed by " +
                            updated.signer_name +
                            " (" +
                            updated.signer_email +
                            ")";
                          var emailObj = {
                            newThread: true,
                            to: userEmails,
                            from: appConfig.emailFrom,
                            subject: noteBody,
                            mergevars: {
                              TITLE:
                                "Contract " +
                                updated.name +
                                " has been signed.",
                              BODY:
                                noteBody +
                                '<br /><br /><a href="' +
                                sb.url +
                                "/app/" +
                                appConfig.instance +
                                "#mystuff&1=o-project-" +
                                project.id +
                                "-" +
                                project.name +
                                "&2=pt-contractTools&3=o-contracts-" +
                                updated.id +
                                "-" +
                                updated.name +
                                '">Click here to view this project</a>',
                              BUTTON: "",
                            },
                            emailtags: ["Contract Signature Email"],
                            type: "notification",
                            typeId: updated.id,
                          };

                          var notificationObj = {
                            title: noteTitle,
                            details: noteBody,
                            color: "olive",
                            icon: "signature",
                            type: "general",
                            producer: obj.id,
                            notify: userIds,
                            link: obj.id,
                            sendToCurrentUser: true,
                          };

                          sb.comm.sendEmail(emailObj, function (response) {
                            var noteBody =
                              "You signed contract " + updated.name + ".";
                            var emailObj = {
                              newThread: true,
                              to: updated.signer_email,
                              from: appConfig.emailFrom,
                              subject: noteBody,
                              mergevars: {
                                TITLE:
                                  "You signed contract " + updated.name + ".",
                                BODY:
                                  noteBody +
                                  '<br /><br /><a href="' +
                                  sb.url +
                                  "/app/contracts#?&i=" +
                                  appConfig.instance +
                                  "&wid=" +
                                  updated.id +
                                  '">Click here to get a copy.</a>',
                                BUTTON: "",
                              },
                              emailtags: ["Contract Signature Email"],
                              type: "notification",
                              typeId: updated.id,
                            };

                            sb.comm.sendEmail(emailObj, function (response) {
                              var noteObj = {
                                type_id: obj.id,
                                type: "contracts",
                                note: noteBody,
                                record_type: "log",
                                author: sb.data.cookie.get("uid"),
                                notifyUsers: [],
                              };

                              sb.data.db.controller(
                                "createNewObject&api_webform=true&pagodaAPIKey=" +
                                  appConfig.instance,
                                { objectType: "notes", objectData: noteObj },
                                function (newNote) {
                                  sb.data.db.controller(
                                    "notify",
                                    notificationObj,
                                    function (newNotification) {
                                      if (
                                        updated.after_signature == "invoices"
                                      ) {
                                        sb.dom.alerts.ask(
                                          {
                                            title: "Contract signed!",
                                            text: "You'll now be taken to the payment portal.",
                                            closeOnCancel: false,
                                            showCancelButton: false,
                                          },
                                          function (resp) {
                                            var relatedObjId = 0;

                                            if (
                                              updated.related_object.hasOwnProperty(
                                                "id"
                                              )
                                            ) {
                                              relatedObjId =
                                                updated.related_object.id;
                                            } else {
                                              relatedObjId =
                                                updated.related_object;
                                            }

                                            window.location.replace(
                                              sb.url +
                                                "/app/invoices#?&i=" +
                                                appConfig.instance +
                                                "&pid=" +
                                                relatedObjId
                                            );
                                          }
                                        );
                                      } else {
                                        signedView(dom, contract);
                                      }
                                    },
                                    sb.url + "/api/_getAdmin.php?do="
                                  );
                                },
                                sb.url + "/api/_getAdmin.php?do="
                              );
                            });
                          });
                        },
                        1
                      );
                    },
                  }
                );
              }
            );
          }
        );
      }
    }

    function signedView(dom, contract) {
      var relatedObjId = 0;

      if (contract.related_object.hasOwnProperty("id")) {
        relatedObjId = contract.related_object.id;
      } else {
        if (typeof contract.related_object === "number") {
          relatedObjId = contract.related_object;
        }
      }

      if (contract.after_signature == "invoices") {
        window.location.replace(
          sb.url +
            "/app/invoices#?&i=" +
            appConfig.instance +
            "&pid=" +
            relatedObjId
        );
      } else {
        dom.empty();

        dom.makeNode("grid", "div", {
          css: "ui sixteen wide column center centered center aligned",
        });
        dom.grid.makeNode("col", "div", { css: "ui basic segment" });

        //dom.makeNode('consent', 'div', {css:'ui sixteen wide centered column'});
        dom.grid.col.makeNode("stepCont", "div", { css: "ui basic segment" });
        dom.grid.col.stepCont.makeNode("steps", "div", {
          css: "ui ordered four steps",
        });

        dom.grid.col.stepCont.steps
          .makeNode("step1", "div", { css: "completed step" })
          .makeNode("content", "div", { css: "content" });
        dom.grid.col.stepCont.steps.step1.content.makeNode("title", "div", {
          css: "title",
          text: "Electronic Signature Disclaimer",
        });
        // dom.consent.cont.steps.step1.content.makeNode('description', 'div', {css:'description', text:'Give your permission to sign this document electronically.'})

        dom.grid.col.stepCont.steps
          .makeNode("step2", "div", { css: "completed step" })
          .makeNode("content", "div", { css: "content" });
        dom.grid.col.stepCont.steps.step2.content.makeNode("title", "div", {
          css: "title",
          text: "Review",
        });
        // dom.consent.cont.steps.step2.content.makeNode('description', 'div', {css:'description', text:'View the contract and make sure you\'re ready to sign.'})

        dom.grid.col.stepCont.steps
          .makeNode("step3", "div", { css: "completed step" })
          .makeNode("content", "div", { css: "content" });
        dom.grid.col.stepCont.steps.step3.content.makeNode("title", "div", {
          css: "title",
          text: "Sign",
        });
        // dom.consent.cont.steps.step3.content.makeNode('description', 'div', {css:'description', text:'Create a digital signature and sign the contract.'})

        dom.grid.col.stepCont.steps
          .makeNode("step4", "div", { css: "completed active step" })
          .makeNode("content", "div", { css: "content" });
        dom.grid.col.stepCont.steps.step4.content.makeNode("title", "div", {
          css: "title",
          text: "Done",
        });
        // dom.consent.cont.steps.step4.content.makeNode('description', 'div', {css:'description', text:'Download a signed copy for your records.'})

        dom.grid.col.makeNode("title", "div", {
          text: "<br /><br /><br />Signing Complete!",
          css: "ui huge header",
        });

        dom.grid.col.makeNode("download", "headerText", {
          text: "Download a copy for your records.",
          css: "text-center",
          size: "small",
        });

        dom.grid.col.makeNode("btns", "buttonGroup", { css: "" });
        dom.grid.col.btns
          .makeNode("pdf", "div", {
            text: '<i class="fa fa-download"></i> Download',
            css: "ui blue centered center aligned button",
          })
          .notify(
            "click",
            {
              type: "invoicesPay-run",
              data: {
                run: function (contract) {
                  var dom = this;
                  var contactId = 0;
                  if (contract.hasOwnProperty("main_contact")) {
                    if (contract.main_contact) {
                      if (contract.main_contact.hasOwnProperty("id")) {
                        if (contract.main_contact.id) {
                          contract.main_contact.id;
                        } else {
                          contactId = contract.main_contact;
                        }
                      }
                    }
                  }

                  dom.grid.col.btns.pdf.loading();

                  sb.data.db.controller(
                    "getObjectById&api_webform=true&pagodaAPIKey=" + instance,
                    { value: contract.id, type: "contracts" },
                    function (obj) {
                      sb.data.db.controller(
                        "getObjectById&api_webform=true&pagodaAPIKey=" +
                          instance,
                        { value: contactId, type: "contacts", childObjs: 2 },
                        function (contact) {
                          contract.main_contact = contact;

                          createMergedHTML(contract, function (merged) {
                            sb.data.makePDF(merged, "D");

                            dom.grid.col.btns.pdf.loading(false);
                          });
                        }
                      );
                    }
                  );
                }.bind(dom, contract),
              },
            },
            sb.moduleId
          );

        dom.patch();
      }
    }

    function useTextSignature(dom, contract, canvas) {
      var modal = this;
      var imageData = $(Canvas2Image.convertToPNG(canvas)).attr("src");
      var imageBlob = new File([dataURItoBlob(imageData)], "signature");
      var fileMetaData = {
        fileType: "png",
        fileName: "contract-signature-" + contract.id + ".png",
        objectType: "contracts",
        objectId: contract.id,
        parent: 0,
        isPublic: 0,
      };

      if (isNaN(fileMetaData.objectId)) {
        fileMetaData.objectId = 0;
      }

      appConfig.instance = contract.instance;

      sb.data.files.upload(imageBlob, fileMetaData, function (savedFile) {
        /*getChildObjs:4*/
        sb.data.db.controller(
          "updateObject&pagodaAPIKey=" + appConfig.instance,
          {
            objectType: "contracts",
            objectData: {
              id: contract.id,
              signatures: savedFile.id,
              getChildObjs: 1,
            },
          },
          function (contract) {
            contract.html_string_merged = contractHTML;
            contract.signatures = savedFile;

            modal.hide();

            $(dom.col.menu.pdf.btn.selector).addClass("disabled");
            $(dom.col.menu.sign.btn.selector).addClass("disabled");
            $(dom.col.menu.sign.btn.selector).html(
              '<i class="fa fa-pencil"></i> Signing'
            );

            $(dom.col.stepCont.steps.step2.selector).addClass("completed");
            $(dom.col.stepCont.steps.step2.selector).removeClass("active");
            $(dom.col.stepCont.steps.step3.selector).removeClass("disabled");
            $(dom.col.stepCont.steps.step3.selector).addClass("active");

            contract.html_string_merged = contract.html_string_merged.replace(
              new RegExp("PLEASE SIGN HERE", "g"),
              "{{PLEASE SIGN HERE}}"
            );

            scanDocumentForSignatureLines.call(this, dom, contract);
          },
          sb.url + "/api/_getAdmin.php?do="
        );
      });
    }

    function viewContract(dom, obj) {
      var contract = obj;
      contract.status = "Signing In Progress";

      createMergedHTML(
        obj,
        function (merged) {
          contractHTML = merged;

          if (obj.status === "Signed") {
            signedView(dom, contract);
          } else {
            /*getChildObjs:4*/
            sb.data.db.controller(
              "updateObject&api_webform=true&pagodaAPIKey=" + instance,
              {
                objectType: "contracts",
                objectData: contract,
                getChildObjs: 1,
              },
              function (contract) {
                contract.html_string_merged = merged;

                dom.empty();

                dom.makeNode("col", "div", {
                  css: "ui sixteen wide column mainContainerArea",
                });

                dom.col.makeNode("menu", "div", {
                  css: "ui inverted top fixed menu",
                  style: "margin-bottom:0px !important;",
                });
                dom.col.menu.makeNode("sign", "div", { css: "ui item" });
                dom.col.menu.sign
                  .makeNode("btn", "div", {
                    css: "ui green button",
                    text: '<i class="fa fa-pencil"></i> Ready To Sign',
                  })
                  .notify(
                    "click",
                    {
                      type: "invoicesPay-run",
                      data: {
                        run: getSignature.bind(dom, contract),
                      },
                    },
                    sb.moduleId
                  );
                dom.col.menu.makeNode("pdf", "div", { css: "ui item" });
                dom.col.menu.pdf
                  .makeNode("btn", "div", {
                    css: "ui blue button",
                    text: '<i class="fa fa-download"></i> Download PDF',
                  })
                  .notify(
                    "click",
                    {
                      type: "invoicesPay-run",
                      data: {
                        run: function (dom, contract) {
                          dom.col.menu.pdf.btn.loading();

                          createMergedHTML(
                            contract,
                            function (merged) {
                              sb.data.makePDF(
                                merged.replace(/\{(.+?)\}/g, ""),
                                "D"
                              );

                              dom.col.menu.pdf.btn.loading(false);
                            },
                            null,
                            null,
                            true
                          );
                        }.bind({}, dom, contract),
                      },
                    },
                    sb.moduleId
                  );

                dom.col.makeNode("consent", "div", {
                  css: "ui basic padded clearing segment",
                });
                dom.col.makeNode("stepCont", "div", {
                  css: "ui basic segment",
                });
                dom.col.stepCont.makeNode("steps", "div", {
                  css: "ui ordered four steps",
                });

                dom.col.stepCont.steps
                  .makeNode("step1", "div", { css: "completed step" })
                  .makeNode("content", "div", { css: "content" });
                dom.col.stepCont.steps.step1.content.makeNode("title", "div", {
                  css: "title",
                  text: "Electronic Signature Disclaimer",
                });
                // dom.consent.cont.steps.step1.content.makeNode('description', 'div', {css:'description', text:'Give your permission to sign this document electronically.'})

                dom.col.stepCont.steps
                  .makeNode("step2", "div", { css: "active step" })
                  .makeNode("content", "div", { css: "content" });
                dom.col.stepCont.steps.step2.content.makeNode("title", "div", {
                  css: "title",
                  text: "Review",
                });
                // dom.consent.cont.steps.step2.content.makeNode('description', 'div', {css:'description', text:'View the contract and make sure you\'re ready to sign.'})

                dom.col.stepCont.steps
                  .makeNode("step3", "div", { css: "disabled step" })
                  .makeNode("content", "div", { css: "content" });
                dom.col.stepCont.steps.step3.content.makeNode("title", "div", {
                  css: "title",
                  text: "Sign",
                });
                // dom.consent.cont.steps.step3.content.makeNode('description', 'div', {css:'description', text:'Create a digital signature and sign the contract.'})

                dom.col.stepCont.steps
                  .makeNode("step4", "div", { css: "disabled step" })
                  .makeNode("content", "div", { css: "content" });
                dom.col.stepCont.steps.step4.content.makeNode("title", "div", {
                  css: "title",
                  text: "Done",
                });
                // dom.consent.cont.steps.step4.content.makeNode('description', 'div', {css:'description', text:'Download a signed copy for your records.'})

                dom.col.makeNode("cont", "div", {
                  css: "ui placeholder segment",
                  style: "margin-top:0px !important;",
                });

                dom.col.cont
                  .makeNode("contractContainer", "div", { css: "" })
                  .makeNode("contract", "div", {
                    id: "bentoDocumentEditorPreviewer",
                    css: "ui very padded segment",
                    text: merged.replace(/\{(.+?)\}/g, ""),
                    style:
                      "max-width:850px; min-height:900px; display:block; margin:3em auto;",
                  });

                dom
                  .makeNode("modals", "div", {})
                  .makeNode("modal", "modal", { size: "large" });

                dom.patch();

                $("#startSignature").on("click", function () {
                  $("#startSignature").addClass("loading");

                  getSignature.call(dom, contract);
                });
              },
              sb.url + "/api/_getAdmin.php?do="
            );
          }
        },
        "noButton"
      );
    }

    $("html,body").css("overflow", "initial");

    sb.data.db.setAPIPath("../../api/_getAdmin.php");

    sb.notify({
      type: "get-instance-data",
      data: {
        onComplete: function () {
          sb.data.db.service(
            "ContractsPortal",
            "initializeInstanceSetup",
            {
              instance: instance,
              contract: +contract,
            },
            function (response) {
              appConfig = response.instance;
              obj = response.contract;
              systemSettings = response.system_settings;
              getConsent.call(dom);
            }
          );
        },
        toolRegistration: false,
      },
    });
  }

  // DOCUMENT PORTAL
  function documentPortalView(dom) {
    var instance = sb.data.url.getParams().i;
    var contract = sb.data.url.getParams().wid;
    appConfig.instance = instance;
    var obj = {};

    function viewContract(dom, obj) {
      var contract = obj;

      if (contract.is_public !== true) {
        dom.makeNode("msg", "div", {
          text:
            '<div class="ui huge icon message">' +
            '<i class="warning icon"></i>' +
            '<div class="content">' +
            '<div class="header">This document is private</div>' +
            "<p>Contact the document owner about making this public.</p>" +
            "</div>" +
            "</div>",
        });
        dom.patch();
      } else {
        $("body").append(
          '<div id="loader" class="ui active dimmer inverted">' +
            '<div class="ui text loader">Loading</div>' +
            "</div>"
        );

        sb.data.db.obj.getById(
          "",
          obj.related_object.id,
          function (related) {
            obj.related_object = related;

            sb.data.db.obj.getWhere(
              "company_logo",
              {
                is_primary: "yes",
                select: {
                  company_logo: true,
                },
              },
              function (logos) {
                createMergedHTML(
                  obj,
                  function (merged) {
                    var companyLogo = logos[0];
                    var companyLogoLink = "";
                    if (companyLogo && companyLogo.company_logo) {
                      companyLogoLink = sb.data.files.getURL(
                        companyLogo.company_logo
                      );
                    }

                    contractHTML = merged;
                    contract.html_string_merged = merged;

                    dom.empty();

                    // Draw the page header
                    dom.makeNode("h", "div", {
                      text:
                        '<div id="topNav" class="sixteen wide column" style="height:55px; border-bottom:1px solid #ebebeb; margin-bottom:30px;">' +
                        '<div class="ui stackable grid" style="padding:0px 14px 0 14px;">' +
                        '<div class="two wide column" style="margin:0; padding-top:0; padding-bottom:0; padding-left:8px !important; padding-right:8px !important; width:100px !important; text-align:center;">' +
                        '<div style="display:inline-block; height:100%; vertical-align:middle;"></div>' +
                        '<div style="display:inline-block;">' +
                        '<img id="navLogo" class="ui centered image" style="display:inline-block !important;" src="' +
                        companyLogoLink +
                        '">' +
                        "</div>" +
                        "</div>" +
                        '<div style="padding-top: 0; padding-bottom: 0; display: flex; flex-direction: column; flex: 1 1 auto;">' +
                        '<div class="ui breadcrumb" style="width:100%; display:block; min-height:55px; max-height:55px; padding:10px; padding-left:0;">' +
                        '<div class="section disabled link">' +
                        appConfig.systemName +
                        "</div>" +
                        '<div class="divider"><i class="ui chevron right icon" style="margin:0 !important;"></i></div>' +
                        '<div class="active section disabled link">' +
                        contract.name +
                        "</div>" +
                        "</div>" +
                        "</div>" +
                        "</div>" +
                        "</div>",
                    });

                    var style =
                      "margin:0 auto; padding:40px !important; box-shadow:0 15px 50px 0px rgb(0 0 0 / 10%) !important;";
                    if (contract.orientation === "landscape") {
                      style = style + "max-width:1253px;";
                    } else {
                      style = style + "max-width:830px;";
                    }

                    // Draw the document
                    dom
                      .makeNode("contractContainer", "div", {
                        style: "padding-bottom:60px;",
                      })
                      .makeNode("contract", "div", {
                        id: "bentoDocumentEditorPreviewer",
                        css: "round-border",
                        text: merged.replace(/\{(.+?)\}/g, ""),
                        style: style,
                      });

                    dom.patch();

                    $("#loader").fadeOut();
                  },
                  "noButton"
                );
              }
            );
          },
          1,
          true
        );
      }
    }

    $("html,body").css("overflow", "initial");

    sb.data.db.setAPIPath("../../api/_getAdmin.php");

    sb.notify({
      type: "get-instance-data",
      data: {
        onComplete: function () {
          sb.data.db.controller(
            "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
            {
              queryObj: {
                instance: instance,
              },
              instance: instance,
              objectType: "instances",
            },
            function (instanceObj) {
              sb.data.db.controller(
                "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
                {
                  queryObj: {
                    id: +contract,
                  },
                  objectType: "contracts",
                  getChildObjs: 1,
                },
                function (objs) {
                  appConfig = instanceObj[0];
                  obj = objs[0];

                  // getConsent.call(dom);
                  viewContract(dom, obj);
                  //getSignature.call(dom, obj);
                },
                sb.url + "/api/_getAdmin.php?do="
              );
            },
            sb.url + "/api/_getAdmin.php?do="
          );
        },
      },
    });
  }

  // Register some basic merge tags
  function registerMergeTags() {
    function formatPhoneNo(unformatted) {
      var formatted = "";

      if (unformatted) {
        ///removes any whitespace chars
        unformatted = unformatted.replace(/\s/g, "");
        unformatted = unformatted.replace(/\D/g, "");

        formatted = "(" + unformatted.slice(0, 3) + ")";
        formatted += " " + unformatted.slice(3, 6);
        formatted += "-" + unformatted.slice(6);
      }

      return formatted;
    }

    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "pagebreak",
        tag: "Page Break",
        data: function (obj, callback) {
          callback("-----------------------------------");
        },
        parse: function (list) {
          return "<pagebreak>";
        },
      },
    });

    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "today",
        tag: "Today",
        data: function (obj, callback) {
          callback();
        },
        parse: function (data, state, opts) {
          if (
            opts &&
            opts.mergeVars &&
            opts.mergeVars.today &&
            moment.isMoment(opts.mergeVars.today)
          ) {
            return opts.mergeVars.today.clone().local().format("MM/DD/YY");
          }
          return moment().local().format("MM/DD/YY");
        },
      },
    });

    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "now",
        tag: "Now",
        data: function (obj, callback) {
          callback();
        },
        parse: function (list) {
          return moment().local().format("MM/DD/YY, h:mm a");
        },
      },
    });

    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "seven_days_from_today",
        tag: "Seven Days From Today",
        data: function (obj, callback) {
          callback();
        },
        parse: function (list) {
          return moment().local().add(7, "days").format("MM/DD/YY");
        },
      },
    });

    // This year
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "this_year",
        tag: "This Year",
        data: function (obj, callback) {
          callback();
        },
        parse: function (list) {
          return moment().local().format("YYYY");
        },
      },
    });
    // This week
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "this_week",
        tag: "This Week",
        data: function (obj, callback) {
          callback();
        },
        parse: function (list) {
          return "Week of " + moment().local().startOf("week").format("M/D/YY");
        },
      },
    });

    // Last year
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "last_year",
        tag: "Last Year",
        data: function (obj, callback) {
          callback();
        },
        parse: function (list) {
          return moment().subtract(1, "year").local().format("YYYY");
        },
      },
    });

    ///7 Days After Event
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "seven_days_after_event",
        tag: "Seven Days After Event",
        data: function (obj, callback) {
          callback(obj.related_object);
        },
        parse: function (project) {
          var mergeText = "";

          if (project.end_date != null && project.end_date != false)
            mergeText = moment(project.end_date)
              .local()
              .add(7, "days")
              .format("MM/DD/YY");

          return mergeText;
        },
      },
    });

    ///15 Days After Event
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "fifteen_days_after_event",
        tag: "Fifteen Days After Event",
        data: function (obj, callback) {
          callback(obj.related_object);
        },
        parse: function (project) {
          var mergeText = "";

          if (project.end_date != null && project.end_date != false)
            mergeText = moment(project.end_date)
              .local()
              .add(15, "days")
              .format("MM/DD/YY");

          return mergeText;
        },
      },
    });

    ///30 Days After Event
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "thirty_days_after_event",
        tag: "Thirty Days After Event",
        data: function (obj, callback) {
          callback(obj.related_object);
        },
        parse: function (project) {
          var mergeText = "";

          if (project.end_date != null && project.end_date != false)
            mergeText = moment(project.end_date)
              .local()
              .add(30, "days")
              .format("MM/DD/YY");

          return mergeText;
        },
      },
    });

    ///10 Days Before Event
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "ten_days_before_event",
        tag: "Ten Days Before Event",
        data: function (obj, callback) {
          callback(obj.related_object);
        },
        parse: function (project) {
          var mergeText = "";

          if (project.start_date != null && project.start_date != false)
            mergeText = moment(project.start_date)
              .local()
              .subtract(10, "days")
              .format("MM/DD/YY");

          return mergeText;
        },
      },
    });

    ///90 Days Before Event
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "ninety_days_before_event",
        tag: "Ninety Days Before Event",
        data: function (obj, callback) {
          callback(obj.related_object);
        },
        parse: function (project) {
          var mergeText = "";

          if (project.start_date != null && project.start_date != false)
            mergeText = moment(project.start_date)
              .local()
              .subtract(90, "days")
              .format("MM/DD/YY");

          return mergeText;
        },
      },
    });

    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "ten_days_from_today",
        tag: "Ten Days From Today",
        data: function (obj, callback) {
          callback();
        },
        parse: function (list) {
          return moment().local().add(10, "days").format("MM/DD/YY");
        },
      },
    });

    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "thirty_days_from_today",
        tag: "Thirty Days From Today",
        data: function (obj, callback) {
          callback();
        },
        parse: function (list) {
          return moment().local().add(30, "days").format("MM/DD/YY");
        },
      },
    });

    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "venue_address",
        tag: "Venue Address",
        data: function (obj, callback) {
          callback(obj.related_object);
        },
        parse: function (proj) {
          var ret = "";

          if (proj.locations && proj.locations[0]) {
            ret =
              proj.locations[0].street +
              "<br />" +
              proj.locations[0].city +
              " " +
              proj.locations[0].state +
              ", " +
              proj.locations[0].zip +
              " " +
              proj.locations[0].country;
          }

          return ret;
        },
      },
    });

    // fix for tags added to client templates
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "instance_address_fix",
        tag: "Voltz Software Address",
        data: function (obj, callback) {
          if (CachedMergeTags.hasOwnProperty("instance_address_fix")) {
            callback(CachedMergeTags["instance_address_fix"]);
          } else {
            sb.data.db.obj.getAll(
              "invoice_system",
              function (config) {
                CachedMergeTags["instance_address_fix"] =
                  config[0].billing_address;
                callback(config[0].billing_address);
              },
              {
                billing_address: true,
              }
            );
          }
        },
        parse: function (info) {
          var ret = "";

          if (info) {
            ret =
              info.street +
              "<br />" +
              info.city +
              " " +
              info.state +
              ", " +
              info.zip +
              " " +
              info.country;
          }

          return ret;
        },
      },
    });

    // actual company address tag
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "instance_address",
        tag: appConfig.systemName + " Address",
        data: function (obj, callback) {
          if (CachedMergeTags.hasOwnProperty("instance_address")) {
            callback(CachedMergeTags["instance_address"]);
          } else {
            sb.data.db.obj.getAll(
              "invoice_system",
              function (config) {
                CachedMergeTags["instance_address"] = config[0].billing_address;
                callback(config[0].billing_address);
              },
              {
                billing_address: true,
              }
            );
          }
        },
        parse: function (info) {
          var ret = "";

          if (info) {
            ret =
              info.street +
              "<br />" +
              info.city +
              " " +
              info.state +
              ", " +
              info.zip +
              " " +
              info.country;
          }

          return ret;
        },
      },
    });

    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "project_uid",
        tag: "PROJECT.NO",
        data: function (obj, callback) {
          callback(obj.related_object);
        },
        parse: function (obj) {
          return obj.object_uid;
        },
      },
    });

    // {{PROPOSAL.LOCATIONS.NAME}}
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "proposal.locations.name",
        tag: "PROPOSAL.LOCATIONS.NAME",
        data: function (obj, callback) {
          callback(obj.related_object);
        },
        parse: function (obj) {
          // Set variables
          var locations = obj.locations;
          var locationsHTML = "";
          var count = 0;

          // Loop through locations
          _.each(locations, function (location) {
            var comma = "";
            if (count > 0) {
              comma = ", ";
            }

            // Set variables
            var name = location.name ? location.name : "";

            // Create HTML
            //locationsHTML += "<div class=\"ui small header\" style=\"margin-top:0; margin-bottom:0;\">" + name + "</div>";
            locationsHTML +=
              '<span style="font-weight:bold;">' + comma + name + "</span>";

            count++;
          });

          // Return HTML
          return locationsHTML;
        },
      },
    });

    // {{PROPOSAL.LOCATIONS.DESCRIPTION}}
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "proposal.locations.description",
        tag: "PROPOSAL.LOCATIONS.DESCRIPTION",
        data: function (obj, callback) {
          callback(obj.related_object);
        },
        parse: function (obj) {
          // Set variables
          var locations = obj.locations;
          var locationsHTML = "";

          // Loop through locations
          _.each(locations, function (location) {
            // Set variables
            var name = location.name ? location.name : "";
            var description = location.description ? location.description : "";

            // Create HTML
            //locationsHTML += "<div class=\"ui small header\" style=\"margin-top:0; margin-bottom:0;\">" + name + "</div>";
            locationsHTML +=
              '<p><span style="font-weight:bold;">' +
              name +
              "</span><br />" +
              description +
              "</p>";
          });

          // Return HTML
          return locationsHTML;
        },
      },
    });

    // Main contact info merge tags
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "main_contact_email",
        tag: "Main contact email",
        data: function (obj, callback) {
          var info = [];
          if (obj.main_contact) {
            info = _.where(obj.main_contact.contact_info, {
              is_primary: "yes",
            });
          }
          var ids = _.pluck(info, "type");

          if (_.isEmpty(ids)) {
            callback(false);
          } else {
            if (CachedMergeTags.hasOwnProperty("main_contact_email")) {
              callback(CachedMergeTags["main_contact_email"]);
            } else {
              sb.data.db.obj.getById(
                "contact_info_types",
                ids,
                function (types) {
                  CachedMergeTags["main_contact_email"] = {
                    info: info,
                    types: types,
                  };

                  callback({
                    info: info,
                    types: types,
                  });
                },
                {
                  data_type: true,
                }
              );
            }
          }
        },
        parse: function (info) {
          var ret = "";
          var e = _.find(info.info, function (i) {
            var type = _.findWhere(info.types, { id: i.type });
            if (type.data_type === "email") {
              ret = i.info;
            }
          });

          return ret;
        },
      },
    });
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "main_contact_phone",
        tag: "Main contact phone",
        data: function (obj, callback) {
          if (obj.main_contact) {
            if (CachedMergeTags.hasOwnProperty("contact_info")) {
              callback(CachedMergeTags["contact_info"]);
            } else {
              sb.data.db.obj.getWhere(
                "contact_info",
                {
                  object_id: obj.main_contact.id,
                  childObjs: {
                    object_id: true,
                    info: true,
                    street: true,
                    city: true,
                    state: true,
                    zip: true,
                    country: true,
                    type: {
                      data_type: true,
                    },
                    is_primary: true,
                  },
                },
                function (info) {
                  CachedMergeTags["contact_info"] = info;
                  callback(CachedMergeTags["contact_info"]);
                }
              );
            }
          } else {
            callback([]);
          }
        },
        parse: function (info) {
          var ret = "";
          var e = _.find(info.info, function (i) {
            if (i.type.data_type === "phone") {
              ret = i.info;
            }
          });

          return formatPhoneNo(ret);
        },
      },
    });
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "main_contact_address",
        tag: "Main contact address",
        data: function (obj, callback) {
          if (obj.main_contact && obj.main_contact.company) {
            if (CachedMergeTags.hasOwnProperty("contact_info")) {
              callback(CachedMergeTags["contact_info"]);
            } else {
              sb.data.db.obj.getWhere(
                "contact_info",
                {
                  object_id: obj.main_contact.id,
                  childObjs: {
                    object_id: true,
                    info: true,
                    street: true,
                    city: true,
                    state: true,
                    zip: true,
                    country: true,
                    type: {
                      data_type: true,
                    },
                    is_primary: true,
                  },
                },
                function (info) {
                  CachedMergeTags["contact_info"] = info;
                  callback(info);
                }
              );
            }
          } else {
            callback(false);
          }
        },
        parse: function (info) {
          var ret = "";
          _.each(info, function (i) {
            if (i.type) {
              if (i.type.data_type === "address") {
                ret =
                  i.street +
                  "<br />" +
                  i.city +
                  " " +
                  i.state +
                  ", " +
                  i.zip +
                  " " +
                  i.country;
              }
            }
          });

          return ret;
        },
      },
    });

    // Client contact info merge tags
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "client_email",
        tag: "Client email",
        data: function (obj, callback) {
          if (obj.main_contact && obj.main_contact.company) {
            if (CachedMergeTags.hasOwnProperty("contact_info")) {
              callback(CachedMergeTags["contact_info"]);
            } else {
              sb.data.db.obj.getWhere(
                "contact_info",
                {
                  object_id: obj.main_contact.id,
                  childObjs: {
                    object_id: true,
                    info: true,
                    street: true,
                    city: true,
                    state: true,
                    zip: true,
                    country: true,
                    type: {
                      data_type: true,
                    },
                    is_primary: true,
                  },
                },
                function (info) {
                  CachedMergeTags["contact_info"] = info;
                  callback(info);
                }
              );
            }
          } else {
            callback(false);
          }
        },
        parse: function (info) {
          var ret = "";
          _.each(info, function (i) {
            if (i.type) {
              if (i.type.data_type === "email") {
                ret = i.info;
              }
            }
          });

          return ret;
        },
      },
    });
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "client_phone",
        tag: "Client phone",
        data: function (obj, callback) {
          if (obj.main_contact && obj.main_contact.company) {
            if (CachedMergeTags.hasOwnProperty("contact_info")) {
              callback(CachedMergeTags["contact_info"]);
            } else {
              sb.data.db.obj.getWhere(
                "contact_info",
                {
                  object_id: obj.main_contact.id,
                  childObjs: {
                    object_id: true,
                    info: true,
                    street: true,
                    city: true,
                    state: true,
                    zip: true,
                    country: true,
                    type: {
                      data_type: true,
                    },
                    is_primary: true,
                  },
                },
                function (info) {
                  CachedMergeTags["contact_info"] = info;
                  callback(info);
                }
              );
            }
          } else {
            callback(false);
          }
        },
        parse: function (info) {
          var ret = "";
          _.each(info, function (i) {
            if (i.type) {
              if (i.type.data_type === "phone") {
                ret = i.info;
              }
            }
          });

          return formatPhoneNo(ret);
        },
      },
    });
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "client_address",
        tag: "Client address",
        data: function (obj, callback) {
          if (obj.main_contact && obj.main_contact.company) {
            if (CachedMergeTags.hasOwnProperty("contact_info")) {
              callback(CachedMergeTags["contact_info"]);
            } else {
              sb.data.db.obj.getWhere(
                "contact_info",
                {
                  object_id: obj.main_contact.id,
                  childObjs: {
                    object_id: true,
                    info: true,
                    street: true,
                    city: true,
                    state: true,
                    zip: true,
                    country: true,
                    type: {
                      data_type: true,
                    },
                    is_primary: true,
                  },
                },
                function (info) {
                  CachedMergeTags["contact_info"] = info;
                  callback(info);
                }
              );
            }
          } else {
            callback(false);
          }
        },
        parse: function (info) {
          var ret = "";
          _.each(info, function (i) {
            if (i.type) {
              if (i.type.data_type === "address") {
                ret =
                  i.street +
                  "<br />" +
                  i.city +
                  " " +
                  i.state +
                  ", " +
                  i.zip +
                  " " +
                  i.country;
              }
            }
          });

          return ret;
        },
      },
    });
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "project_owner",
        tag: "Project owner",
        data: function (obj, callback) {
          callback(obj.related_object);
        },
        parse: function (proj) {
          var ret = "";

          if (!_.isEmpty(proj.owner)) {
            ret = proj.owner.name;
          }

          return ret;
        },
      },
    });

    // DATE_BOOKED merge tag
    sb.notify({
      type: "register-merge-tag",
      data: {
        name: "date_booked",
        tag: "DATE_BOOKED",
        data: function (obj, callback) {
          console.log('date_booked merge tag:::', arguments);

          function checkProjectForDateBooked(project, callback) {
            // Check if project is valid and is a Project type
            if (!project) {
              callback("");
              return;
            }

            // If project is just an ID, fetch the full object
            if (typeof project === 'number' || (typeof project === 'string' && !isNaN(project))) {
              sb.data.db.obj.getById(
                "groups",
                project,
                function (data) {
                  // Verify it's a Project and has date_booked
                  if (data && data.object_bp_type === 'groups' && data.group_type === 'Project') {
                    callback(data.date_booked || "");
                  } else {
                    callback("");
                  }
                },
                {
                  date_booked: true,
                  object_bp_type: true,
                  group_type: true
                }
              );
            }
            // If project is an object
            else if (typeof project === 'object' && project.object_bp_type === 'groups' && project.group_type === 'Project') {
              // Check if it has date_booked already
              if (project.date_booked !== null && project.date_booked !== undefined) {
                callback(project.date_booked);
              } else {
                // Fetch the date_booked field specifically
                sb.data.db.obj.getById(
                  "groups",
                  project.id,
                  function (data) {
                    callback(data.date_booked || "");
                  },
                  {
                    date_booked: true
                  }
                );
              }
            } else {
              callback("");
            }
          }

          // First check obj.parent
          if (obj && obj.parent) {
            checkProjectForDateBooked(obj.parent, function(result) {
              if (result) {
                callback(result);
              } else {
                // Fallback to obj.related_object
                if (obj.related_object) {
                  checkProjectForDateBooked(obj.related_object, callback);
                } else {
                  callback("");
                }
              }
            });
          }
          // If no parent, check obj.related_object
          else if (obj && obj.related_object) {
            checkProjectForDateBooked(obj.related_object, callback);
          } else {
            callback("");
          }
        },
        parse: function (dateString, obj) {
          if (!dateString || dateString === null || dateString === undefined) return "";
          return moment(dateString).local().format('MM/DD/YY h:mm a');
        }
      }
    });
  }

  function build_toolCollections(dom, state, draw) {
    //!! Docs collection
    var collectionsSetup = {
      domObj: dom,
      layer: "hq",
      state: state,
      objectType: "contracts",
      singleView: {
        view: function (ui, obj, draw) {
          // !TODO - Needs to update table if edits occur in single view
          singleView(obj, ui, state, draw, false);
        },
      },
      actions: {
        view: true,
        create: function (ui, obj_info, onComplete) {
          createWorkflow(ui, state, obj_info, onComplete);
        },
      },
      fields: {
        name: {
          title: "Name",
        },
        merge_type: {
          title: "Type",
          view: function (dom, obj) {
            var typeString = "<i>No Type Selected</i>";
            if (obj.merge_type) {
              typeString = obj.merge_type.toUpperCase();
            }
            dom.makeNode("type", "div", { text: typeString });
          },
        },
        requires_approval: {
          title: "Requires Approval",
          view: function (dom, obj) {
            var typeString = "No";
            if (obj.requires_approval == "Yes") {
              typeString = "Yes";
            }
            dom.makeNode("type", "div", { text: typeString });
          },
        },
        after_signature: {
          title: "After Signature",
          view: function (dom, obj) {
            var typeString = "Do Nothing";
            if (obj.after_signature == "invoices") {
              typeString = "Send to invoice portal";
            }
            dom.makeNode("type", "div", { text: typeString });
          },
        },
        sent_on: {
          title: "Sent On",
          view: function (dom, obj) {
            var sentOn = '<div style="text-align: center"> - </div>';

            if (obj.sent_on !== "") {
              sentOn = moment(obj.sent_on).local().format("M/D/YYYY h:mm a");
            }

            dom.makeNode("sent_on", "div", { text: sentOn });
          },
        },
        date_created: {
          title: "Date Created",
          view: function (dom, obj) {
            dom.makeNode("date_created", "div", {
              text: moment(obj.date_created).local().format("M/D/YYYY h:mm a"),
            });
          },
        },
        last_updated: {
          title: "Last Updated",
          view: function (dom, obj) {
            dom.makeNode("last_updated", "div", {
              text: moment(obj.last_updated).local().format("M/D/YYYY h:mm a"),
            });
          },
        },
      },
      groupings: {
        type: "merge_type",
      },
      where: {
        active: {
          type: "not_equal",
          value: "No",
        },
        childObjs: {
          name: true,
          is_deleted: true,
          merge_type: true,
          status: true,
          after_signature: true,
          date_created: true,
          sent_on: true,
          requires_approval: true,
          last_updated: {
            fname: true,
            lname: true,
          },
          signer_name: true,
          signer_ip: true,
          signer_email: true,
          signed_on: true,
        },
      },
    };

    // !! Collection setup
    if (state.hasOwnProperty("layer")) {
      // Set layer
      collectionsSetup.layer = state.layer;

      // Set tags
      if (state.layer !== "hq") {
        collectionsSetup.where.tagged_with = [state.id];
        if (state.layer === "myStuff") {
          collectionsSetup.where.tagged_with = [state.where.tagged_with[0]];
        } else if (state.entity) {
          collectionsSetup.where.tagged_with = [state.entity];
        }

        if (state.hasOwnProperty("parent")) {
          if (state.parent.hasOwnProperty("object_bp_type")) {
            if (state.parent.object_bp_type === "contact_types") {
              delete collectionsSetup.where.tagged_with;
              collectionsSetup.where.shared_with = [state.entity];
            }
          }
        } else if (state.layer === "object" && state.object) {
          delete collectionsSetup.where.tagged_with;
          collectionsSetup.where.shared_with = [state.object.id];
        }

        if (state.layer === "project") {
          collectionsSetup.fields.status = {
            title: "Status",
            view: function (dom, obj) {
              var statusString;
              switch (obj.status) {
                case "Approved":
                  statusString =
                    '<div class="ui green label">' + obj.status + "</div>";

                  break;

                case "Declined":
                  statusString =
                    '<div class="ui red label">' + obj.status + "</div>";

                  break;

                case "Approval Requested":
                  statusString =
                    '<div class="ui blue label">' + obj.status + "</div>";

                  break;

                case "Signed":
                  statusString =
                    '<div class="ui green label">' + obj.status + "</div>";

                  break;

                case "Signing In Progress":
                  statusString =
                    '<div class="ui orange label"><nobr>' +
                    obj.status +
                    "</nobr></div>";

                  break;

                case "Out For Signature":
                  statusString =
                    '<div class="ui yellow label"><nobr>' +
                    obj.status +
                    "</nobr></div>";

                  break;

                default:
                  statusString = '<div class="ui label">Unsigned</div>';
              }

              dom.makeNode("status", "div", { text: statusString });
            },
          };
        }
      }
    }

    collectionsSetup.state.ignoreTeamTagFromCollectionsFile = 1;

    sb.notify({
      type: "show-collection",
      data: collectionsSetup,
    });
  }

  function build_loader(ui, text, style) {
    var wrapperStyle = "";

    if (style !== undefined) {
      wrapperStyle = style;
    }

    ui.makeNode("loadingSeg", "div", {
      css: "ui active centered inline loader",
    });

    ui.makeNode("loadWrap", "div", {
      style: wrapperStyle,
    });
    ui.loadWrap.makeNode("loadText", "div", {
      text: text,
      css: "text-center",
    });
  }

  return {
    // framework functions
    initListeners: function () {
      sb.listen({
        "add-contract-to-object": this.addToObject,
        "contracts-run": this.run,
        "start-payment-portal": this.startPaymentPortal,
        "start-signature-portal": this.startSignaturePortal,
        "start-document-portal": this.startDocumentPortal,
        "update-contracts-title": this.updateTitle,
        "view-all-contracts": this.viewAll,
        "view-single-contract": this.viewSingle,
        "view-contract-templates": this.viewContractTemplates,
        "register-proposal-section-type": this.registerProposalSectionType,
        "register-merge-tag": this.registerMergeTag,
        "get-merged-html": this.getMergedDoc,
        "get-template-query": this.getTemplateQuery,
        "view-pdf": this.viewPDF,
        "request-signature": this.requestSignature,
      });
    },

    init: function () {
      sb.notify({
        type: "register-tool",
        data: {
          navigationItem: {
            moduleId: sb.moduleId,
            instanceId: sb.instanceId,
            id: "contractsComponent",
            title: "Contracts",
            icon: '<i class="fa fa-file-text-o"></i>',
            views: [
              // Main tool
              {
                id: "contractTools",
                layers: ["hq", "team", "project", "myStuff", "object"],
                availableToEntities: true,
                //     							layers: ['hq', 'team', 'myStuff', 'project', 'tool', 'entity', 'object'],
                name: "Documents",
                tip: "Create documents for this headquarter. Choose from a list of templates and send it out for signature.",
                icon: {
                  type: "file",
                  color: "yellow",
                },
                mainViews: [
                  {
                    dom: function (ui, state, draw, mainDom) {
                      build_toolCollections(ui, state, draw);
                    },
                  },
                ],
                boxViews: [
                  {
                    id: "contractApprovals",
                    title: "Documents and Contracts",
                    width: "eight",
                    collections: {
                      fields: {
                        name: {
                          title: "Name",
                          type: "title",
                          isSearchable: true,
                        },
                        date_created: {
                          title: "Created",
                          type: "title",
                          view: function (ui, obj) {
                            var status = obj.status;

                            if (!status) {
                              status = "Unsigned";
                            }

                            ui.makeNode("date", "div", {
                              css: "date",
                              text: `Status: ${status} | Added ${moment(
                                obj.date_created
                              )
                                .local()
                                .fromNow()}`,
                            });
                          },
                        },
                      },
                      actions: {},
                      selectedView: "list",
                      objectType: "contracts",
                      emptyMessage: "No documents need approval",
                      subviews: {
                        list: {
                          hideTimeRangeFilter: true,
                        },
                      },
                      where: {
                        is_template: 0,
                        childObjs: {
                          group_type: true,
                          name: true,
                          status: true,
                          created_by: {
                            profile_image: true,
                          },
                        },
                      },
                    },
                  },
                ],
                settings: [
                  {
                    object_type: "contract_types",
                    name: "1. Contract Types",
                  },
                  {
                    name: "2. Signature Request Email Template",
                    object_type: "contract_settingss",
                    action: emailTemplateSettings,
                  },
                  {
                    name: "3. Electronic Signature Disclaimer",
                    object_type: "contract_settings",
                    action: editSystemSettings,
                  },
                ],
                default: true
              }, // template-table
              {
                id: "template-table",
                type: "table",
                default: true,
                title: "Contract Templates",
                icon: '<i class="fa fa-file-text-o"></i>',
                setup: createTemplateTableSetup(),
              }, // newTemplate
              {
                id: "newTemplate",
                type: "custom",
                title: "New Template",
                icon: '<i class="fa fa-plus"></i>',
                color: "green",
                dom: createContractView,
              },
              // CONTRACT OBJECT VIEW
              {
                id: "contracts-obj",
                type: "object-view",
                title: "Contract",
                icon: "pdf",
                select: 2,
                dom: function (dom, state, draw) {
                  singleView(state.pageObject, dom, state, draw, false);
                },
              }, // CONTRACT OBJECT EDIT VIEW
              {
                id: "contracts-obj",
                type: "object-view",
                title: "Contract",
                icon: "pdf",
                edit: true,
                select: 2,
                dom: function (dom, state, draw) {
                  // Navigate to regular page and edit from there
                  var url = window.location.href;
                  var editParams = "=o-" + state.pageObjectType + "-e-";
                  var defaultParams = "=o-" + state.pageObjectType + "-";
                  if (url.indexOf(editParams) > -1) {
                    var newURL = url.replace(editParams, defaultParams);
                    history.pushState({}, null, newURL);
                    singleView(state.pageObject, dom, state, draw, true);
                  }
                },
              }, // Project Invoice Tool
              {
                id: "projectInvoiceTool",
                type: "tool",
                name: "Invoice",
                default: true,
                tip: "Build the invoice for this project.",
                icon: {
                  type: "usd",
                  color: "green",
                },
                mainViews: [
                  {
                    name: "Invoice Tool",
                    dom: function (dom, state, draw) {
                      function build_invoiceView(ui, state, draw) {
                        var RootUi = ui;
                        var MenuUi = {};

                        function build_menu(ui, BodyUI) {
                          //$('.mainViewContainer').css('padding', 'none');

                          /*
													ui.makeNode('menuBreak', 'div', {
														text: '<br />'
													});
*/

                          ui.makeNode("menu", "div", {
                            css: "ui black inverted stackable menu",
                            id: "topInvoiceMenu",
                          });

                          ui.menu
                            .makeNode("invoiceItem", "div", {
                              tag: "a",
                              css: "header item invoiceMenuItem",
                              text: "INVOICE",
                            })
                            .notify(
                              "click",
                              {
                                type: [sb.moduleId + "-run"],
                                data: {
                                  run: function (data) {
                                    BodyUI.empty();

                                    build_body(BodyUI);

                                    BodyUI.patch();
                                  },
                                },
                              },
                              sb.moduleId
                            );

                          ui.menu.makeNode("rightMenu", "div", {
                            css: "right menu",
                          });

                          ui.menu.rightMenu.makeNode("invoicePortal", "div", {
                            tag: "a",
                            css: "item",
                            text: "Payment Portal",
                            target: "_blank",
                            href:
                              sb.url +
                              "/app/invoices#?&i=" +
                              appConfig.instance +
                              "&pid=" +
                              state.pageObject.id,
                          });

                          ui.menu.rightMenu
                            .makeNode("paymentItem", "div", {
                              tag: "a",
                              css: "ui item invoiceMenuItem",
                              text: "Manage Payments",
                            })
                            .notify(
                              "click",
                              {
                                type: [sb.moduleId + "-run"],
                                data: {
                                  run: function () {
                                    ui.makeNode("modal", "modal", {
                                      onShow: function () {
                                        sb.data.db.obj.getWhere(
                                          "invoices",
                                          {
                                            related_object:
                                              state.project.proposal.id,
                                          },
                                          function (invoices) {
                                            sb.notify({
                                              type: "show-payment-button",
                                              data: {
                                                dom: ui.modal.body,
                                                notification:
                                                  "invoice-payment-completed",
                                                admin: true,
                                                invoices: invoices,
                                                proposalId: state.project.proposal.id,
                                                completeCallback:
                                                  function () {},
                                              },
                                            });
                                          }
                                        );
                                      },
                                      onClose: function () {
                                        dom.wrapper.body.seg.css(
                                          "ui very padded basic loading segment"
                                        );

                                        invoicesView(
                                          dom.wrapper.body.seg.seg,
                                          state,
                                          draw,
                                          function () {
                                            dom.wrapper.body.seg.css(
                                              "ui very padded basic segment"
                                            );
                                          }
                                        );
                                      },
                                    });

                                    ui.patch();
                                    ui.modal.show();
                                  },
                                },
                              },
                              sb.moduleId
                            );

                          ui.menu.rightMenu.makeNode("pdfs", "div", {
                            css: "ui simple dropdown item pdfMenu",
                            style: "color:white !important;",
                            text: "PDF's",
                          });

                          ui.menu.rightMenu.pdfs.makeNode("div", "div", {
                            css: "inverted menu",
                          });

                          ui.menu.rightMenu
                            .makeNode("docsItem", "div", {
                              tag: "a",
                              css: "ui item invoiceMenuItem",
                              text: "Documents & Contracts",
                            })
                            .notify(
                              "click",
                              {
                                type: [sb.moduleId + "-run"],
                                data: {
                                  run: function (data) {
                                    /*
																	$('.invoiceMenuItem').removeClass('active');
																	$(ui.menu.rightMenu.docsItem.selector).addClass('active');
*/
                                    ui.makeNode("modal", "modal", {
                                      onShow: function () {
                                        ui.modal.body.makeNode(
                                          "head",
                                          "div",
                                          {}
                                        );
                                        ui.modal.body.makeNode(
                                          "lb_1",
                                          "lineBreak",
                                          { spaces: 1 }
                                        );
                                        ui.modal.body.makeNode(
                                          "body",
                                          "div",
                                          {}
                                        );

                                        ui.modal.body.head.makeNode(
                                          "title",
                                          "div",
                                          {
                                            tag: "h2",
                                            text: "Documents & Contracts",
                                          }
                                        );

                                        projectView(
                                          ui.modal.body.body,
                                          state,
                                          draw
                                        );

                                        ui.modal.body.patch();
                                      },
                                    });

                                    ui.patch();
                                    ui.modal.show();

                                    /*
build_docsArea(ui);

																	BodyUI.empty();

																	projectView(BodyUI, state, draw);

																	BodyUI.patch();
*/
                                  },
                                },
                              },
                              sb.moduleId
                            );
                        }

                        function build_docsArea(ui) {}

                        function build_header(ui, run) {
                          ui.makeNode("seg", "div", {
                            css: "ui very padded basic segment",
                            style: "padding-bottom:0px;",
                          });

                          ui.seg.makeNode("grid", "div", {
                            css: "ui grid",
                          });

                          ui.seg.grid.makeNode("col1", "div", {
                            css: "six wide column",
                          });
                          ui.seg.grid.makeNode("col2", "div", {
                            css: "ten wide column",
                          });

                          ui.seg.grid.col2.makeNode("grid", "div", {
                            css: "ui equal width grid",
                          });

                          ui.seg.grid.col2.grid.makeNode("col1", "div", {
                            css: "column",
                          });
                          ui.seg.grid.col2.grid.makeNode("col2", "div", {
                            css: "column",
                          });
                          ui.seg.grid.col2.grid.makeNode("col3", "div", {
                            css: "column",
                          });

                          if (!run) {
                            return;
                          }

                          var project = state.data.project[0];
                          var contact = state.data.contact[0];
                          var user = state.data.user[0];
                          var cachedProject = _.clone(project);

                          project.owner = user;

                          ui.seg.grid.col1.makeNode("company", "div", {
                            tag: "h1",
                            text: contact.company.name,
                            css: "ui huge header",
                            style: "font-size: 3em;",
                          });

                          ui.seg.grid.col2.grid.col1.makeNode(
                            "owner",
                            "div",
                            {}
                          );
                          ui.seg.grid.col2.grid.col1.makeNode(
                            "lb_1",
                            "lineBreak",
                            { spaces: 1 }
                          );
                          ui.seg.grid.col2.grid.col1.makeNode(
                            "mainContact",
                            "div",
                            {}
                          );

                          ui.seg.grid.col2.grid.col2.makeNode(
                            "start_dateWrap",
                            "div",
                            {}
                          );
                          ui.seg.grid.col2.grid.col2.makeNode(
                            "lb_1",
                            "lineBreak",
                            { spaces: 1 }
                          );
                          ui.seg.grid.col2.grid.col2.makeNode(
                            "end_dateWrap",
                            "div",
                            {}
                          );

                          ui.seg.grid.col2.grid.col2.start_dateWrap.makeNode(
                            "label",
                            "div",
                            {
                              text: 'Start Date <i class="pencil alternate icon"></i>',
                              css: "text-muted",
                            }
                          );
                          ui.seg.grid.col2.grid.col2.start_dateWrap.makeNode(
                            "cont",
                            "div",
                            {}
                          );

                          ui.seg.grid.col2.grid.col2.end_dateWrap.makeNode(
                            "label",
                            "div",
                            {
                              text: 'End Date <i class="pencil alternate icon"></i>',
                              css: "text-muted",
                            }
                          );
                          ui.seg.grid.col2.grid.col2.end_dateWrap.makeNode(
                            "cont",
                            "div",
                            {}
                          );

                          ui.seg.grid.col2.grid.col3.makeNode(
                            "venueWrap",
                            "div",
                            {}
                          );
                          ui.seg.grid.col2.grid.col3.makeNode(
                            "lb_1",
                            "lineBreak",
                            { spaces: 1 }
                          );
                          ui.seg.grid.col2.grid.col3.makeNode(
                            "guestCountWrap",
                            "div",
                            {}
                          );

                          ui.seg.grid.col2.grid.col3.venueWrap.makeNode(
                            "label",
                            "div",
                            {
                              css: "text-muted",
                              text: 'Locations <i class="pencil alternate icon"></i>',
                            }
                          );
                          ui.seg.grid.col2.grid.col3.venueWrap.makeNode(
                            "cont",
                            "div",
                            {}
                          );

                          ui.seg.grid.col2.grid.col3.guestCountWrap.makeNode(
                            "label",
                            "div",
                            {
                              text: 'Head Count <i class="pencil alternate icon"></i>',
                              css: "text-muted",
                            }
                          );
                          ui.seg.grid.col2.grid.col3.guestCountWrap.makeNode(
                            "cont",
                            "div",
                            {}
                          );

                          // ---- Fields ----

                          sb.notify({
                            type: "view-field",
                            data: {
                              type: "user",
                              property: "owner",
                              obj: project,
                              options: {
                                title: "Manager",
                              },
                              ui: ui.seg.grid.col2.grid.col1.owner,
                            },
                          });

                          sb.notify({
                            type: "view-field",
                            data: {
                              type: "user",
                              property: "main_contact",
                              obj: project,
                              options: {
                                title: "Main Contact",
                              },
                              ui: ui.seg.grid.col2.grid.col1.mainContact,
                            },
                          });

                          sb.notify({
                            type: "view-field",
                            data: {
                              type: "date",
                              dateType: "day",
                              property: "start_date",
                              ui: ui.seg.grid.col2.grid.col2.start_dateWrap
                                .cont,
                              obj: project,
                              options: {
                                edit: true,
                                update: function (
                                  obj,
                                  [fieldName],
                                  newVal,
                                  onComplete
                                ) {
                                  sb.notify({
                                    type: "update-invoices",
                                    data: {
                                      dom: RootUi.wrapper.body.seg.seg,
                                      project: cachedProject,
                                      startDate: newVal,
                                      callback: function (response) {
                                        // Show loader
                                        $("#loader").fadeIn();

                                        // Update state
                                        cachedProject.start_date = newVal;

                                        // Update dom
                                        invoicesView(
                                          RootUi.wrapper.body.seg.seg,
                                          state,
                                          draw,
                                          function () {
                                            RootUi.wrapper.body.seg.css(
                                              "ui very padded basic segment"
                                            );
                                            onComplete;

                                            // Hide loader
                                            $("#loader").fadeOut();
                                          }
                                        );
                                      },
                                    },
                                  });
                                },
                              },
                            },
                          });

                          sb.notify({
                            type: "view-field",
                            data: {
                              type: "date",
                              dateType: "day",
                              property: "end_date",
                              ui: ui.seg.grid.col2.grid.col2.end_dateWrap.cont,
                              obj: project,
                              options: {
                                edit: true,
                                update: function (
                                  obj,
                                  [fieldName],
                                  newVal,
                                  onComplete
                                ) {
                                  sb.notify({
                                    type: "update-invoices",
                                    data: {
                                      dom: RootUi.wrapper.body.seg.seg,
                                      project: cachedProject,
                                      endDate: newVal,
                                      callback: function (response) {
                                        // Show loader
                                        $("#loader").fadeIn();

                                        // Update state
                                        cachedProject.end_date = newVal;

                                        // Update dom
                                        invoicesView(
                                          RootUi.wrapper.body.seg.seg,
                                          state,
                                          draw,
                                          function () {
                                            RootUi.wrapper.body.seg.css(
                                              "ui very padded basic segment"
                                            );
                                            onComplete;

                                            // Hide loader
                                            $("#loader").fadeOut();
                                          }
                                        );
                                      },
                                    },
                                  });
                                },
                              },
                            },
                          });

                          sb.notify({
                            type: "view-field",
                            data: {
                              type: "locations",
                              property: "locations",
                              ui: ui.seg.grid.col2.grid.col3.venueWrap.cont,
                              obj: project,
                              options: {
                                edit: true,
                                multi: true,
                              },
                            },
                          });

                          sb.notify({
                            type: "view-field",
                            data: {
                              type: "quantity",
                              property: "head_count",
                              ui: ui.seg.grid.col2.grid.col3.guestCountWrap
                                .cont,
                              obj: project,
                              options: {
                                edit: true,
                                commitUpdates: true,
                                onUpdate: function (obj) {
                                  // get active menu and update guest count
                                  //if (obj.proposal && Number.isInteger(obj.proposal.menu.id)) {
                                  if (
                                    obj.proposal &&
                                    Number.isInteger(obj.proposal.menu)
                                  ) {
                                    //RootUi.wrapper.body.seg.css('ui very padded basic loading segment');

                                    var updates = [
                                      {
                                        id: obj.id,
                                        head_count: obj.head_count,
                                        object_bp_type: "groups",
                                      },
                                      {
                                        id: obj.proposal.menu,
                                        guest_count: obj.head_count,
                                        object_bp_type: "inventory_menu",
                                      },
                                    ];

                                    sb.data.db.obj.update(
                                      "proposals",
                                      updates,
                                      function (response) {
                                        if (response) {
                                          state.project = obj;
                                          state.pageObject.proposal.main_object =
                                            obj;

                                          // RootUi.wrapper.body.seg.seg.empty();
                                          //
                                          // invoicesView(RootUi.wrapper.body.seg.seg, state, draw, function(){
                                          //
                                          // 	RootUi.wrapper.body.seg.css('ui very padded basic segment');
                                          //
                                          // });

                                          build_invoiceView(
                                            RootUi,
                                            state,
                                            draw
                                          );

                                          // sb.data.db.controller('setMenuReservations', {
                                          // 	menuId: obj.proposal.menu
                                          // }, function(response){
                                          //
                                          // 	state.project = obj;
                                          // 	state.pageObject.proposal.main_object = obj;
                                          //
                                          // 	RootUi.wrapper.body.seg.seg.empty();
                                          //
                                          // 	invoicesView(RootUi.wrapper.body.seg.seg, state, draw, function(){
                                          //
                                          // 		RootUi.wrapper.body.seg.css('ui very padded basic segment');
                                          //
                                          // 	});
                                          //
                                          // 	build_invoiceView(RootUi, state, draw);
                                          //
                                          // });
                                        }
                                      }
                                    );
                                  }
                                },
                              },
                            },
                          });

                          ui.seg.loading(false);

                          ui.seg.grid.patch();
                        }

                        ui.empty();

                        ui.makeNode("wrapper", "div", {});

                        ui.wrapper.makeNode("menuWrap", "div", {});
                        ui.wrapper.makeNode("head", "div", {});
                        ui.wrapper.makeNode("body", "div", {});

                        MenuUi = ui.wrapper.menuWrap;

                        build_menu(ui.wrapper.menuWrap, ui.wrapper.body);

                        draw({
                          dom: ui,
                          after: function (ui) {
                            _.each(state.data.contracts, function (contract) {
                              ui.wrapper.menuWrap.menu.rightMenu.pdfs.div
                                .makeNode("item" + contract.id, "div", {
                                  css: "item",
                                  text:
                                    '<i class="ui file icon"></i> ' +
                                    contract.name,
                                })
                                .notify(
                                  "click",
                                  {
                                    type: "contracts-run",
                                    data: {
                                      run: function () {
                                        $(
                                          ui.wrapper.menuWrap.menu.rightMenu
                                            .pdfs.div["item" + contract.id]
                                            .selector
                                        ).html(
                                          '<i class="ui notched circle loading icon"></i> ' +
                                            contract.name
                                        );

                                        createMergedHTML(
                                          contract,
                                          function (htmlString) {
                                            $(
                                              ui.wrapper.menuWrap.menu.rightMenu
                                                .pdfs.div["item" + contract.id]
                                                .selector
                                            ).html(
                                              '<i class="ui file icon"></i> ' +
                                                contract.name
                                            );

                                            sb.data.makePDF(htmlString, "D");
                                          }
                                        );
                                      },
                                    },
                                  },
                                  sb.moduleId
                                );
                            });

                            ui.wrapper.menuWrap.menu.rightMenu.pdfs.div.patch();

                            build_header(ui.wrapper.head, true);

                            // ui.wrapper.body.makeNode('seg', 'div', {css:'ui very padded loading segment', style:'padding-top:0px !important;'})
                            // 	.makeNode('seg', 'div', {css:''});

                            ui.wrapper.body.makeNode("loadingWrap", "div", {});

                            ui.wrapper.body.makeNode("seg", "div", {});

                            build_loader(
                              ui.wrapper.body.loadingWrap,
                              "building menu...please wait"
                            );

                            ui.patch();

                            invoicesView(
                              ui.wrapper.body.seg,
                              state,
                              draw,
                              function () {
                                //ui.wrapper.body.seg.css('ui basic segment');

                                ui.wrapper.body.loadingWrap.empty();
                                ui.wrapper.body.loadingWrap.patch();
                              }
                            );
                          },
                        });
                      }

                      function display_invoiceView() {
                        if (_.isEmpty(state.pageObject.main_contact)) {
                          var linkOpts = {
                            tool: "crmProject",
                            startAt: sb.data.url.createPageURL("object", {
                              id: state.pageObject.id,
                              name: state.pageObject.name,
                              type: "project",
                            }),
                          };

                          var link = sb.data.url.createPageURL(
                            "project-tools",
                            linkOpts
                          );

                          dom
                            .makeNode("msg", "div", { css: "ui message" })
                            .makeNode("h", "div", {
                              text: "No point of contact set",
                              css: "header",
                            });

                          dom.msg.makeNode("m", "div", {
                            tag: "p",
                            text:
                              'Go <a href="' +
                              link +
                              '">here</a> to set the point of contact on this project.',
                          });

                          dom.patch();

                          return;
                        }

                        build_invoiceView(dom, state, draw);
                      }

                      var queries = [
                        {
                          responseName: "isDeletedProject",
                          table: "groups",
                          query: {
                            id: state.project.id,
                            archive: true,
                          },
                          childObjs: {
                            id: true,
                            is_deleted: true,
                            is_archieved: true,
                            name: true,
                          },
                        },
                        {
                          responseName: "contracts",
                          table: "contracts",
                          query: {
                            related_object: state.project.id,
                          },
                          childObjs: 1,
                        },
                        {
                          responseName: "project",
                          table: "groups",
                          query: {
                            id: state.project.id,
                          },
                          childObjs: 1,
                        },
                        {
                          responseName: "contact",
                          table: "contacts",
                          query: {
                            id: state.project.main_contact.id,
                          },
                          childObjs: 1,
                        },
                      ];
                      var projectOwnerId = 0;

                      if (state.project.owner === null) {
                        if (state.project.main_contact.manager) {
                          projectOwnerId =
                            state.project.main_contact.manager.id;
                        }
                      } else {
                        projectOwnerId = state.project.owner.id;
                      }

                      queries.push({
                        responseName: "user",
                        table: "users",
                        query: {
                          id: projectOwnerId,
                        },
                        childObjs: 1,
                      });

                      build_loader(
                        dom,
                        "fetching all necessary data...please wait"
                      );
                      dom.patch();

                      sb.data.db.service(
                        "DataRepository",
                        "get",
                        queries,
                        function (data) {
                          state.data = data;

                          // verify archived status and prompt to unarchive if necessary
                          let is_archived =
                            !!data.isDeletedProject[0]?.is_deleted;

                          if (is_archived) {
                            const options = {
                              objectType: state.pageObjectType,
                              is_archieved: true,
                            };

                            archive_selected_items(
                              state.project.id,
                              options,
                              function (choice) {
                                // if declined to unarchive, return user to main project page
                                if (choice) {
                                  window.location.reload();
                                } else {
                                  let projectUrl = sb.data.url.createPageURL(
                                    "object",
                                    {
                                      id: state.pageObject.id,
                                      name: state.pageObject.name,
                                      type: "project",
                                    }
                                  );
                                  window.open(projectUrl, "_self");
                                }
                              }
                            );
                          } else {
                            display_invoiceView();
                          }
                        }
                      );
                    },
                  },
                ],
                boxViews: [],
              }, // Document Approvals -- hq/team/my stuff
              {
                id: "documentApprovals",
                layers: ["hq", "team", "myStuff"],
                name: "Document Approvals",
                tip: "Documents that need admin approval before being sent to a client.",
                icon: {
                  type: "file",
                  color: "red",
                },
                mainViews: [
                  {
                    dom: function (dom, state, draw, mainDom) {
                      var where = {
                        status: "Approval Requested",
                        childObjs: {
                          name: true,
                          merge_type: true,
                          date_created: true,
                          requires_approval: true,
                          sent_on: true,
                          last_updated: {
                            fname: true,
                            lname: true,
                          },
                        },
                      };

                      // Adjust query for different layers
                      if (state.where && !_.isEmpty(state.where.tagged_with)) {
                        // For my stuff, don't look at contracts you are tagged on,
                        // look at contracts that you are an approver on
                        if (state.layer === "myStuff") {
                          where.notify_list = {
                            type: "contains",
                            value: state.where.tagged_with[0],
                          };
                        } else {
                          where.tagged_with = state.where.tagged_with;
                        }
                      }

                      sb.notify({
                        type: "show-collection",
                        data: {
                          domObj: dom,
                          state: state,
                          objectType: "contracts",
                          singleView: {
                            view: function (ui, obj, draw) {
                              singleView(obj, ui, state, draw, false);
                            },
                          },
                          actions: {
                            view: true,
                            create: function (ui, obj_info, onComplete) {
                              editContract(obj_info, ui, state, onComplete);
                            },
                          },
                          fields: {
                            name: {
                              title: "Name",
                            },
                            merge_type: {
                              title: "Type",
                              view: function (dom, obj) {
                                var typeString = "<i>No Type Selected</i>";
                                if (obj.merge_type) {
                                  typeString = obj.merge_type.toUpperCase();
                                }
                                dom.makeNode("type", "div", {
                                  text: typeString,
                                });
                              },
                            },
                            requires_approval: {
                              title: "Requires Approval",
                              view: function (dom, obj) {
                                var typeString = "No";
                                if (obj.requires_approval == "Yes") {
                                  typeString = "Yes";
                                }
                                dom.makeNode("type", "div", {
                                  text: typeString,
                                });
                              },
                            },
                            sent_on: {
                              title: "Sent On",
                              view: function (dom, obj) {
                                var sentOn =
                                  '<div style="text-align: center"> - </div>';

                                if (obj.sent_on !== "") {
                                  sentOn = moment(obj.sent_on)
                                    .local()
                                    .format("M/D/YYYY h:mm a");
                                }

                                dom.makeNode("sent_on", "div", {
                                  text: sentOn,
                                });
                              },
                            },
                            date_created: {
                              title: "Date Created",
                              view: function (dom, obj) {
                                dom.makeNode("date_created", "div", {
                                  text: moment(obj.date_created)
                                    .local()
                                    .format("M/D/YYYY h:mm a"),
                                });
                              },
                            },
                            last_updated: {
                              title: "Last Updated",
                              view: function (dom, obj) {
                                dom.makeNode("last_updated", "div", {
                                  text: moment(obj.last_updated)
                                    .local()
                                    .format("M/D/YYYY h:mm a"),
                                });
                              },
                            },
                          },
                          groupings: {
                            type: "merge_type",
                          },
                          where: where,
                        },
                      });
                    },
                  },
                ],
                boxViews: [
                  {
                    id: "documentApprovals",
                    title: "Document Approvals",
                    width: "eight",
                    collections: {
                      fields: {
                        name: {
                          title: "Name",
                          type: "title",
                          isSearchable: true,
                        },
                        date_created: {
                          title: "Created",
                          type: "title",
                          view: function (ui, obj) {
                            ui.makeNode("date", "div", {
                              css: "date",
                              text: `Created ${moment(obj.date_created)
                                .local()
                                .fromNow()}</span>`,
                            });
                          },
                        },
                      },
                      actions: {},
                      selectedView: "list",
                      objectType: "contracts",
                      emptyMessage: "No documents need approval",
                      subviews: {
                        list: {
                          hideTimeRangeFilter: true,
                        },
                      },
                      where: {
                        status: "Approval Requested",
                        paged: {
                          sortCol: "end_date",
                          sortDir: "asc",
                          count: true,
                          page: 0,
                          pageLength: 5,
                        },
                        childObjs: {
                          group_type: true,
                          name: true,
                          created_by: {
                            profile_image: true,
                          },
                        },
                      },
                    },
                  },
                ],
              }, // Document Approvals -- team tool
              {
                id: "documentApprovals",
                type: "teamTool",
                name: "Document Approvals",
                tip: "Documents that need admin approval before being sent to a client.",
                icon: {
                  type: "file",
                  color: "red",
                },
                mainViews: [
                  {
                    dom: function (dom, state, draw, mainDom) {
                      sb.notify({
                        type: "show-collection",
                        data: {
                          domObj: dom,
                          state: state,
                          objectType: "contracts",
                          singleView: {
                            view: function (ui, obj, draw) {
                              singleView(obj, ui, state, draw, false);
                            },
                          },
                          actions: {
                            view: true,
                            create: function (ui, obj_info, onComplete) {
                              editContract(obj_info, ui, state, onComplete);
                            },
                          },
                          fields: {
                            name: {
                              title: "Name",
                            },
                            merge_type: {
                              title: "Type",
                              view: function (dom, obj) {
                                var typeString = "<i>No Type Selected</i>";
                                if (obj.merge_type) {
                                  typeString = obj.merge_type.toUpperCase();
                                }
                                dom.makeNode("type", "div", {
                                  text: typeString,
                                });
                              },
                            },
                            requires_approval: {
                              title: "Requires Approval",
                              view: function (dom, obj) {
                                var typeString = "No";
                                if (obj.requires_approval == "Yes") {
                                  typeString = "Yes";
                                }
                                dom.makeNode("type", "div", {
                                  text: typeString,
                                });
                              },
                            },
                            date_created: {
                              title: "Date Created",
                              view: function (dom, obj) {
                                dom.makeNode("date_created", "div", {
                                  text: moment(obj.date_created)
                                    .local()
                                    .format("M/D/YYYY h:mm a"),
                                });
                              },
                            },
                            last_updated: {
                              title: "Last Updated",
                              view: function (dom, obj) {
                                dom.makeNode("last_updated", "div", {
                                  text: moment(obj.last_updated)
                                    .local()
                                    .format("M/D/YYYY h:mm a"),
                                });
                              },
                            },
                          },
                          groupings: {
                            type: "merge_type",
                          },
                          where: {
                            status: "Approval Requested",
                            childObjs: {
                              name: true,
                              merge_type: true,
                              date_created: true,
                              requires_approval: true,
                              sent_on: true,
                              last_updated: {
                                fname: true,
                                lname: true,
                              },
                            },
                          },
                        },
                      });
                    },
                  },
                ],
                boxViews: [
                  {
                    id: "documentApprovals",
                    title: "Document Approvals",
                    width: "eight",
                    collections: {
                      fields: {
                        name: {
                          title: "Name",
                          type: "title",
                          isSearchable: true,
                        },
                        date_created: {
                          title: "Created",
                          type: "title",
                          view: function (ui, obj) {
                            ui.makeNode("date", "div", {
                              css: "date",
                              text: `Created ${moment(obj.date_created)
                                .local()
                                .fromNow()}</span>`,
                            });
                          },
                        },
                      },
                      actions: {},
                      selectedView: "list",
                      objectType: "contracts",
                      emptyMessage: "No documents need approval",
                      subviews: {
                        list: {
                          hideTimeRangeFilter: true,
                        },
                      },
                      where: {
                        status: "Approval Requested",
                        paged: {
                          sortCol: "end_date",
                          sortDir: "asc",
                          count: true,
                          page: 0,
                          pageLength: 5,
                        },
                        childObjs: {
                          group_type: true,
                          name: true,
                          created_by: {
                            profile_image: true,
                          },
                        },
                      },
                    },
                  },
                ],
              }, // Settings
              {
                id: "settings",
                type: "settings",
                title: "Settings",
                icon: '<i class="fa fa-cog"></i>',
                setup: [
                  {
                    name: "3. Electronic Signature Disclaimer",
                    object_type: "contract_settings",
                    action: editSystemSettings,
                  },
                  {
                    object_type: "contract_types",
                    name: "1. Contract Types",
                  },
                  {
                    name: "2. Signature Request Email Template",
                    object_type: "contract_settingss",
                    action: emailTemplateSettings,
                  },
                ],
              },
            ],
          },
        },
      });

      components.table = sb.createComponent("crud-table");
      //components.notes = sb.createComponent('notes2');
      //components.emails = sb.createComponent('emails');
      components.tags = sb.createComponent("tags");

      registerMergeTags();
    },

    destroy: function () {
      _.each(components, function (comp) {
        comp.destroy();
      });

      ui = {};
      tableUI = {};
      components = {};
      contractId = 0;
      objectId = 0;
      contractType = "";
      defaultDisclaimer =
        "The parties agree that this agreement may be electronically signed. The parties agree that the electronic signatures appearing on this agreement are the same as handwritten signatures for the purposes of validity, enforceability and admissibility.<br /><br />You may withdraw your consent to receive electronic documents, notices or disclosures at any time. In order to withdraw consent, you must notify the sending party that you wish to withdraw consent and request that your future documents, notices and disclosures be provided in paper format. To request paper copies of documents; withdraw consent to conduct business electronically and receive documents, notices or disclosures electronically; or withdraw consent to sign documents electronically, please contact the sending party by telephone, postal mail or email.";
      defaultRequestEmailSubject = "New Signature Request";
      defaultRequestEmail = "";
      homeScreen = true;
      navigation = true;
    },

    addToObject: function (data) {
      var contactId = 0,
        objectId = 0,
        contractType = data.type;

      if (data.objectId) {
        objectId = data.objectId;
      }

      if (data.contactId) {
        contactId = data.contactId;
      }

      addContractView({}, data.domObj, data);
    },

    getMergedDoc: function (data) {
      if (data && (!_.isEmpty(data.state) || !_.isEmpty(data.template))) {
        createMergedHTML(
          {
            state: data.state,
            template: data.template,
            justMerge: true,
            html_string: data.template,
            related_object: data.related_object,
          },
          data.callback,
          false,
          "",
          {
            mergeVars: data.mergeVars || false,
          }
        );
      } else {
        createMergedHTML(
          data.obj,
          data.callback,
          data.payNow,
          data.customString,
          data.opts
        );
      }
    },

    registerProposalSectionType: function (setup) {
      sectionTypes[setup.name] = {
        getHTML: setup.getHTML,
      };
    },

    registerMergeTag: function (setup) {
      MergeTags.push({
        name: setup.name,
        tag: setup.tag,
        data: setup.data || false,
        parse: setup.parse || false,
        options: setup.options || false,
      });
    },

    run: function (data) {
      data.run();
    },

    startPaymentPortal: function () {
      var instance = sb.data.url.getParams().i;
      var invoice = sb.data.url.getParams().iid;
      var project = sb.data.url.getParams().pid;
      var clientId = false;
      var invoiceListIds = false;
      var portalQueries = [
        {
          responseName: "instances",
          table: "instances",
          query: {
            instance: instance,
          },
          childObjs: 1,
        },
        {
          responseName: "portalTemplates",
          table: "contracts",
          query: {
            merge_type: "portal",
          },
          childObjs: 1,
        },
        {
          responseName: "invoices",
          table: "invoices",
          query: {
            id: parseInt(invoice),
          },
          childObjs: 2,
        },
        {
          responseName: "company_logo",
          table: "company_logo",
          query: {
            is_primary: "yes",
          },
          childObjs: 1,
        },
        {
          responseName: "invoice_system",
          table: "invoice_system",
          query: null,
          childObjs: 2,
        },
        {
          responseName: "hq",
          table: "groups",
          query: {
            group_type: "Headquarters",
          },
          childObjs: 1,
        },
      ];

      appConfig.instance = instance;

      sb.data.db.setAPIPath("../../api/_getAdmin.php");

      sb.data.db.controller(
        "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
        {
          queryObj: {
            instance: instance,
          },
          instance: instance,
          objectType: "instances",
        },
        function (instanceResponse) {
          $("#main-loader").addClass("animated fadeOut");
          $("#main-loader").removeClass("ui active text loader");
          $("#main-loader").empty();

          appConfig = instanceResponse[0];

          document.documentElement.style.height = "100vh";

          dom = sb.dom.make(".main");

          dom.makeNode("menu", "div", {
            css: "ui huge inverted stackable menu",
          });

          dom.makeNode("cont", "div", {
            css: "ui basic very padded loading segment",
          });

          dom.build();

          if (invoice) {
            sb.data.db.service(
              "DataRepository",
              "get",
              portalQueries,
              function (portalQueries_response) {
                var instanceObj = portalQueries_response.instances;
                var invoiceObj = portalQueries_response.invoices[0];
                var companyLogoObj = portalQueries_response.company_logo;
                var config = portalQueries_response.invoice_system;

                dom.menu.makeNode("title", "div", {
                  css: "ui item",
                  text: instanceObj[0].systemName,
                });

                dom.menu.makeNode("right", "div", {
                  css: "ui right inverted menu",
                });

                dom.menu.right
                  .makeNode("pdf", "div", {
                    css: "ui item",
                    text: '<i class="ui download icon"></i> Download PDF',
                  })
                  .notify(
                    "click",
                    {
                      type: "contracts-run",
                      data: {
                        run: function (obj) {
                          var pdfQueries = [
                            {
                              responseName: "menu_pricing_breakdown",
                              table: "inventory_menu_pricing_breakdown",
                              query: {
                                space: invoiceObj.related_object.main_object.id,
                              },
                              childObjs: 1,
                            },
                            {
                              responseName: "billing_address",
                              table: "contact_info",
                              query: {
                                id: parseInt(config[0].billing_address.id),
                              },
                              childObjs: 1,
                            },
                            {
                              responseName: "project",
                              table: "groups",
                              query: {
                                group_type: "Project",
                                id: parseInt(
                                  invoiceObj.related_object.main_object.id
                                ),
                              },
                              childObjs: 2,
                            },
                          ];

                          dom.cont.loading(true);

                          sb.data.db.service(
                            "DataRepository",
                            "get",
                            pdfQueries,
                            function (pdfQueries_response) {
                              dom.cont.loading(false);

                              sb.data.makePDF(
                                createMergedInvoiceHTML({
                                  pricedMenu:
                                    pdfQueries_response.menu_pricing_breakdown,
                                  invoices: [invoiceObj],
                                  logo: companyLogoObj,
                                  projectObj: pdfQueries_response.project[0],
                                  billingAddress:
                                    pdfQueries_response.billing_address[0],
                                  hq: portalQueries_response.hq[0],
                                  invoice: true,
                                }),
                                "D"
                              );
                            }
                          );
                        }.bind({}, invoice),
                      },
                    },
                    sb.moduleId
                  );

                dom.menu.patch();

                dom.cont.makeNode("grid", "div", { css: "ui centered grid" });
                dom.cont.grid.makeNode("col", "div", {
                  css: "ui six wide column",
                });
                dom.cont.grid.col.makeNode("seg", "div", {
                  css: "ui padded segment",
                });
                dom.cont.grid.col.seg.makeNode("name", "div", {
                  tag: "h1",
                  text:
                    invoiceObj.name +
                    '<div class="sub header">For ' +
                    invoiceObj.related_object.main_object.name +
                    "</div>",
                  css: "ui header",
                });
                dom.cont.grid.col.seg.makeNode("table", "div", {
                  tag: "table",
                  css: "ui table",
                });

                dom.cont.grid.col.seg.table.makeNode("item", "div", {
                  tag: "tr",
                });
                dom.cont.grid.col.seg.table.item.makeNode("td1", "div", {
                  tag: "td",
                  text: invoiceObj.memo,
                });
                dom.cont.grid.col.seg.table.item.makeNode("td2", "div", {
                  tag: "td",
                  style: "text-align:right;",
                  text: "$" + (invoiceObj.amount / 100).formatMoney(),
                });

                dom.cont.grid.col.seg.table.makeNode("payments", "div", {
                  tag: "tr",
                });
                dom.cont.grid.col.seg.table.payments.makeNode("td1", "div", {
                  tag: "td",
                  style: "text-align:right;",
                  text: "Payments",
                });
                dom.cont.grid.col.seg.table.payments.makeNode("td2", "div", {
                  tag: "td",
                  style: "text-align:right;",
                  text: "$" + (invoiceObj.paid / 100).formatMoney(),
                });

                dom.cont.grid.col.seg.table.makeNode("due", "div", {
                  tag: "tr",
                  style: "font-weight:bold;",
                });
                dom.cont.grid.col.seg.table.due.makeNode("td1", "div", {
                  tag: "td",
                  style: "text-align:right;",
                  text: "Total Due",
                });
                dom.cont.grid.col.seg.table.due.makeNode("td2", "div", {
                  tag: "td",
                  style: "text-align:right;",
                  text: "$" + (invoiceObj.balance / 100).formatMoney(),
                });

                dom.cont.grid.col.seg
                  .makeNode("button", "div", {
                    css: "ui button green centered",
                    text:
                      '<i class="fa fa-credit-card"></i> Pay ($' +
                      (Math.ceil(invoiceObj.balance) / 100).formatMoney() +
                      ")",
                  })
                  .notify(
                    "click",
                    {
                      type: "paymentMethodRun",
                      data: {
                        run: function () {
                          stripePaymentSourcesUI.loading(true);

                          var paramObj = {
                            contactId: invoiceObj.main_contact.id,
                            stripeId: invoiceObj.main_contact.stripe_id,
                            verifiedSources: true,
                            instanceId: appConfig.id,
                          };

                          sb.data.db.service(
                            "StripeService",
                            "getStripeCustomer",
                            paramObj,
                            function (response) {
                              $(".ui.green.button.centered").hide();
                              stripePaymentSourcesUI.loading(false);

                              if (!response.customer) {
                                dom.cont.grid.col.seg.makeNode(
                                  "noItems",
                                  "div",
                                  {
                                    text: "There was an error retrieving the Stripe payment sources for this contact.",
                                    css: "text-center",
                                    style: "color: red;",
                                  }
                                );

                                dom.cont.grid.col.seg.patch();
                              } else {
                                sb.data.db.obj.getAll(
                                  "invoice_fees",
                                  function (feesList) {
                                    sb.notify({
                                      type: "view-field",
                                      data: {
                                        type: "contact-payment-sources",
                                        property: "object",
                                        ui: stripePaymentSourcesUI,
                                        obj: response,
                                        options: {
                                          contactId: invoiceObj.main_contact.id,
                                          initiatePayments: true,
                                          feesList: feesList,
                                          invoiceBalance: invoiceObj.balance,
                                          instanceId: appConfig.id,
                                          eventId: invoiceObj.related_object.id,
                                          selectedInvoiceIds: [+invoice],
                                          selectedInvoices: [invoiceObj],
                                          paymentForm: false,
                                        },
                                      },
                                    });

                                    stripePaymentSourcesUI.patch();
                                  }
                                );
                              }
                            }
                          );
                        }.bind(dom),
                      },
                    },
                    sb.moduleId
                  );

                var stripePaymentSourcesUI = dom.cont.grid.col.seg.makeNode(
                  "sources",
                  "div",
                  { css: "ui basic segment contact-payment-sources-container" }
                );

                dom.cont.patch();

                dom.cont.loading(false);
              }
            );
          } else {
            if (!project) {
              clientId = sb.data.url.getParams().cid;
              invoiceListIds = sb.data.url.getParams().timing.split(",");
            }

            if (clientId) {
                sb.data.db.controller(
                    "getObjectsWhere&api_webform=true&pagodaAPIKey=" + instance,
                    {
                    queryObj: {
                        id: +clientId,
                    },
                    getChildObjs: 2,
                    instance: instance,
                    objectType: "companies",
                    },
                    function (clientObjs) {
                      sb.data.db.controller(
                          "getObjectsWhere&api_webform=true&pagodaAPIKey=" +
                          instance,
                          {
                          queryObj: {
                              id: {
                              type: "or",
                              values: invoiceListIds,
                              },
                          },
                          instance: instance,
                          objectType: "invoices",
                          },
                          function (invoices) {
                          sb.data.db.controller(
                              "getMergeTagData",
                              {
                              objId: obj.id,
                              },
                              function (data) {
                              var logo = !_.isEmpty(data.company_logo)
                                  ? data.company_logo[0]
                                  : [];
                              var invoiceSystem = data.invoice_system;

                              var statementHTML =
                                  createMergedClientStatementPortal({
                                  invoiceSystem: invoiceSystem[0],
                                  invoices: invoices,
                                  client: clientObjs[0],
                                  logo: logo,
                                  });

                              dom.cont
                                  .makeNode("top", "div", {
                                  css: "ui center aligned grid",
                                  })
                                  .makeNode("cont", "div", {
                                  css: "ui six wide column",
                                  });

                              dom.cont.makeNode("bottom", "div", { css: "" });

                              dom.cont.patch();

                              dom.cont.bottom.makeNode("header", "div", {
                                  text: statementHTML,
                              });

                              dom.cont.patch();

                              dom.cont.loading(false);

                              $(".pdf-statement-download").on(
                                  "click",
                                  function () {
                                  sb.data.makePDF(
                                      $("#invoice-container").html(),
                                      "I"
                                  );
                                  }
                              );

                              var subtotal = 0,
                                  total = 0,
                                  amountPaid = 0,
                                  totalDue = 0;

                              _.each(invoices, function (item, k) {
                                  _.each(item.payments, function (p) {
                                  amountPaid += +p.amount;
                                  });

                                  subtotal += item.amount;
                              });

                              total = subtotal;

                              total = Math.round(total);

                              totalDue = total - amountPaid;

                              sb.notify({
                                  type: "show-make-payment-button",
                                  data: {
                                  domObj: dom.cont.top.cont,
                                  payment: {
                                      customerId: invoices[0].main_contact,
                                      invoiceId: invoices,
                                      price: totalDue,
                                      admin: false,
                                  },
                                  buttonSetup: {
                                      text: '<i class="fa fa-credit-card"></i> Make A Payment',
                                      css: "green centered",
                                      skip: false,
                                      admin: false,
                                      notification: "invoice-payment-completed",
                                      action: savePayment.bind(dom, invoices),
                                      refreshAction: function (dom) {
                                      sb.notify({
                                          type: "start-payment-portal",
                                          data: {
                                          domObj: dom,
                                          },
                                      });
                                      }.bind({}, dom),
                                  },
                                  },
                              });
                              }
                          );
                          },
                          null,
                          true
                      );
                    }
                );
            } else {

                sb.data.db.controller(
                    "getMergeTagData"
                    , {objId: +project}
                    , function (data) {

                        var obj = data.project;
                        var invoices = data.invoices;
                        var feesList = data.feesList;
                        var invoiceSystem = data.invoice_system;
                        var logo = !_.isEmpty(data.company_logo)
                        ? data.company_logo[0]
                        : [];

                        function viewInvoiceBalance(ui, data){

                            var logo = !_.isEmpty(data.company_logo)
                            ? data.company_logo[0]
                            : [];

                            var invoices = data.invoices;
                            var invoiceSystem = data.invoice_system;
                            var contact = data.contact;

                            var statementHTML = createMergedClientStatementPortal(
                                {
                                    invoiceSystem: invoiceSystem[0],
                                    invoices: invoices,
                                    client: contact,
                                    logo: logo,
                                    payNow: true,
                                    project: data.project
                                }
                            );

                            ui.makeNode("header", "div", {
                                css: "ui basic segment",
                                style: "padding-left:0px",
                            });
                            ui.header.makeNode("hone", "div", {
                                css: "ui large header",
                                text:
                                "To pay a single invoice, click the 'Pay Now' button next to the invoice you want to pay. \n " +
                                "Then, follow the prompts below. \n ",
                            });
                            ui.header.makeNode("htwo", "div", {
                                css: "ui large header",
                                text: "To pay a custom amount, use the 'Pay Custom Amount' button below.",
                            });

                            ui.makeNode("invoice", "div", {
                                css: "ui very padded grey segment",
                                text: statementHTML,
                            });
                            ui.makeNode("btns", "div", {
                                css: "ui basic segment",
                            });
                            ui.makeNode(
                                "paymentField",
                                "div",
                                {}
                            );

                            ui.btns.makeNode("payAll", "div", {
                                css: "ui huge green button",
                                text: '<i class="fa fa-credit-card"></i> Pay Custom Amount',
                            })
                            .notify(
                                "click",
                                {
                                    type: "paymentMethodRun",
                                    data: {
                                        run: function () {
                                            // spinner on button click
                                            ui.btns.payAll.loading();

                                            var paramObj = {
                                                contactId: obj.main_contact.id,
                                                stripeId: obj.main_contact.stripe_id,
                                                verifiedSources: true,
                                                instanceId: appConfig.id,
                                            };
                                            var root =
                                                sb.url + "/api/_getAdmin.php?do=";
                                            var invoiceBalance = totalDue;

                                            sb.data.db.service(
                                                "StripeService",
                                                "getStripeCustomer",
                                                paramObj,
                                                function (response) {

                                                    if (!response.customer) {
                                                        dom.compWrapper.makeNode(
                                                            "noItems",
                                                            "headerText",
                                                            {
                                                                text: "There was an error retrieving the Stripe payment sources for this contact.",
                                                                size: "xx-small",
                                                                css: "text-center",
                                                            }
                                                        );
                                                    } else {

                                                        var customOptions = {
                                                            contactId:
                                                                obj.main_contact.id,
                                                            initiatePayments: false,
                                                            feesList: feesList,
                                                            invoiceBalance:
                                                                invoiceBalance,
                                                            instanceId: appConfig.id,
                                                            eventId: obj.proposal.id,
                                                            selectedInvoices:
                                                                selectedInvoiceArray.sort(
                                                                    function (a, b) {
                                                                        return (
                                                                            new Date(
                                                                                a.due_date
                                                                            ) -
                                                                            new Date(b.due_date)
                                                                        );
                                                                    }
                                                                ),
                                                            selectedInvoiceIds:
                                                                selectedInvoiceArray
                                                                    .sort(function (a, b) {
                                                                        return (
                                                                            new Date(
                                                                                a.due_date
                                                                            ) -
                                                                            new Date(b.due_date)
                                                                        );
                                                                    })
                                                                    .map((inv) => inv.id),
                                                            paymentForm: false,
                                                            onSuccess: function(resp){

                                                                console.warn('PayAll onSuccess', arguments);

                                                                $("html, body").animate({ scrollTop: "0" }, 1000);

                                                                data.invoices = resp.invoices;

                                                                dom.cont.loading(true);

                                                                setTimeout(function(){

                                                                    invoiceCont.refresh(data);

                                                                    dom.cont.loading(false);

                                                                }, 5000);
                                                            }
                                                        };

                                                        sb.notify({
                                                            type: "view-field",
                                                            data: {
                                                                type: "contact-payment-sources",
                                                                property: "object",
                                                                ui: ui.paymentField,
                                                                obj: response,
                                                                options: customOptions
                                                            },
                                                        });

                                                        ui.paymentField.patch();

                                                        // scroll down to payment form
                                                        $("html, body").animate(
                                                            {
                                                                scrollTop:
                                                                    $(document).height(),
                                                            },
                                                            1000
                                                        );

                                                        ui.btns.payAll.loading( false );

                                                    }
                                                },
                                                root
                                            );
                                        },
                                    },
                                },
                                sb.moduleId
                            );

                            return ui.patch();
                        }

                        dom.menu.makeNode("title", "div", {
                            css: "ui item",
                            text: obj.name.toUpperCase(),
                        });

                        dom.menu.makeNode("right", "div", {
                            css: "ui right inverted menu",
                        });

                                    dom.menu.right
                                        .makeNode("pdf", "div", {
                                        css: "ui item",
                                        text: '<i class="ui download icon"></i> Download PDF',
                                        })
                                        .notify(
                                        "click",
                                        {
                                            type: "contracts-run",
                                            data: {
                                            run: function (obj) {
                                                sb.data.makePDF(
                                                createMergedClientStatementPortal({
                                                  invoiceSystem: invoiceSystem[0],
                                                  invoices: invoices,
                                                  client: invoices[0].main_contact,
                                                  logo: logo,
                                                  payNow: false,
                                                  project: obj
                                                }),
                                                "D"
                                            );
                                }.bind({}, obj),
                                },
                            },
                            sb.moduleId
                            );

                        dom.cont.makeNode("modals", "div", {});

                        dom.cont.makeNode("grid", "div", {
                            css: "ui stackable grid",
                        });
                        dom.cont.grid.makeNode("left", "div", {
                            css: "ui five wide column",
                        });

                        var invoiceCont = dom.cont.grid.makeNode("right", "div", {
                            css: "ui eleven wide column",
                            id: "invoiceCont"
                        });

                        invoiceCont.refresh = viewInvoiceBalance.bind({}, invoiceCont);
                        invoiceCont.refresh(data);

                        dom.cont.grid.left
                        .makeNode("cont", "div", {
                            css: "ui basic loading segment",
                        })
                        .makeNode("title", "div", {
                            css: "ui large header",
                            text: "Signed Contract List",
                        });

                        dom.makeNode("modals", "div", {});
                        dom.modals.makeNode("payment", "modal", {});

                        dom.build();

                        dom.cont.loading(false);

                        // calculate total
                        var subtotal = 0,
                        total = 0,
                        amountPaid = 0,
                        totalDue = 0;

                        _.each(invoices, function (item, k) {
                        amountPaid += item.paid;
                        subtotal += item.amount;
                        });

                        total = subtotal;

                        total = Math.round(total);

                        totalDue = total - amountPaid;

                        invoices = _.sortBy(invoices, "due_date");

                        // calculate total of current balance (factor in invoice due dates)
                        var currentBalance = 0;
                        var invoicePaymentTotal = 0;
                        var selectedInvoiceArray = [];

                        _.each(invoices, function (item, k) {
                        invoicePaymentTotal = 0;

                        if (item["balance"] > 0) {
                            selectedInvoiceArray.push(item);
                        }

                        _.each(item.payments, function (p) {
                            invoicePaymentTotal += +p.amount;
                        });

                        currentBalance += item.amount - invoicePaymentTotal;
                        });

                        if (currentBalance == 0) {
                        currentBalance = totalDue;
                        }

                        $('#invoiceCont').on("click", ".payButton", function () {
                            // spinner on button click
                            var invId = $(this).data("id");

                            $("#" + invId).addClass("loading");

                            var paramObj = {
                                contactId: obj.main_contact.id,
                                stripeId: obj.main_contact.stripe_id,
                                verifiedSources: true,
                                instanceId: appConfig.id,
                            };

                            var root = sb.url + "/api/_getAdmin.php?do=";
                            var invoiceBalance = +this.dataset.balance;
                            var invoiceId = +this.dataset.id;

                            sb.data.db.service(
                                "StripeService",
                                "getStripeCustomer",
                                paramObj,
                                function (response) {
                                    if (!response.customer) {
                                        dom.compWrapper.makeNode(
                                            "noItems",
                                            "headerText",
                                            {
                                                text: "There was an error retrieving the Stripe payment sources for this contact.",
                                                size: "xx-small",
                                                css: "text-center",
                                            }
                                        );
                                    } else {

                                        var inlineOptions = {
                                            contactId: obj.main_contact.id,
                                            initiatePayments: true,
                                            feesList: feesList,
                                            invoiceBalance: invoiceBalance,
                                            instanceId: appConfig.id,
                                            eventId: obj.proposal.id,
                                            selectedInvoices: [invoiceId],
                                            selectedInvoiceIds: [invoiceId],
                                            paymentForm: false,
                                            onSuccess: function(resp){

                                                dom.cont.loading(true);

                                                console.warn('inline onSuccess', {arguments, data});

                                                $("html, body").animate({ scrollTop: "0" }, 1000);

                                                data.invoices = resp.invoices;


                                                setTimeout(function(){

                                                    invoiceCont.refresh(data);

                                                    dom.cont.loading(false);

                                                }, 5000);

                                            }
                                        };

                                        sb.notify({
                                            type: "view-field",
                                            data: {
                                                type: "contact-payment-sources",
                                                property: "object",
                                                ui: dom.cont.grid.right
                                                    .paymentField,
                                                obj: response,
                                                options: inlineOptions,
                                            },
                                        });

                                        dom.cont.grid.right.paymentField.patch();

                                        // scroll down to payment form
                                        $("html, body").animate(
                                            { scrollTop: $(document).height() },
                                            1000
                                        );
                                        $("#" + invId).removeClass("loading");


                                    }
                                },
                                root
                            );
                        });

                        sb.data.db.controller(
                            "getObjectsWhere&api_webform=true&pagodaAPIKey=" +
                            instance,
                            {
                                queryObj: {
                                    related_object: obj.id,
                                    status: "Signed",
                                },
                                instance: instance,
                                objectType: "contracts",
                            },
                            function (contracts) {

                                _.each(contracts, function (contract) {
                                    dom.cont.grid.left.cont.makeNode(
                                        "contract" + contract.id,
                                        "div",
                                        { css: "ui secondary segment" }
                                    );
                                    dom.cont.grid.left.cont[
                                        "contract" + contract.id
                                    ].makeNode("title", "div", {
                                        css: "ui header",
                                        text:
                                            contract.name +
                                            " - <small>Signed on " +
                                            moment(contract.signed_on)
                                                .local()
                                                .format("LLLL") +
                                            "</small>",
                                    });
                                    dom.cont.grid.left.cont[
                                        "contract" + contract.id
                                    ]
                                        .makeNode("pdf", "div", {
                                            css: "ui blue button",
                                            text: '<i class="ui download icon"></i> Download PDF',
                                        })
                                        .notify(
                                            "click",
                                            {
                                                type: "contracts-run",
                                                data: {
                                                    run: function (obj) {
                                                        $(
                                                            dom.cont.grid.left.cont[
                                                                "contract" + contract.id
                                                            ].pdf.selector
                                                        ).html(
                                                            '<i class="ui circle notch loading icon"></i> Building PDF'
                                                        );

                                                        sb.data.db.obj.getById(
                                                            obj.object_bp_type,
                                                            obj.id,
                                                            function (fullObj) {
                                                                createMergedHTML(
                                                                    fullObj,
                                                                    function (htmlString) {
                                                                        $(
                                                                            dom.cont.grid.left.cont[
                                                                                "contract" + contract.id
                                                                            ].pdf.selector
                                                                        ).html(
                                                                            '<i class="ui download icon"></i> Download PDF'
                                                                        );

                                                                        sb.data.makePDF(
                                                                            htmlString,
                                                                            "D"
                                                                        );
                                                                    }
                                                                );
                                                            },
                                                            1
                                                        );
                                                    }.bind({}, contract),
                                                },
                                            },
                                            sb.moduleId
                                        );
                                });

                                dom.cont.grid.left.cont.patch();

                                dom.cont.grid.left.cont.loading(false);
                            }
                        );

                    }
                );

            }
          }
        }
      );
    },

    startDocumentPortal: function (data) {
      var dom = data.domObj;

      registerMergeTags();
      documentPortalView(dom, []);
    },

    startSignaturePortal: function (data) {
      $("#main-loader").addClass("animated fadeOut");
      $("#main-loader").removeClass("ui active text loader");
      $("#main-loader").empty();
      var dom = data.domObj;

      //dom.build();

      registerMergeTags();

      signContractView(dom, []);

      /*
			instance = sb.data.url.getParams().i;
			contract = sb.data.url.getParams().wid;
			appConfig.instance = instance;

			sb.data.db.setAPIPath('../../api/_getAdmin.php');
*/

      /*
			sb.notify({
				type: 'get-instance-data'
				, data: {
					onComplete: function () {

						sb.data.db.controller(
							'getObjectsWhere&api_webform=true&pagodaAPIKey='+ instance,
							{
								queryObj:{
									instance:instance
								},
								instance:instance,
								objectType:'instances'
							},
						function(instanceObj){

							sb.data.db.controller(
								'getObjectsWhere&api_webform=true&pagodaAPIKey='+ instance,
								{
									queryObj:{
										id:+contract
									},
									objectType:'contracts',
									getChildObjs:1
								},
							function(objs){

								appConfig = instanceObj[0];
								obj = objs[0];

								dom.empty();

								dom.makeNode('cont', 'div', {css:'ui basic segment'})
									.makeNode('cont', 'div', {css:''});

								dom.cont.cont.makeNode('modals', 'div', {});

								dom.build();

								signContractView(dom.cont.cont, obj, []);

							}, sb.url+'_getAdmin.php?do=');

						}, sb.url+'_getAdmin.php?do=');

					}
				}
			});
*/
    },

    updateTitle: function (data) {
      components.table.notify({
        type: "update-title",
        data: data,
      });
    },

    viewAll: function (data) {
      var contactId = 0,
        objectId = 0,
        contractType = data.type;

      if (data.objectId) {
        objectId = data.objectId;
      }

      if (data.contactId) {
        contactId = data.contactId;
      }

      if (data.contractId && data.objectView) {
        sb.data.db.obj.getById(
          "contracts",
          data.contractId,
          function (contractObj) {
            singleView(contractObj, data.domObj, data, data.draw);
          },
          4
        );
      } else {
        createUI(data.domObj, contractType, objectId, contactId, data);

        tableUI.state.show();
      }
    },

    viewContractTemplates: function (data) {
      var setup = createTemplateTableSetup();

      setup.domObj = data.domObj;

      components.table.notify({
        type: "show-table",
        data: setup,
      });
    },

    viewSingle: function (data) {
      function view(ui, selection, object, options) {
        // Clear cached merge tags
        CachedMergeTags = [];
        sb.notify({
          type: "clear-entity-cached-merge-tags",
          data: {
            callback: function () {
              var canEditMessage = true;

              // Don't allow editing if the document created is meant to be
              // kept up to date (show the merged doc at the current state,
              // but the document should store the pre-merged version.)
              if (
                options.sendDocumentAsLink === true &&
                options.activeDoc === true
              ) {
                canEditMessage = false;
              }

              ui.makeNode("br1", "lineBreak", {});
              ui.makeNode("test", "div", {
                id: "sendEmailMessage",
                css: "ui segment",
                style: "box-shadow:none; padding:0 !important;",
                text: '<i class="grey notched circle loading icon"></i>',
              });
              ui.makeNode("br2", "lineBreak", {});
              ui.patch();

              if (selection && object) {
                selection.related_object = object;
              }

              sb.notify({
                type: "get-merged-html",
                data: {
                  obj: selection,
                  callback: function (html) {
                    options.body = {
                      html: html,
                    };

                    ui.test.empty();

                    sb.notify({
                      type: "view-field",
                      data: {
                        type: "detail",
                        property: "html",
                        obj: options.body,
                        options: {
                          edit: canEditMessage,
                          editing: canEditMessage,
                          commitUpdates: false,
                          alwaysMerge: false,
                          useMedium: true,
                          header: true,
                          labelTxt: "Email Body",
                          mentions: [],
                          promptSave: false,
                          previewAndEdit: true,
                          css: "",
                          style: "",
                          onChange: function (html) {
                            options.body = {
                              html: html,
                            };
                          },
                        },
                        ui: ui.test,
                      },
                    });

                    ui.test.patch();
                  },
                  opts: { mergeForPdf: false },
                },
              });
            },
          },
        });
      }

      if (_.isNumber(data.object) && !_.isNull(data.object)) {
        sb.data.db.obj.getById(
          "contracts",
          data.object,
          function (contractObj) {
            data.object = contractObj;

            view(data.ui, data.selection, data.object, data.options);
          },
          4
        );
      } else {
        view(data.ui, data.selection, data.object, data.options);
      }
    },

    getTemplateQuery: function (data) {
      getTemplateQuery(data.state, data.callback);
    },

    viewPDF: function (data) {
      viewPDF(data.obj, data.callback);
    },

    requestSignature: function (data) {
      requestSignature(data.obj);
    },
  };

  // Helper function for fee calculation (copied from contact-payment-sources.js)
  function calculatePaymentWithFees(price, percentFee, flatFee, selectedInvoiceIds) {
    var amount = parseFloat(price);

    var variableFee = parseFloat(
      (Math.round(amount * (parseFloat(percentFee) / 100) * 100) / 100).toFixed(2)
    );
    var fixedFee = parseFloat(
      (Math.round(parseFloat(flatFee * selectedInvoiceIds.length) * 100) / 100).toFixed(2)
    );

    var vOne = variableFee + fixedFee;
    var vTwo = (vOne) * 100;
    var vThree = Math.round(vTwo) / 100;
    var vFour = (vThree).toFixed(2);

    var total = amount + parseFloat(vFour);
    var fee = Math.round((total - amount) * 100) / 100;

    var output = {
      amount: parseInt(Math.round(amount * 100)),
      fee: parseInt(fee * 100),
      total: parseInt(Math.round(total * 100)),
      feeDisplayText: percentFee,
    };
    return output;
  }

  // Main CSG Forte callback handler
  function processCSGForteCallback(messageEvent) {
    console.log('🔥 CSG FORTE CALLBACK RECEIVED 🔥', messageEvent);

    // 🚨 FILTER OUT NON-CSG FORTE MESSAGES 🚨
    if (messageEvent && messageEvent.origin) {
      if (!messageEvent.origin.includes('forte.net')) {
        console.log('🚫 IGNORING NON-FORTE MESSAGE from:', messageEvent.origin);
        return; // Ignore messages not from CSG Forte
      }
    }

    // Extract callback data
    var callbackData = null;
    if (messageEvent && messageEvent.data) {
      try {
        callbackData = JSON.parse(messageEvent.data);
        console.log('✅ Callback data parsed successfully:', callbackData);
      } catch (e) {
        console.log('⚠️ Failed to parse callback data as JSON, using raw data:', e);
        callbackData = messageEvent.data;
      }
    } else {
      console.log('📝 Using messageEvent directly as callback data');
      callbackData = messageEvent;
    }

    // 🚨 ADDITIONAL FILTER: Must have CSG Forte event structure 🚨
    if (!callbackData || (!callbackData.event && !callbackData.response_code)) {
      console.log('🚫 IGNORING MESSAGE - Not CSG Forte format:', callbackData);
      return;
    }

    // 🔥 ALWAYS LOG ALL CSG FORTE RESPONSES TO PIPEDREAM 🔥
    if (typeof sb !== 'undefined' && sb.data && sb.data.db && sb.data.db.service) {
      sb.data.db.service(
        'CSGForteService',
        'debugLog',
        {
          stage: 'FRONTEND_CALLBACK_RECEIVED',
          data: {
            messageEvent: messageEvent,
            callbackData: callbackData,
            timestamp: new Date().toISOString()
          },
          source: 'frontend_callback'
        },
        function(result) {
          console.log('🔧 Debug sent to backend for callback');
        },
        function(error) {
          console.log('❌ Debug send failed for callback:', error);
        }
      );
    }

    // 🔥 HANDLE ALL CSG FORTE EVENTS WITH COMPLETE VISIBILITY 🔥
    console.log('📋 EVENT TYPE:', callbackData.event);
    console.log('📋 RESPONSE CODE:', callbackData.response_code);
    console.log('📋 RESPONSE DESC:', callbackData.response_desc || callbackData.response_description);

    // Handle each event type specifically
    switch(callbackData.event) {
      case 'begin':
        console.log('🚀 CSG Forte modal started - transaction beginning');
        // 🔥 SHOW POPUP FOR BEGIN EVENT 🔥
        $('body').toast({
          title: '🚀 CSG Forte Begin',
          message: 'Modal loaded and transaction beginning',
          class: 'info',
          showIcon: 'info circle',
          displayTime: 5000
        });
        // Log to Pipedream
        if (typeof sb !== 'undefined' && sb.data && sb.data.db && sb.data.db.service) {
          sb.data.db.service('CSGForteService', 'debugLog', {
            stage: 'MODAL_BEGIN',
            data: callbackData,
            source: 'frontend_begin'
          }, function(result) { console.log('🔧 Begin event logged'); }, function(error) { console.log('❌ Begin log failed:', error); });
        }
        return; // Don't process further

      case 'success':
        console.log('✅ PAYMENT SUCCESS - Processing...');
        $('body').toast({
          title: '✅ CSG Forte Success',
          message: 'Payment successful - processing backend...',
          class: 'success',
          showIcon: 'check circle',
          displayTime: 5000
        });
        break;

      case 'failure':
        console.log('❌ PAYMENT FAILURE - Logging...');
        $('body').toast({
          title: '❌ CSG Forte Failure',
          message: 'Payment failed: ' + (callbackData.response_description || callbackData.response_desc || 'Unknown error'),
          class: 'error',
          showIcon: 'exclamation triangle',
          displayTime: 10000
        });
        break;

      case 'error':
        console.log('💥 SYSTEM ERROR - Logging...');
        $('body').toast({
          title: '💥 CSG Forte Error',
          message: 'System error: ' + (callbackData.response_description || callbackData.response_desc || 'Unknown error'),
          class: 'error',
          showIcon: 'exclamation triangle',
          displayTime: 10000
        });
        break;

      case 'abort':
        console.log('🚫 USER CANCELLED - Logging...');
        $('body').toast({
          title: '🚫 CSG Forte Abort',
          message: 'User cancelled payment',
          class: 'warning',
          showIcon: 'ban',
          displayTime: 5000
        });
        break;

      case 'expired':
        console.log('⏰ SESSION EXPIRED - Logging...');
        $('body').toast({
          title: '⏰ CSG Forte Expired',
          message: 'Payment session expired',
          class: 'warning',
          showIcon: 'clock outline',
          displayTime: 8000
        });
        break;

      default:
        console.log('❓ UNKNOWN EVENT TYPE:', callbackData.event);
        $('body').toast({
          title: '❓ CSG Forte Unknown Event',
          message: 'Unknown event: ' + (callbackData.event || 'undefined') + ' - Check console',
          class: 'warning',
          showIcon: 'question circle',
          displayTime: 10000
        });
        break;
    }

    // Check if successful (only process success events)
    var isSuccess = callbackData.event === 'success' ||
                   callbackData.response_code === 'A01' ||
                   callbackData.response_desc === 'TEST APPROVAL';

    if (isSuccess) {

      sb.data.db.service(
        'CSGForteService',
        'processCSGFortePayment',
        callbackData,
        function(result) {
          // CSG Forte Payment Success Handler (EXACT COPY/PASTE patterns from iCheckGateway and contracts.js)

          // 1. Individual payment toasts (EXACT COPY from iCheckGateway lines 2095-2112)
          if (result.processedPayments && result.processedPayments.length > 0) {
            _.map(result.processedPayments, function (paym) {
              var inv = _.find(result.invoices, { id: paym.invoice });
              var title = 'Invoice Updated';
              var message = 'Payment of <strong>$' + (paym.amount / 100).formatMoney() + '</strong> has been applied to </br>' + inv.name.toUpperCase() + '.</br> New balance: $' + (inv.balance / 100).formatMoney();

              // Display alert
              $('body').toast({
                title: title,
                message: message,
                class: 'blue',
                showIcon: 'dollar sign',
                showProgress: 'bottom',
                displayTime: 15000,
                closeIcon: true
              });
            });
          }

          // 2. Call onSuccess handler if available (from contact-payment-sources context)
          if (typeof paymentPortalonSuccess !== 'undefined' && paymentPortalonSuccess) {
            try {
              paymentPortalonSuccess(result);
            } catch (err) {
              console.error('Error in paymentPortalonSuccess:', err);
            }
          }

          // 3. UI refresh using EXACT SAME PATTERN as working code
          $("html, body").animate({ scrollTop: "0" }, 1000);

          // Check if we have the stored references from when payment was initiated
          if (window.csgForteUIRefs && window.csgForteUIRefs.data && window.csgForteUIRefs.invoiceCont && window.csgForteUIRefs.domCont) {
            // EXACT COPY of working pattern from lines 12627-12637
            window.csgForteUIRefs.data.invoices = result.invoices;
            window.csgForteUIRefs.domCont.loading(true);

            setTimeout(function(){
              window.csgForteUIRefs.invoiceCont.refresh(window.csgForteUIRefs.data);
              window.csgForteUIRefs.domCont.loading(false);
            }, 5000);

          } else {
            // Fallback: Just show a success message
            $('body').toast({
              title: 'Payment Complete!',
              message: 'Please refresh the page to see updated balances.',
              class: 'success',
              showIcon: 'check circle',
              displayTime: 10000
            });
          }
        },
        function(error) {
          console.log('❌ CSG FORTE BACKEND PROCESSING ERROR:', error);

          // 🔥 LOG BACKEND PROCESSING ERRORS TO PIPEDREAM 🔥
          if (typeof sb !== 'undefined' && sb.data && sb.data.db && sb.data.db.service) {
            sb.data.db.service(
              'CSGForteService',
              'debugLog',
              {
                stage: 'FRONTEND_BACKEND_ERROR',
                data: {
                  error: error,
                  callbackData: callbackData,
                  timestamp: new Date().toISOString()
                },
                source: 'frontend_error'
              },
              function(result) {
                console.log('🔧 Error debug sent to backend');
              },
              function(debugError) {
                console.log('❌ Debug send failed for error:', debugError);
              }
            );
          }

          $('body').toast({
            title: 'Processing Error',
            message: 'Payment successful but could not be recorded.',
            class: 'error',
            showIcon: 'exclamation triangle',
            displayTime: 20000
          });
        }
      );
    } else {
      // 🔥 HANDLE ALL NON-SUCCESS EVENTS 🔥
      console.log('❌ CSG FORTE NON-SUCCESS EVENT:', callbackData.event, callbackData);

      // 🔥 LOG ALL NON-SUCCESS EVENTS TO PIPEDREAM 🔥
      if (typeof sb !== 'undefined' && sb.data && sb.data.db && sb.data.db.service) {
        sb.data.db.service(
          'CSGForteService',
          'debugLog',
          {
            stage: 'FRONTEND_NON_SUCCESS_EVENT',
            data: {
              callbackData: callbackData,
              event_type: callbackData.event,
              response_code: callbackData.response_code,
              response_description: callbackData.response_description || callbackData.response_desc,
              timestamp: new Date().toISOString()
            },
            source: 'frontend_' + (callbackData.event || 'unknown')
          },
          function(result) {
            console.log('🔧 Non-success event logged to backend');
          },
          function(debugError) {
            console.log('❌ Debug send failed for non-success event:', debugError);
          }
        );
      }

      // All event notifications are now handled in the switch statement above
      console.log('ℹ️ Non-success event handled:', callbackData.event);
    }
  }



});
