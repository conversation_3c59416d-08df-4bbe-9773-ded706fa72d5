<?php

class CSGForteService
{
    private $forteApiAccessId = null;
    private $forteSecureKey = null;
    private $forteLocationId = null;
    private $forteEnvironment = null;
    private $currentEnv = null;
    private $testMode = false;

    function __construct($sb)
    {
        $this->sb = $sb;

        // Load environment variables - USE CURRENT_ENV ONLY
        $this->currentEnv = getenv('CURRENT_ENV');

        // Use CURRENT_ENV to determine CSG Forte mode
        $this->forteEnvironment = ($this->currentEnv === 'production') ? 'production' : 'sandbox';

        // 🔥 PHP SERVER-SIDE LOGGING 🔥
        $currentUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'];
        error_log("=== CSG FORTE SERVICE INIT ===");
        error_log("1) URL: " . $currentUrl);
        error_log("2) CURRENT_ENV: " . ($this->currentEnv ?: 'NULL'));
        error_log("3) CSG_FORTE_ENVIRONMENT: " . ($this->forteEnvironment ?: 'NULL'));
        error_log("================================");

        if ($this->forteEnvironment === 'production') {
            // Load production credentials
            $this->forteApiAccessId = getenv('CSG_FORTE_API_ACCESS_ID_PROD');
            $this->forteSecureKey = getenv('CSG_FORTE_SECURE_KEY_PROD');
            $this->forteLocationId = getenv('CSG_FORTE_LOCATION_ID_PROD');
            $this->testMode = false;
        } else {
            // Load development/sandbox credentials with fallbacks - LOCALHOST ALWAYS USES SANDBOX
            $this->forteApiAccessId = getenv('CSG_FORTE_API_ACCESS_ID') ?: 'f518bddbad0ff95d91a988675a765582';
            $this->forteSecureKey = getenv('CSG_FORTE_SECURE_KEY') ?: '76d62a1c850878be84bc1f51c5fff08a';
            $this->forteLocationId = getenv('CSG_FORTE_LOCATION_ID') ?: '358310';
            $this->testMode = true;
        }
    }

    // PRIVATE METHODS

    /**
     * Generate HMAC-SHA256 signature for CSG Forte Checkout API
     * Based on Forte documentation: api_access_id|method|version_number|total_amount|utc_time|order_number|customer_token|paymethod_token
     */
    private function generateSignature($signatureString)
    {
        // 🔥 COMPREHENSIVE SIGNATURE GENERATION LOGGING 🔥
        $this->sendDebugLog('SIGNATURE_GENERATION_START', [
            'signature_string' => $signatureString,
            'signature_string_length' => strlen($signatureString),
            'secure_key_length' => strlen($this->forteSecureKey),
            'secure_key_preview' => '***' . substr($this->forteSecureKey, -4),
            'hash_method' => 'HMAC-SHA256'
        ]);

        $signature = hash_hmac('sha256', $signatureString, $this->forteSecureKey);

        // 🔥 LOG GENERATED SIGNATURE 🔥
        $this->sendDebugLog('SIGNATURE_GENERATION_COMPLETE', [
            'generated_signature' => $signature,
            'signature_length' => strlen($signature),
            'signature_preview' => substr($signature, 0, 8) . '...' . substr($signature, -8)
        ]);

        return $signature;
    }

    /**
     * Get current UTC time from Forte servers (prevents replay attacks)
     */
    private function getForteUTCTime()
    {
        // 🔥 COMPREHENSIVE UTC TIME LOGGING 🔥
        $this->sendDebugLog('UTC_TIME_REQUEST_START', [
            'current_env' => $this->currentEnv,
            'forte_environment' => $this->forteEnvironment,
            'test_mode' => $this->testMode
        ]);

        // Use correct URLs from documentation
        $utcUrl = $this->testMode
            ? 'https://sandbox.forte.net/checkout/getUTC?callback=?'
            : 'https://checkout.forte.net/getUTC?callback=?';

        // 🔥 LOG THE EXACT URL BEING USED 🔥
        $this->sendDebugLog('UTC_TIME_URL_SELECTED', [
            'url' => $utcUrl,
            'test_mode' => $this->testMode,
            'environment_logic' => [
                'current_env' => $this->currentEnv,
                'forte_environment' => $this->forteEnvironment,
                'url_selection_logic' => $this->testMode ? 'sandbox_url' : 'production_url'
            ]
        ]);

        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $utcUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $curlError = curl_error($curl);
        curl_close($curl);

        // 🔥 LOG CURL RESPONSE DETAILS 🔥
        $this->sendDebugLog('UTC_TIME_CURL_RESPONSE', [
            'http_code' => $httpCode,
            'curl_error' => $curlError,
            'response_length' => strlen($response),
            'response_preview' => substr($response, 0, 100),
            'full_response' => $response
        ]);

        if ($httpCode === 200 && $response) {
            // Response is JSONP format: "callback(636397036957980000)"
            // Extract the UTC ticks from the parentheses
            $positionOfOpeningParenthesis = stripos($response, "(");
            $positionOfClosingParenthesis = stripos($response, ")");

            if ($positionOfOpeningParenthesis !== false && $positionOfClosingParenthesis !== false) {
                // FIXED: Use exact formula from CSG Forte documentation
                $utcTicks = substr($response, $positionOfOpeningParenthesis + 1, $positionOfClosingParenthesis - 2);

                // 🔥 LOG UTC TIME EXTRACTION 🔥
                $this->sendDebugLog('UTC_TIME_EXTRACTION', [
                    'raw_response' => $response,
                    'opening_pos' => $positionOfOpeningParenthesis,
                    'closing_pos' => $positionOfClosingParenthesis,
                    'extracted_utc' => $utcTicks,
                    'extraction_method' => 'documentation_formula'
                ]);

                return trim($utcTicks);
            }
        }

        // 🔥 LOG UTC API FAILURE 🔥
        $this->sendDebugLog('UTC_TIME_API_FAILED', [
            'http_code' => $httpCode,
            'response' => $response,
            'curl_error' => $curlError ?? 'none',
            'fallback_method' => 'manual_dotnet_ticks'
        ]);

        // Fallback: Generate .NET ticks manually if API fails
        // .NET ticks = 100-nanosecond intervals since January 1, 0001 00:00:00 UTC
        $unixTimestamp = time();
        $dotNetEpochTicks = 621355968000000000; // Ticks from 0001-01-01 to 1970-01-01
        $ticksPerSecond = 10000000; // 10^7 ticks per second
        $fallbackUtc = ($unixTimestamp * $ticksPerSecond) + $dotNetEpochTicks;

        // 🔥 LOG FALLBACK UTC GENERATION 🔥
        $this->sendDebugLog('UTC_TIME_FALLBACK_GENERATED', [
            'unix_timestamp' => $unixTimestamp,
            'dotnet_epoch_ticks' => $dotNetEpochTicks,
            'ticks_per_second' => $ticksPerSecond,
            'calculated_utc' => $fallbackUtc
        ]);

        return $fallbackUtc;
    }

    // PUBLIC METHODS

    /**
     * Debug method to check environment variables
     */
    public function debugEnvironment($request)
    {
        $debugInfo = [
            'raw_getenv' => [
                'CURRENT_ENV' => getenv('CURRENT_ENV'),
                'CSG_FORTE_API_ACCESS_ID' => getenv('CSG_FORTE_API_ACCESS_ID'),
                'CSG_FORTE_SECURE_KEY' => getenv('CSG_FORTE_SECURE_KEY'),
                'CSG_FORTE_LOCATION_ID' => getenv('CSG_FORTE_LOCATION_ID'),
                'CSG_FORTE_ENVIRONMENT' => getenv('CSG_FORTE_ENVIRONMENT'),
            ],
            'final_values' => [
                'currentEnv' => $this->currentEnv,
                'forteApiAccessId' => $this->forteApiAccessId,
                'forteSecureKey' => $this->forteSecureKey,
                'forteLocationId' => $this->forteLocationId,
                'forteEnvironment' => $this->forteEnvironment,
                'testMode' => $this->testMode,
            ],
            'env_array' => $_ENV,
            'server_array' => array_filter($_SERVER, function($key) {
                return strpos($key, 'CSG_FORTE') !== false || strpos($key, 'CURRENT_ENV') !== false;
            }, ARRAY_FILTER_USE_KEY)
        ];

        $returnObj = new stdClass();
        $returnObj->success = true;
        $returnObj->debug = $debugInfo;

        return $this->sb->sendData($returnObj, 1);
    }

    /**
     * Create secure payment button configuration - SIMPLIFIED VERSION
     */
    public function createPaymentButton($request)
    {
        // Initialize debug response object
        $debugObj = new stdClass();
        $debugObj->success = false;
        $debugObj->message = 'DEBUG: Function started';

        try {
            // 🔥 ENVIRONMENT VARIABLES DEBUG LOGGING 🔥
            $debugObj->env_debug = [
                'CURRENT_ENV' => getenv('CURRENT_ENV'),
                'CSG_FORTE_ENVIRONMENT' => getenv('CSG_FORTE_ENVIRONMENT'),
                'CSG_FORTE_API_ACCESS_ID' => getenv('CSG_FORTE_API_ACCESS_ID'),
                'CSG_FORTE_SECURE_KEY' => getenv('CSG_FORTE_SECURE_KEY') ? '***' . substr(getenv('CSG_FORTE_SECURE_KEY'), -4) : 'NOT_SET',
                'CSG_FORTE_LOCATION_ID' => getenv('CSG_FORTE_LOCATION_ID'),
                'CSG_FORTE_API_ACCESS_ID_PROD' => getenv('CSG_FORTE_API_ACCESS_ID_PROD'),
                'CSG_FORTE_SECURE_KEY_PROD' => getenv('CSG_FORTE_SECURE_KEY_PROD') ? '***' . substr(getenv('CSG_FORTE_SECURE_KEY_PROD'), -4) : 'NOT_SET',
                'CSG_FORTE_LOCATION_ID_PROD' => getenv('CSG_FORTE_LOCATION_ID_PROD'),
                'final_values_used' => [
                    'currentEnv' => $this->currentEnv,
                    'forteEnvironment' => $this->forteEnvironment,
                    'forteApiAccessId' => $this->forteApiAccessId,
                    'forteSecureKey' => $this->forteSecureKey ? '***' . substr($this->forteSecureKey, -4) : 'NOT_SET',
                    'forteLocationId' => $this->forteLocationId,
                    'testMode' => $this->testMode
                ]
            ];

            // 🔥 COMPREHENSIVE CREDENTIAL VERIFICATION LOGGING 🔥
            $this->sendDebugLog('CREDENTIAL_VERIFICATION', [
                'environment_detection' => [
                    'CURRENT_ENV_raw' => getenv('CURRENT_ENV'),
                    'CURRENT_ENV_type' => gettype(getenv('CURRENT_ENV')),
                    'CURRENT_ENV_empty' => empty(getenv('CURRENT_ENV')),
                    'currentEnv_property' => $this->currentEnv,
                    'forteEnvironment_property' => $this->forteEnvironment,
                    'testMode_property' => $this->testMode
                ],
                'credential_selection_logic' => [
                    'production_check' => $this->currentEnv === 'production',
                    'credentials_used' => $this->forteEnvironment === 'production' ? 'PROD_CREDENTIALS' : 'SANDBOX_CREDENTIALS'
                ],
                'sandbox_credentials' => [
                    'api_access_id' => getenv('CSG_FORTE_API_ACCESS_ID'),
                    'secure_key_set' => !empty(getenv('CSG_FORTE_SECURE_KEY')),
                    'location_id' => getenv('CSG_FORTE_LOCATION_ID')
                ],
                'production_credentials' => [
                    'api_access_id' => getenv('CSG_FORTE_API_ACCESS_ID_PROD'),
                    'secure_key_set' => !empty(getenv('CSG_FORTE_SECURE_KEY_PROD')),
                    'location_id' => getenv('CSG_FORTE_LOCATION_ID_PROD')
                ],
                'final_credentials_in_use' => [
                    'api_access_id' => $this->forteApiAccessId,
                    'secure_key_preview' => $this->forteSecureKey ? '***' . substr($this->forteSecureKey, -4) : 'NOT_SET',
                    'location_id' => $this->forteLocationId,
                    'environment' => $this->forteEnvironment
                ]
            ]);

            // Debug: Function entry
            $debugObj->debug_start = [
                'request_received' => !empty($request),
                'function_called' => 'createPaymentButton',
                'timestamp' => date('Y-m-d H:i:s'),
                'step' => 'ENTRY'
            ];

            // Debug: Get UTC time
            $debugObj->debug_utc_start = ['step' => 'UTC_TIME_START'];
            $utcTime = $this->getForteUTCTime();
            $debugObj->debug_utc = [
                'utc_time_generated' => $utcTime,
                'step' => 'UTC_TIME_SUCCESS'
            ];

            // Debug: Request parameters
            $debugObj->debug_params = [
                'paymentType' => isset($request->paymentType) ? $request->paymentType : 'NOT_SET',
                'allowEdit' => isset($request->allowEdit) ? $request->allowEdit : 'NOT_SET',
                'totalAmount' => isset($request->totalAmount) ? $request->totalAmount : 'NOT_SET',
                'invoiceLabels' => isset($request->invoiceLabels) ? $request->invoiceLabels : 'NOT_SET',
                'orderNumber' => isset($request->orderNumber) ? $request->orderNumber : 'NOT_SET',
                'contactId' => isset($request->contactId) ? $request->contactId : 'NOT_SET',
                'step' => 'PARAMS_PARSED'
            ];

            // Debug: Order number logic
            $orderNumber = !empty($request->orderNumber) ? $request->orderNumber : 'ORDER_' . time() . '_' . rand(1000, 9999);
            $debugObj->debug_order = [
                'order_number_final' => $orderNumber,
                'step' => 'ORDER_NUMBER_SET'
            ];

            // Debug: Amount calculation start
            $debugObj->debug_amount_start = [
                'allowEdit' => isset($request->allowEdit) ? $request->allowEdit : false,
                'request_totalAmount' => isset($request->totalAmount) ? $request->totalAmount : 'NOT_SET',
                'step' => 'AMOUNT_CALC_START'
            ];

            // Handle amount calculation based on payment type
            if (isset($request->allowEdit) && $request->allowEdit) {
                // Custom payment with editable amount - need range calculation
                $debugObj->debug_amount_custom = ['step' => 'CUSTOM_AMOUNT_PATH'];

                $maxAmount = isset($request->totalAmount) ? $request->totalAmount : 0;

                // Check if we have invoice labels for more accurate calculation
                if (isset($request->invoiceLabels) && is_array($request->invoiceLabels)) {
                    $invoiceTotal = 0;
                    foreach ($request->invoiceLabels as $invoice) {
                        $invoiceAmount = isset($invoice['amount']) ? $invoice['amount'] : 0;
                        $invoiceTotal += $invoiceAmount;
                    }
                    $maxAmount = $invoiceTotal;
                }

                // Convert cents to dollars for range
                $maxAmountFormatted = number_format($maxAmount / 100, 2, '.', '');
                $defaultAmount = $maxAmountFormatted; // Default to full amount

                // Create range string: "min-max;default"
                $totalAmount = "0.01-{$maxAmountFormatted};{$defaultAmount}";

                $debugObj->debug_amount_range = [
                    'max_amount_cents' => $maxAmount,
                    'max_amount_formatted' => $maxAmountFormatted,
                    'total_amount_range' => $totalAmount,
                    'step' => 'RANGE_CALCULATED'
                ];
            } else {
                // Fixed payment - exact amount only
                $debugObj->debug_amount_fixed = ['step' => 'FIXED_AMOUNT_PATH'];
                $totalAmount = number_format($request->totalAmount / 100, 2, '.', '');

                $debugObj->debug_amount_fixed_result = [
                    'total_amount_fixed' => $totalAmount,
                    'step' => 'FIXED_CALCULATED'
                ];
            }

            $debugObj->debug_amount_final = [
                'totalAmount' => $totalAmount,
                'step' => 'AMOUNT_FINAL'
            ];

            // Debug: Signature creation
            $debugObj->debug_signature_start = ['step' => 'SIGNATURE_START'];

            // 🔥 LOG SIGNATURE STRING COMPONENTS BEFORE BUILDING 🔥
            $this->sendDebugLog('SIGNATURE_STRING_COMPONENTS', [
                'api_access_id' => $this->forteApiAccessId,
                'method' => 'sale',
                'version_number' => '2.0',
                'total_amount' => $totalAmount,
                'utc_time' => $utcTime,
                'order_number' => $orderNumber,
                'customer_token' => '', // empty
                'paymethod_token' => '', // empty
                'documentation_format' => 'api_access_id|method|version_number|total_amount|utc_time|order_number|customer_token|paymethod_token'
            ]);

            // Create signature string according to Forte documentation
            $signatureString = $this->forteApiAccessId . '|sale|2.0|' . $totalAmount . '|' . $utcTime . '|' . $orderNumber . '||';

            // 🔥 LOG COMPLETE SIGNATURE STRING 🔥
            $this->sendDebugLog('SIGNATURE_STRING_BUILT', [
                'signature_string' => $signatureString,
                'signature_string_length' => strlen($signatureString),
                'pipe_count' => substr_count($signatureString, '|'),
                'expected_pipe_count' => 7, // Should have 7 pipes for 8 components
                'string_breakdown' => explode('|', $signatureString)
            ]);

            $debugObj->debug_signature_string = [
                'signature_string' => $signatureString,
                'step' => 'SIGNATURE_STRING_CREATED'
            ];

            // Generate secure signature on server
            $signature = $this->generateSignature($signatureString);

            $debugObj->debug_signature_final = [
                'signature_generated' => !empty($signature),
                'signature_length' => strlen($signature),
                'step' => 'SIGNATURE_GENERATED'
            ];

            // Debug: Button data creation
            $debugObj->debug_button_start = ['step' => 'BUTTON_DATA_START'];

            $buttonData = [
                'api_access_id' => $this->forteApiAccessId,
                'location_id' => $this->forteLocationId,
                'method' => 'sale',
                'version_number' => '2.0',
                'total_amount' => $totalAmount,
                'utc_time' => (string)$utcTime,
                'order_number' => $orderNumber,
                'hash_method' => 'sha256',
                'signature' => $signature,
                'callback' => 'oncallback',
                'environment' => $this->forteEnvironment,
                'script_url' => $this->testMode
                    ? "https://sandbox.forte.net/checkout/v2/js"
                    : "https://checkout.forte.net/v2/js"
            ];

            $debugObj->debug_button_data = [
                'button_data_created' => true,
                'button_data_keys' => array_keys($buttonData),
                'step' => 'BUTTON_DATA_CREATED'
            ];

            // Debug: Final response preparation
            $debugObj->debug_response_prep = ['step' => 'RESPONSE_PREP'];

            $returnObj = new stdClass();
            $returnObj->success = true;
            $returnObj->buttonData = $buttonData;
            $returnObj->test_mode = $this->testMode;

            // Add all debug info to successful response
            $returnObj->debug_info = $debugObj;
            $returnObj->message = 'Payment button created successfully';

            $debugObj->debug_final = [
                'return_obj_created' => true,
                'step' => 'SUCCESS_COMPLETE'
            ];

            return $this->sb->sendData($returnObj, 1);

        } catch (Exception $e) {
            // Enhanced error debugging
            $debugObj->debug_error = [
                'exception_message' => $e->getMessage(),
                'exception_file' => $e->getFile(),
                'exception_line' => $e->getLine(),
                'exception_trace' => $e->getTraceAsString(),
                'step' => 'EXCEPTION_CAUGHT'
            ];

            $errorObj = new stdClass();
            $errorObj->success = false;
            $errorObj->message = 'Failed to create payment button: ' . $e->getMessage();
            $errorObj->error = $e->getMessage();
            $errorObj->debug_info = $debugObj;

            return $this->sb->sendData($errorObj, 0);
        }
    }

    /**
     * Verify callback signature from CSG Forte
     */
    public function verifyCallbackSignature($request)
    {
        try {
            $callbackData = $request->callbackData;
            $receivedSignature = $callbackData['signature'];

            // Remove signature from data for verification
            unset($callbackData['signature']);

            // Generate expected signature
            $expectedSignature = $this->generateSignature($callbackData);

            $returnObj = new stdClass();
            $returnObj->success = hash_equals($expectedSignature, $receivedSignature);
            $returnObj->expected_signature = $expectedSignature;
            $returnObj->received_signature = $receivedSignature;

            return $this->sb->sendData($returnObj, 1);

        } catch (Exception $e) {
            return $this->sb->sendData($e, 0);
        }
    }

    /**
     * Process successful payment callback
     */
    public function processPaymentCallback($request)
    {
        try {
            // Verify signature first
            $signatureVerification = $this->verifyCallbackSignature($request);

            if (!$signatureVerification->success) {
                throw new Exception('Invalid callback signature');
            }

            // Extract payment data from callback
            $paymentData = $request->paymentData;
            $billingEmail = $paymentData->billing_address->email ?? null;
            $payerName = $paymentData->card->name_on_card ?? $paymentData->echeck->account_holder_name ?? 'Unknown';

            // Handle guest contact creation/lookup
            $contactResult = $this->handlePaymentContact($billingEmail, $payerName, $paymentData, $request->mainContactId);

            // Create payment record
            $paymentResult = $this->createPaymentRecord($paymentData, $contactResult->contactId, $request);

            $returnObj = new stdClass();
            $returnObj->success = true;
            $returnObj->message = 'Payment processed successfully';
            $returnObj->contactId = $contactResult->contactId;
            $returnObj->isGuestContact = $contactResult->isGuest;
            $returnObj->paymentId = $paymentResult->paymentId;
            $returnObj->test_mode = $this->testMode;

            return $this->sb->sendData($returnObj, 1);

        } catch (Exception $e) {
            return $this->sb->sendData($e, 0);
        }
    }

    /**
     * Handle contact creation/lookup for payment processing
     */
    private function handlePaymentContact($email, $payerName, $paymentData, $mainContactId)
    {
        $returnObj = new stdClass();

        if (!$email) {
            // No email provided, link to main contact
            $returnObj->contactId = $mainContactId;
            $returnObj->isGuest = false;
            return $returnObj;
        }

        // Search for existing contact by email
        $existingContact = $this->findContactByEmail($email);

        if ($existingContact) {
            // Found existing contact
            $returnObj->contactId = $existingContact['id'];
            $returnObj->isGuest = false;
            return $returnObj;
        }

        // Create guest contact
        $guestContact = $this->createGuestContact($email, $payerName, $paymentData, $mainContactId);
        $returnObj->contactId = $guestContact['id'];
        $returnObj->isGuest = true;

        return $returnObj;
    }

    /**
     * Find contact by email address
     */
    private function findContactByEmail($email)
    {
        // Search contact_info for email addresses
        $contactInfos = $this->sb->pgObjects->getWhere('contact_info', [
            'info' => $email,
            'childObjs' => ['type' => true]
        ]);

        foreach ($contactInfos as $contactInfo) {
            if ($contactInfo['type'] && $contactInfo['type']['data_type'] === 'email') {
                // Found email contact info, get the parent contact
                $contacts = $this->sb->pgObjects->getWhere('contacts', [
                    'contact_info' => $contactInfo['id']
                ]);

                if (!empty($contacts)) {
                    return $contacts[0];
                }
            }
        }

        return null;
    }

    /**
     * Create guest contact for payment processing
     */
    private function createGuestContact($email, $payerName, $paymentData, $mainContactId)
    {
        // Parse name
        $nameParts = explode(' ', trim($payerName));
        $firstName = $nameParts[0] ?? 'Guest';
        $lastName = count($nameParts) > 1 ? end($nameParts) : 'Payer';

        // Get main contact to inherit company/group relationships
        $mainContact = $this->sb->pgObjects->getById('contacts', $mainContactId);

        // Create guest contact
        $guestContactData = [
            'fname' => $firstName,
            'lname' => $lastName,
            'company' => $mainContact['company'] ?? null,
            'type' => $mainContact['type'] ?? null, // Inherit contact type
            'data_source' => 'csg_forte_guest', // Mark as CSG Forte guest
            'notes' => 'Guest contact created from CSG Forte payment by ' . $payerName
        ];

        $guestContact = $this->sb->pgObjects->create('contacts', $guestContactData);

        // Create contact info records
        $this->createGuestContactInfo($guestContact['id'], $email, $paymentData);

        return $guestContact;
    }

    /**
     * Create contact info for guest contact
     */
    private function createGuestContactInfo($contactId, $email, $paymentData)
    {
        $contactInfoIds = [];

        // Create email contact info
        if ($email) {
            $emailInfo = $this->sb->pgObjects->create('contact_info', [
                'info' => $email,
                'type' => $this->getContactInfoTypeId('email'),
                'is_primary' => true
            ]);
            $contactInfoIds[] = $emailInfo['id'];
        }

        // Create phone contact info if available
        $phone = $paymentData->billing_address->phone ?? null;
        if ($phone) {
            $phoneInfo = $this->sb->pgObjects->create('contact_info', [
                'info' => $phone,
                'type' => $this->getContactInfoTypeId('phone')
            ]);
            $contactInfoIds[] = $phoneInfo['id'];
        }

        // Create address contact info if available
        $address = $paymentData->billing_address->physical_address ?? null;
        if ($address) {
            $addressInfo = $this->sb->pgObjects->create('contact_info', [
                'street' => $address->street_line1 ?? '',
                'street2' => $address->street_line2 ?? '',
                'city' => $address->locality ?? '',
                'state' => $address->region ?? '',
                'zip' => $address->postal_code ?? '',
                'type' => $this->getContactInfoTypeId('address')
            ]);
            $contactInfoIds[] = $addressInfo['id'];
        }

        // Link contact info to contact
        if (!empty($contactInfoIds)) {
            $this->sb->pgObjects->update('contacts', [
                'id' => $contactId,
                'contact_info' => $contactInfoIds
            ]);
        }
    }

    /**
     * Get contact info type ID by data type
     */
    private function getContactInfoTypeId($dataType)
    {
        $types = $this->sb->pgObjects->getWhere('contact_info_types', [
            'data_type' => $dataType
        ]);

        return !empty($types) ? $types[0]['id'] : null;
    }

    /**
     * Create payment record in database
     */
    private function createPaymentRecord($paymentData, $contactId, $request)
    {
        // TODO: Implement payment record creation
        // Similar to iCheckGatewayService patterns

        $returnObj = new stdClass();
        $returnObj->paymentId = 'temp_payment_id';
        return $returnObj;
    }

    /**
     * Get service configuration for frontend
     */
    public function getServiceConfig($request)
    {
        $returnObj = new stdClass();
        $returnObj->success = true;
        $returnObj->environment = $this->forteEnvironment;
        $returnObj->test_mode = $this->testMode;
        $returnObj->script_url = $this->testMode
            ? "https://sandbox.forte.net/checkout/v2/js"
            : "https://checkout.forte.net/v2/js";

        return $this->sb->sendData($returnObj, 1);
    }

    /**
     * Process CSG Forte payment callback and create Bento payment records
     * PHASE 2 IMPLEMENTATION - Credit Card payments first
     */
    /**
     * Simple test method to verify service is working
     */
    public function testService()
    {
        $returnObj = new stdClass();
        $returnObj->success = true;
        $returnObj->message = "CSGForteService is working!";
        $returnObj->timestamp = date('Y-m-d H:i:s');
        return $this->sb->sendData($returnObj, 1);
    }

    public function processCSGFortePayment($csgForteCallback)
    {
        // 🔥 ALWAYS LOG ALL CSG FORTE RESPONSES 🔥
        $this->sendDebugLog('CSG_FORTE_CALLBACK_RECEIVED', [
            'raw_callback' => $csgForteCallback,
            'callback_type' => gettype($csgForteCallback),
            'callback_keys' => is_array($csgForteCallback) ? array_keys($csgForteCallback) : 'not_array'
        ], ['source' => 'processCSGFortePayment']);

        try {
            // Convert object to array for easier access
            $callback = (array) $csgForteCallback;

            // Extract xdata fields
            $invoiceIds = explode(',', $callback['xdata_2']);
            $projectId = intval($callback['xdata_3']);
            $invoiceValuePaid = intval($callback['xdata_4']);
            $feesTotal = intval($callback['xdata_5']);

            // Get proposal and project
            $proposal = $this->sb->pgObjects->getById('proposals', $projectId);
            if (!$proposal) {
                throw new Exception("Proposal not found: " . $projectId);
            }

            $project = $this->sb->pgObjects->getById('groups', $proposal['main_object'], 1);
            if (!$project) {
                throw new Exception("Project not found: " . $proposal['main_object']);
            }

            $allInvoices = $this->sb->pgObjects->where('invoices', ['related_object' => $proposal['id']]);

            // Determine selected invoices based on payment type (xdata_1)
            $paymentType = $callback['xdata_1']; // "fixed" or "custom"

            if ($paymentType === 'fixed') {
                // Inline payment - use specific invoice from xdata_2
                $selectedInvoices = array_filter($allInvoices, function($inv) use($invoiceIds) {
                    return $inv['id'] == $invoiceIds[0];
                });
            } else {
                // Custom payment - use all unpaid invoices sorted by due date
                $selectedInvoices = array_filter($allInvoices, function($inv) {
                    return $inv['balance'] > 0;
                });

                // Sort by due date (earliest first) - COPIED FROM Stripe/iCheckGateway
                usort($selectedInvoices, function($a, $b) {
                    return strtotime($a["due_date"]) - strtotime($b["due_date"]);
                });
            }

            // Sort by due date (earliest first) - COPIED FROM Stripe/iCheckGateway
            usort($selectedInvoices, function($a, $b) {
                return strtotime($a["due_date"]) - strtotime($b["due_date"]);
            });

            // Check for existing paid invoices
            $hasAPaidInvoice = false;
            foreach ($allInvoices as $invoice) {
                if ($invoice['paid'] > 0) {
                    $hasAPaidInvoice = true;
                    break;
                }
            }

            // Get unpaid remaining invoices
            $unpaidRemaining = $this->sb->pgObjects->where('invoices', [
                'related_object' => $proposal['id'],
                'balance' => [
                    'type' => 'greater_than',
                    'value' => 0
                ],
            ]);

            // Sort remaining by closest due date
            usort($unpaidRemaining, function($a, $b) {
                return strtotime($a["due_date"]) - strtotime($b["due_date"]);
            });

            // Filter out the selected invoices from the unpaid remaining list
            $selectedIds = array_column($selectedInvoices, 'id');
            $unpaidRemaining = array_filter($unpaidRemaining, function($invoice) use($selectedIds) {
                return !in_array($invoice['id'], $selectedIds);
            });

            $paymentOwnerId = is_null($_COOKIE['uid']) ? $project['main_contact'] : $_COOKIE['uid'];
            $optionalEmail = $callback['billing_email_address'];

            // Determine payment type
            $paymentType = $this->determinePaymentType($callback);

            $logPaymentsArray = array();
            $logInvoicesArray = array();

            // USE THE FULL ALLOCATION ENGINE WITH TWO WHILE LOOPS
            return $this->executeAllocationEngine(
                $invoiceValuePaid,
                $selectedInvoices,
                $unpaidRemaining,
                $proposal,
                $callback,
                $paymentOwnerId,
                $optionalEmail,
                $logPaymentsArray,
                $logInvoicesArray,
                $hasAPaidInvoice,
                $project['type'],
                $paymentType
            );

        } catch (Exception $e) {

            // COPIED EXACTLY FROM STRIPE SERVICE - Get proposal and project
            error_log("CSG STEP 12: About to get proposal with ID: " . $projectId);
            $proposal = $this->sb->pgObjects->getById('proposals', $projectId);
            error_log("CSG STEP 13: Proposal retrieved: " . ($proposal ? "SUCCESS" : "FAILED"));
            if (!$proposal) {
                throw new Exception("Proposal not found: " . $projectId);
            }

            error_log("CSG STEP 14: About to get project with ID: " . $proposal['main_object']);
            $project = $this->sb->pgObjects->getById('groups', $proposal['main_object'], 1);
            error_log("CSG STEP 15: Project retrieved: " . ($project ? "SUCCESS" : "FAILED"));
            if (!$project) {
                throw new Exception("Project not found: " . $proposal['main_object']);
            }

            error_log("CSG STEP 16: About to get all invoices for proposal: " . $proposal['id']);
            $allInvoices = $this->sb->pgObjects->where('invoices', ['related_object' => $proposal['id']]);
            error_log("CSG STEP 17: All invoices count: " . count($allInvoices));

            $projectType = $project['type'];
            error_log("CSG STEP 18: Project type retrieved");

            // COPIED EXACTLY FROM STRIPE SERVICE - Determine selected invoices
            if (count($invoiceIds) == 1 && !empty($invoiceIds[0])) {
                // Single invoice payment
                $selectedInvoices = array_filter($allInvoices, function($inv) use($invoiceIds) {
                    return $inv['id'] == $invoiceIds[0];
                });
            } else {
                // Multi-invoice or no specific invoice - use all unpaid invoices
                $selectedInvoices = array_filter($allInvoices, function($inv) {
                    return $inv['balance'] > 0;
                });
            }

            // COPIED EXACTLY FROM STRIPE SERVICE - Sort by due date
            usort($selectedInvoices, function($a, $b) {
                return strtotime($a["due_date"]) - strtotime($b["due_date"]);
            });

            // COPIED EXACTLY FROM STRIPE SERVICE - Check for existing paid invoices
            $hasAPaidInvoice = false;
            foreach ($allInvoices as $invoice) {
                if ($invoice['paid'] > 0) {
                    $hasAPaidInvoice = true;
                    break;
                }
            }

            // COPIED EXACTLY FROM STRIPE SERVICE - Get unpaid remaining invoices
            $unpaidRemaining = $this->sb->pgObjects->where('invoices', [
                'related_object' => $proposal['id'],
                'balance' => [
                    'type' => 'greater_than',
                    'value' => 0
                ],
            ]);

            // Sort remaining by closest due date
            usort($unpaidRemaining, function($a, $b) {
                return strtotime($a["due_date"]) - strtotime($b["due_date"]);
            });

            // Filter out the selected invoices from the unpaid remaining list
            $selectedIds = array_column($selectedInvoices, 'id');
            $unpaidRemaining = array_filter($unpaidRemaining, function($invoice) use($selectedIds) {
                return !in_array($invoice['id'], $selectedIds);
            });

            $paymentOwnerId = is_null($_COOKIE['uid']) ? $project['main_contact'] : $_COOKIE['uid'];
            $optionalEmail = $csgForteCallback['billing_email_address'];

            // DETERMINE PAYMENT TYPE - Credit Card vs ACH (COPIED FROM iCheckGateway PATTERNS)
            $paymentType = $this->determinePaymentType($csgForteCallback);

            $logPaymentsArray = array();
            $logInvoicesArray = array();

            // COPIED EXACTLY FROM STRIPE/iCheckGateway SERVICES - Execute the two while loops
            return $this->executeAllocationEngine(
                $invoiceValuePaid,
                $selectedInvoices,
                $unpaidRemaining,
                $proposal,
                $csgForteCallback,
                $paymentOwnerId,
                $optionalEmail,
                $logPaymentsArray,
                $logInvoicesArray,
                $hasAPaidInvoice,
                $projectType,
                $paymentType
            );

        } catch (Exception $e) {
            // 🔥 ALWAYS LOG ALL CSG FORTE ERRORS 🔥
            $this->sendDebugLog('CSG_FORTE_ERROR', [
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'error_trace' => $e->getTraceAsString(),
                'callback_data' => $csgForteCallback ?? 'not_available'
            ], ['source' => 'processCSGFortePayment_exception']);

            $returnObj = new stdClass();
            $returnObj->success = false;
            $returnObj->message = "Error: " . $e->getMessage();
            $returnObj->file = $e->getFile();
            $returnObj->line = $e->getLine();
            return $this->sb->sendData($returnObj, 0);
        }
    }

    /**
     * Determine payment type for proper field routing
     * COPIED FROM iCheckGateway PATTERNS
     */
    private function determinePaymentType($callbackData) {
        if ($callbackData['method_used'] === 'echeck') {
            // ACH/eCheck payment - use iCheckGateway patterns
            return [
                'type' => 'ACH',
                'service_pattern' => 'iCheckGateway',
                'payment_id_field' => 'icg_payment_id'
            ];
        } else {
            // Credit cards: visa, mast, disc, amex - use Stripe patterns
            return [
                'type' => 'CREDIT_CARD',
                'service_pattern' => 'Stripe',
                'payment_id_field' => 'stripeId'
            ];
        }
    }

    /**
     * Parse CSG Forte callback and extract xdata fields
     */
    private function parseCSGForteCallback($csgForteCallback)
    {
        // Extract all data from CSG callback including xdata fields
        return [
            'transaction_id' => $csgForteCallback['trace_number'],
            'method_used' => $csgForteCallback['method_used'],
            'total_amount_csg' => floatval($csgForteCallback['total_amount']) * 100, // Convert to cents
            'payment_type' => $csgForteCallback['xdata_1'],     // "fixed" or "custom"
            'invoice_ids' => explode(',', $csgForteCallback['xdata_2']),  // Invoice IDs array
            'project_id' => $csgForteCallback['xdata_3'],       // Project/proposal ID
            'base_amount' => intval($csgForteCallback['xdata_4']),       // Base amount (cents, no fees)
            'fee_amount' => intval($csgForteCallback['xdata_5']),        // Fee amount (cents)
            'total_amount' => intval($csgForteCallback['xdata_6']),      // Total amount (cents, with fees)
            'fee_config' => $csgForteCallback['xdata_7'],       // Fee percentage
            'tax_rate' => $csgForteCallback['xdata_8'],         // Tax rate
            'customer_info' => [
                'name' => $csgForteCallback['billing_name'],
                'email' => $csgForteCallback['billing_email_address'],
                'last_4' => $csgForteCallback['last_4']
            ]
        ];
    }

    /**
     * Process Credit Card payment using Stripe patterns
     * LIFTED EXACTLY from StripeService allocation engine
     */
    private function processCreditCardPayment($callbackData)
    {
        // Extract payment data - NO CALCULATIONS, use xdata values directly
        $invoiceValuePaid = $callbackData['base_amount']; // Use pre-calculated base amount
        $feesTotal = $callbackData['fee_amount']; // Use pre-calculated fees
        $paymentTotal = $callbackData['total_amount']; // Use pre-calculated total

        // Get proposal and project data
        $proposal = $this->sb->pgObjects->getById('proposals', $callbackData['project_id']);
        $project = $this->sb->pgObjects->getById('groups', $proposal['main_object'], 1);

        // Get selected invoices and sort by due date (LIFTED from Stripe)
        $selectedInvoices = $this->sb->pgObjects->getById('invoices', $callbackData['invoice_ids'], 1);
        usort($selectedInvoices, function($a, $b) {
            return strtotime($a["due_date"]) - strtotime($b["due_date"]);
        });

        // Identify if first payment for possible state transition (LIFTED from Stripe)
        $allInvoices = $this->sb->pgObjects->where('invoices', ['related_object' => $proposal['id']]);
        $hasAPaidInvoice = false;
        foreach ($allInvoices as $i => $inv) {
            if ($inv['amount'] > 0 && $inv['balance'] < $inv['amount']) {
                $hasAPaidInvoice = true;
            }
        }

        // Get unpaid remaining invoices (LIFTED from Stripe)
        $unpaidRemaining = $this->sb->pgObjects->where('invoices', [
            'related_object' => $proposal['id'],
            'balance' => [
                'type' => 'greater_than',
                'value' => 0
            ],
        ]);

        // Sort remaining by closest due date (LIFTED from Stripe)
        usort($unpaidRemaining, function($a, $b) {
            return strtotime($a["due_date"]) - strtotime($b["due_date"]);
        });

        // Filter out selected invoices from unpaid remaining (LIFTED from Stripe)
        $selectedIds = array_column($selectedInvoices, 'id');
        $unpaidRemaining = array_filter($unpaidRemaining, function($invoice) use($selectedIds) {
            return !in_array($invoice['id'], $selectedIds);
        });

        $projectType = $project['type'];
        $paymentOwnerId = is_null($_COOKIE['uid']) ? $project['main_contact'] : $_COOKIE['uid'];
        $optionalEmail = $callbackData['customer_info']['email']; // Use CSG customer email

        $logPaymentsArray = array();
        $logInvoicesArray = array();

        // Execute allocation engine - FIRST WHILE LOOP (LIFTED EXACTLY from Stripe)
        return $this->executeAllocationEngine(
            $invoiceValuePaid,
            $selectedInvoices,
            $unpaidRemaining,
            $proposal,
            $callbackData,
            $paymentOwnerId,
            $optionalEmail,
            $logPaymentsArray,
            $logInvoicesArray,
            $hasAPaidInvoice,
            $projectType
        );
    }

    /**
     * Execute payment allocation engine - LIFTED EXACTLY from Stripe service
     * Two while loops that distribute payment across invoices
     */
    private function executeAllocationEngine(
        $invoiceValuePaid,
        $selectedInvoices,
        $unpaidRemaining,
        $proposal,
        $callbackData,
        $paymentOwnerId,
        $optionalEmail,
        $logPaymentsArray,
        $logInvoicesArray,
        $hasAPaidInvoice,
        $projectType,
        $paymentType
    ) {
        // FIRST WHILE LOOP - Process selected invoices (LIFTED EXACTLY from Stripe)
        while ($invoiceValuePaid > 0):
            if(empty($selectedInvoices))
                break;

            foreach($selectedInvoices as $i => &$invoice){
                $currentInvoice = $invoice;
                $lastInvoice = end($selectedInvoices);
                $payment = 0;
                $paymentObject = array();

                if ( $invoiceValuePaid == 0 || $invoice['balance'] == 0 ) break 2;

                if ($invoiceValuePaid < $invoice['balance']) {
                    // Create payment object - partial payment
                    $payment = $invoiceValuePaid;

                    // USE ACTUAL FEE FROM CSG CALLBACK
                    $transactionFee = intval($callbackData['xdata_5']); // Total fees from CSG

                    // Reconcile invoiceValuePaid amount
                    $invoiceValuePaid = $invoiceValuePaid - $invoiceValuePaid;

                    // Create payment object with correct ID field based on payment type
                    $paymentObj = array(
                        'main_object' => $proposal['id'],
                        'amount' => $payment,
                        'fee' => $transactionFee,
                        'invoice' => $invoice["id"],
                        'test_payment' => $this->testMode,
                        'manual_payment' => false,
                        'owner' => $paymentOwnerId,
                        'optional_receipt_email' => $optionalEmail
                    );

                    // Add payment ID field based on payment type (COPIED FROM iCheckGateway/Stripe PATTERNS)
                    $paymentObj[$paymentType['payment_id_field']] = $callbackData['trace_number'];

                    // PIPEDREAM LOG: Before payment creation
                    $this->sendDebugLog('PAYMENT_CREATE_BEFORE', $paymentObj, ['loop' => 'first', 'payment_type' => 'partial']);

                    // Create payment object
                    $paymentObject = $this->sb->pgObjects->create('payments', $paymentObj);

                    // PIPEDREAM LOG: After payment creation
                    $this->sendDebugLog('PAYMENT_CREATE_AFTER', $paymentObject, ['loop' => 'first', 'payment_type' => 'partial']);

                    $paymentsArray = $invoice["payments"] == null ? [] : $invoice["payments"];
                    array_push($paymentsArray, $paymentObject["id"]);

                    $invoice["payments"] = $paymentsArray;
                    $invoice["balance"] = $invoice['balance'] - $payment;
                    $invoice["paid"] += $payment;

                    // PIPEDREAM LOG: Before invoice update
                    $this->sendDebugLog('INVOICE_UPDATE_BEFORE', $invoice, ['loop' => 'first', 'payment_type' => 'partial']);

                    $updatedInvoice = $this->sb->pgObjects->update('invoices', $invoice);

                    // PIPEDREAM LOG: After invoice update
                    $this->sendDebugLog('INVOICE_UPDATE_AFTER', $updatedInvoice, ['loop' => 'first', 'payment_type' => 'partial']);

                } else {
                    // Reconcile invoiceValuePaid amount
                    $invoiceValuePaid = $invoiceValuePaid - $invoice['balance'];

                    // Create payment object - full payment
                    $payment = $invoice['balance'];
                    $invoice['balance'] = $invoice['balance'] - $payment;

                    if ( end($selectedInvoices)['id'] == $currentInvoice['id'] && $invoiceValuePaid > 0 && empty($unpaidRemaining) ) {
                        $payment = $invoiceValuePaid + $payment;
                        $invoiceValuePaid = $invoiceValuePaid - $invoiceValuePaid;
                    }

                    // USE ACTUAL FEE FROM CSG CALLBACK
                    $transactionFee = intval($callbackData['xdata_5']); // Total fees from CSG

                    // Create payment object with correct ID field based on payment type
                    $paymentObj = array(
                        'main_object' => $proposal['id'],
                        'amount' => $payment,
                        'fee' => $transactionFee,
                        'invoice' => $invoice["id"],
                        'test_payment' => $this->testMode,
                        'manual_payment' => false,
                        'owner' => $paymentOwnerId,
                        'optional_receipt_email' => $optionalEmail
                    );

                    // Add payment ID field based on payment type (COPIED FROM iCheckGateway/Stripe PATTERNS)
                    $paymentObj[$paymentType['payment_id_field']] = $callbackData['trace_number'];

                    // PIPEDREAM LOG: Before payment creation
                    $this->sendDebugLog('PAYMENT_CREATE_BEFORE', $paymentObj, ['loop' => 'first', 'payment_type' => 'full']);

                    // Create payment object
                    $paymentObject = $this->sb->pgObjects->create('payments', $paymentObj);

                    // PIPEDREAM LOG: After payment creation
                    $this->sendDebugLog('PAYMENT_CREATE_AFTER', $paymentObject, ['loop' => 'first', 'payment_type' => 'full']);

                    $paymentsArray = $invoice["payments"] == null ? [] : $invoice["payments"];
                    array_push($paymentsArray, $paymentObject["id"]);

                    $invoice["payments"] = $paymentsArray;
                    $invoice['paid'] += $payment;

                    // PIPEDREAM LOG: Before invoice update
                    $this->sendDebugLog('INVOICE_UPDATE_BEFORE', $invoice, ['loop' => 'first', 'payment_type' => 'full']);

                    $updatedInvoice = $this->sb->pgObjects->update('invoices', $invoice);

                    // PIPEDREAM LOG: After invoice update
                    $this->sendDebugLog('INVOICE_UPDATE_AFTER', $updatedInvoice, ['loop' => 'first', 'payment_type' => 'full']);
                }

                array_push($logPaymentsArray, $paymentObject);
                array_push($logInvoicesArray, $updatedInvoice);

                unset($payment);
                unset($selectedInvoices[$i]);
            }
        endwhile;

        // Continue with second while loop in next chunk...
        return $this->executeSecondAllocationLoop(
            $invoiceValuePaid,
            $unpaidRemaining,
            $proposal,
            $callbackData,
            $paymentOwnerId,
            $optionalEmail,
            $logPaymentsArray,
            $logInvoicesArray,
            $hasAPaidInvoice,
            $projectType,
            $paymentType
        );
    }

    /**
     * Execute second allocation loop for unpaid remaining invoices
     * LIFTED EXACTLY from Stripe service
     */
    private function executeSecondAllocationLoop(
        $invoiceValuePaid,
        $unpaidRemaining,
        $proposal,
        $callbackData,
        $paymentOwnerId,
        $optionalEmail,
        $logPaymentsArray,
        $logInvoicesArray,
        $hasAPaidInvoice,
        $projectType,
        $paymentType
    ) {
        // SECOND WHILE LOOP - Process unpaid remaining invoices (LIFTED EXACTLY from Stripe)
        while ($invoiceValuePaid > 0):
            if(empty($unpaidRemaining))
                break;

            foreach($unpaidRemaining as $i => &$invoice){
                $currentInvoice = $invoice;
                $payment = 0;
                $paymentObject = array();

                if ( $invoiceValuePaid == 0 ) break 2;

                if ($invoiceValuePaid < $invoice['balance']) {
                    // Create payment object - partial payment
                    $payment = $invoiceValuePaid;

                    // USE ACTUAL FEE FROM CSG CALLBACK
                    $transactionFee = intval($callbackData['xdata_5']); // Total fees from CSG

                    // Reconcile invoiceValuePaid amount
                    $invoiceValuePaid = $invoiceValuePaid - $invoiceValuePaid;

                    // Create payment object with correct ID field based on payment type
                    $paymentObj = array(
                        'main_object' => $proposal['id'],
                        'amount' => $payment,
                        'fee' => $transactionFee,
                        'invoice' => $invoice["id"],
                        'test_payment' => $this->testMode,
                        'manual_payment' => false,
                        'owner' => $paymentOwnerId,
                        'optional_receipt_email' => $optionalEmail
                    );

                    // Add payment ID field based on payment type (COPIED FROM iCheckGateway/Stripe PATTERNS)
                    $paymentObj[$paymentType['payment_id_field']] = $callbackData['trace_number'];

                    // PIPEDREAM LOG: Before payment creation
                    $this->sendDebugLog('PAYMENT_CREATE_BEFORE', $paymentObj, ['loop' => 'second', 'payment_type' => 'partial']);

                    // Create payment object
                    $paymentObject = $this->sb->pgObjects->create('payments', $paymentObj);

                    // PIPEDREAM LOG: After payment creation
                    $this->sendDebugLog('PAYMENT_CREATE_AFTER', $paymentObject, ['loop' => 'second', 'payment_type' => 'partial']);

                    $paymentsArray = $invoice["payments"] == null ? [] : $invoice["payments"];
                    array_push($paymentsArray, $paymentObject["id"]);

                    $invoice["payments"] = $paymentsArray;
                    $invoice["balance"] = $invoice['balance'] - $payment;
                    $invoice["paid"] += $payment;

                    // PIPEDREAM LOG: Before invoice update
                    $this->sendDebugLog('INVOICE_UPDATE_BEFORE', $invoice, ['loop' => 'second', 'payment_type' => 'partial']);

                    $updatedInvoice = $this->sb->pgObjects->update('invoices', $invoice);

                    // PIPEDREAM LOG: After invoice update
                    $this->sendDebugLog('INVOICE_UPDATE_AFTER', $updatedInvoice, ['loop' => 'second', 'payment_type' => 'partial']);

                } else {
                    // Reconcile invoiceValuePaid amount
                    $invoiceValuePaid = $invoiceValuePaid - $invoice['balance'];

                    // Create payment object - full payment
                    $payment = $invoice['balance'];
                    $invoice['balance'] = $invoice['balance'] - $payment;

                    // USE ACTUAL FEE FROM CSG CALLBACK
                    $transactionFee = intval($callbackData['xdata_5']); // Total fees from CSG

                    // Create payment object with correct ID field based on payment type
                    $paymentObj = array(
                        'main_object' => $proposal['id'],
                        'amount' => $payment,
                        'fee' => $transactionFee,
                        'invoice' => $invoice["id"],
                        'test_payment' => $this->testMode,
                        'manual_payment' => false,
                        'owner' => $paymentOwnerId,
                        'optional_receipt_email' => $optionalEmail
                    );

                    // Add payment ID field based on payment type (COPIED FROM iCheckGateway/Stripe PATTERNS)
                    $paymentObj[$paymentType['payment_id_field']] = $callbackData['trace_number'];

                    // PIPEDREAM LOG: Before payment creation
                    $this->sendDebugLog('PAYMENT_CREATE_BEFORE', $paymentObj, ['loop' => 'second', 'payment_type' => 'full']);

                    // Create payment object
                    $paymentObject = $this->sb->pgObjects->create('payments', $paymentObj);

                    // PIPEDREAM LOG: After payment creation
                    $this->sendDebugLog('PAYMENT_CREATE_AFTER', $paymentObject, ['loop' => 'second', 'payment_type' => 'full']);

                    $paymentsArray = $invoice["payments"] == null ? [] : $invoice["payments"];
                    array_push($paymentsArray, $paymentObject["id"]);

                    $invoice["payments"] = $paymentsArray;
                    $invoice['paid'] += $payment;

                    // PIPEDREAM LOG: Before invoice update
                    $this->sendDebugLog('INVOICE_UPDATE_BEFORE', $invoice, ['loop' => 'second', 'payment_type' => 'full']);

                    $updatedInvoice = $this->sb->pgObjects->update('invoices', $invoice);

                    // PIPEDREAM LOG: After invoice update
                    $this->sendDebugLog('INVOICE_UPDATE_AFTER', $updatedInvoice, ['loop' => 'second', 'payment_type' => 'full']);
                }

                array_push($logPaymentsArray, $paymentObject);
                array_push($logInvoicesArray, $updatedInvoice);

                unset($payment);
                unset($unpaidRemaining[$i]);
            }
        endwhile;

        // Handle state transitions and notifications
        return $this->finalizePaymentProcessing(
            $proposal,
            $callbackData,
            $logPaymentsArray,
            $logInvoicesArray,
            $hasAPaidInvoice,
            $projectType
        );
    }

    /**
     * Finalize payment processing with state transitions and notifications
     * LIFTED from Stripe service patterns
     */
    private function finalizePaymentProcessing(
        $proposal,
        $callbackData,
        $logPaymentsArray,
        $logInvoicesArray,
        $hasAPaidInvoice,
        $projectType
    ) {
        // Handle state transitions (COPIED EXACTLY from iCheckGateway/Stripe)
        $project = $this->sb->pgObjects->getById('groups', $proposal['main_object'], 1);

        if (is_int($projectType['onFirstFullPayment'])) {
            if (!$hasAPaidInvoice) {
                $transitionResponse = $this->sb->updateState(
                    $project['id'],
                    null,
                    $projectType['onFirstFullPayment'],
                    $this->sb->getUrl() . '/app/'. $project['instance'] .'#mystuff&1=o-project-'. $project['id'] .'-'. rawurlencode($project['name']),
                    '',
                    false,
                    0,
                    function($response) use($logPaymentsArray) {
                        // State transition callback
                    }
                );
            }
        }

        // Send email notifications (COPIED EXACTLY from iCheckGateway/Stripe)
        $this->sendPaymentNotifications($project, $proposal, $callbackData, $logPaymentsArray, $allInvoices);

        // Get updated invoices for response (LIFTED from existing services)
        $updatedInvoices = $this->sb->pgObjects->where('invoices', ['related_object' => $proposal['id']]);

        // Send notifications (TODO: implement in next iteration)
        // $this->sendManagerNotification($callbackData, $logPaymentsArray, $project);
        // $this->sendCustomerReceipt($callbackData, $logPaymentsArray, $project);

        // Return success response (LIFTED from existing services)
        $returnObj = new stdClass();
        $returnObj->success = true;
        $returnObj->invoices = $updatedInvoices;
        $returnObj->processedPaymentAmount = $callbackData['xdata_4']; // Use actual payment amount
        $returnObj->processedPayments = $logPaymentsArray;
        $returnObj->processedInvoices = $logInvoicesArray;
        $returnObj->paymentType = ($callbackData['method_used'] === 'echeck') ? 'ACH' : 'Credit Card';
        $returnObj->transactionId = $callbackData['trace_number']; // Use CSG transaction ID

        // PIPEDREAM LOG: Success completion
        $this->sendDebugLog('PROCESS_SUCCESS', [
            'payments_created' => count($logPaymentsArray),
            'invoices_updated' => count($logInvoicesArray),
            'total_processed' => $callbackData['xdata_4'],
            'transaction_id' => $callbackData['trace_number']
        ]);

        return $this->sb->sendData($returnObj, 1);
    }

    /**
     * Send debug data to Pipedream for real-time monitoring - ALWAYS ACTIVE
     */
    private function sendDebugLog($stage, $data, $context = []) {
        $debugData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'stage' => $stage,
            'service' => 'CSGForteService',
            'method' => debug_backtrace()[1]['function'] ?? 'unknown',
            'test_mode' => $this->testMode,
            'current_env' => $this->currentEnv,
            'forte_environment' => $this->forteEnvironment,
            'context' => $context,
            'data' => $data
        ];

        $this->httpPost("https://eomvpn0zjsy8add.m.pipedream.net", [
            'debug_data' => json_encode($debugData, JSON_PRETTY_PRINT)
        ]);
    }

    /**
     * Debug logging endpoint for frontend calls
     */
    public function debugLog($request) {
        $debugData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'source' => $request->source ?? 'unknown',
            'stage' => $request->stage ?? 'unknown',
            'data' => $request->data ?? [],
            'current_env' => $this->currentEnv,
            'forte_environment' => $this->forteEnvironment
        ];

        $this->httpPost("https://eomvpn0zjsy8add.m.pipedream.net", [
            'frontend_debug' => json_encode($debugData, JSON_PRETTY_PRINT)
        ]);

        return $this->sb->sendData(['success' => true], 1);
    }

    /**
     * HTTP POST helper for Pipedream logging
     */
    private function httpPost($url, $data) {
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_TIMEOUT, 5);
        $response = curl_exec($curl);
        curl_close($curl);
        return $response;
    }

    /**
     * Send payment notification emails
     * COPIED EXACTLY from iCheckGateway/Stripe services
     */
    private function sendPaymentNotifications($project, $proposal, $callbackData, $logPaymentsArray, $allInvoices) {
        // Calculate totals (COPIED from iCheckGateway)
        $paymentAmount = 0;
        $paymentFees = 0;
        foreach ($logPaymentsArray as $payment) {
            $paymentAmount += $payment['amount'];
            $paymentFees += $payment['fee'];
        }
        $totalPayment = $paymentAmount + $paymentFees;

        // Format amounts for display
        $paymentAmountFormatted = number_format($paymentAmount / 100, 2);
        $paymentFeesFormatted = number_format($paymentFees / 100, 2);
        $totalPaymentFormatted = number_format($totalPayment / 100, 2);

        // Get project details (COPIED from iCheckGateway)
        $projectName = $project['name'];
        $groupObjectUID = $project['object_uid'];
        $startDate = $project['start_date'];
        $proposalId = $proposal['id'];
        $instanceName = $project['instance'];
        $date = date('m/d/Y g:i A');

        // Build project URL (COPIED from iCheckGateway)
        $urlText = $this->sb->getUrl() . '/app/'. $project['instance'] .'#mystuff&1=o-project-'. $project['id'] .'-'. rawurlencode($project['name']);

        // Build invoice portal URL for customer receipt
        $baseUrl = $this->sb->getUrl();
        if (strpos($baseUrl, 'localhost') !== false) {
            $portalBaseUrl = 'http://localhost:8080';
        } elseif (strpos($baseUrl, 'bento-dev') !== false) {
            $portalBaseUrl = 'https://bento-dev.infinityhospitality.net';
        } else {
            $portalBaseUrl = 'https://bento.infinityhospitality.net';
        }
        // CORRECT URL: i=instance_name, pid=project_id_number
        $invoicePortalLink = $portalBaseUrl . '/app/invoices#?&i=' . $instanceName . '&pid=' . $project['id'];

        // Manager email addresses
        $managerEmails = array();
        $instanceReplyEmail = '<EMAIL>';

        // Determine environment based on URL
        $currentUrl = $this->sb->getUrl();
        $isLocalhost = (strpos($currentUrl, 'localhost') !== false);
        $isStaging = (strpos($currentUrl, 'bento-dev') !== false);
        $isProduction = !$isLocalhost && !$isStaging;

        // Localhost (rickyvoltz instance only) - use test Mailspons addresses
        if ($isLocalhost && $instanceName == 'rickyvoltz') {
            array_push($managerEmails, "<EMAIL>");
        }
        // Production/Staging - use real manager emails based on instance
        else {
            if ($instanceName == 'infinity' || $instanceName == 'nlp') {
                array_push($managerEmails, "<EMAIL>");
            } elseif ($instanceName == 'dreamcatering') {
                array_push($managerEmails, "<EMAIL>");
                array_push($managerEmails, "<EMAIL>");
            } elseif ($instanceName == 'rickyvoltz') {
                // Production rickyvoltz - use infinity accounting
                array_push($managerEmails, "<EMAIL>");
            }
        }

        // Determine payment method description
        $paymentMethodDescription = '';
        if ($callbackData['method_used'] === 'echeck') {
            // For ACH, use last_4 from callback
            $accountLast4 = isset($callbackData['last_4']) ? $callbackData['last_4'] : 'XXXX';
            $paymentMethodDescription = 'bank account ending in ' . $accountLast4;
        } else {
            $paymentMethodDescription = $callbackData['method_used'] . ' ending in ' . $callbackData['last_4'];
        }

        // Send manager notification with PROFESSIONAL STYLING AND FULL DETAILS
        $managerEmailBody = $this->buildManagerEmailHTML($projectName, $groupObjectUID, $startDate, $paymentAmountFormatted, $paymentFeesFormatted, $totalPaymentFormatted, $urlText, $callbackData['trace_number'], $date, $paymentMethodDescription, $callbackData, $logPaymentsArray, $proposal, $project, $allInvoices);

        $notificationmergevars = (object) array(
            'TITLE' => 'Payment Notification - ' . $projectName,
            'SUBJECT' => '💰 Payment Received: $' . $totalPaymentFormatted . ' for "'. $projectName .'"',
            'BODY' => $managerEmailBody,
            'BUTTON' => 'View Project in Bento',
            'BUTTON_LINK' => $urlText,
            'INSTANCE_NAME' => 'Bento Systems'
        );

        $this->sb->sendEmail($managerEmails, $instanceReplyEmail, 'A payment has been posted to "'. $projectName .'"', $notificationmergevars, null, 0);

        // Send customer receipt
        $receiptEmailAddresses = array();

        // Localhost (rickyvoltz instance only) - use test Mailspons address
        if ($isLocalhost && $instanceName == 'rickyvoltz') {
            array_push($receiptEmailAddresses, "<EMAIL>");
        }
        // Production/Staging - use actual customer email
        else {
            if (!empty($callbackData['billing_email_address'])) {
                array_push($receiptEmailAddresses, $callbackData['billing_email_address']);
            }
        }

        // Send customer receipt with PROFESSIONAL STYLING
        $customerReceiptBody = $this->buildCustomerReceiptHTML($projectName, $proposalId, $paymentMethodDescription, $date, $paymentAmountFormatted, $paymentFeesFormatted, $totalPaymentFormatted, $callbackData['trace_number'], $project);

        $receiptmergevars = (object) array(
            'TITLE' => 'Payment Receipt - ' . $projectName,
            'SUBJECT' => '✅ Payment Confirmation: $' . $totalPaymentFormatted . ' - ' . $projectName,
            'BODY' => $customerReceiptBody,
            'INSTANCE_NAME' => 'Bento Systems'
        );

        $this->sb->sendEmail($receiptEmailAddresses, $instanceReplyEmail, 'Receipt for ' . $projectName . ' Payment', $receiptmergevars, null, 0);
    }

    /**
     * Build professional HTML email for manager notifications
     * MODERN PROFESSIONAL STYLING FOR $15M COMPANY WITH FULL ACCOUNTANT DETAILS
     */
    private function buildManagerEmailHTML($projectName, $eventId, $startDate, $paymentAmount, $paymentFees, $paymentTotal, $projectLink, $confirmationNumber, $date, $paymentMethod, $callbackData, $logPaymentsArray, $proposal, $project, $allInvoices) {
        // Get company branding based on instance - PROPER MAPPING FOR MANAGER EMAIL
        $companyName = 'Bento Systems';

        if ($project['instance'] == 'infinity' || $project['instance'] == 'nlp') {
            $companyName = 'Infinity Hospitality';
        } elseif ($project['instance'] == 'rickyvoltz') {
            $companyName = 'Ricky Voltz';
        } elseif ($project['instance'] == 'dreamcatering') {
            $companyName = 'Dream Events & Catering';
        }

        // Build comprehensive payment details for accountants
        $paymentScheduleInfo = '';
        $invoiceNumbers = array();
        foreach ($logPaymentsArray as $payment) {
            // Get the actual invoice to get its number/name
            $invoiceRecord = null;
            foreach ($allInvoices as $inv) {
                if ($inv['id'] == $payment['invoice']) {
                    $invoiceRecord = $inv;
                    break;
                }
            }
            if ($invoiceRecord) {
                $invoiceNumbers[] = '#' . $invoiceRecord['id'];
            }
        }
        $paymentScheduleInfo = implode('-', $invoiceNumbers) . ' : Balance';

        // Get customer details
        $mainContactName = isset($project['main_contact_name']) ? $project['main_contact_name'] : 'N/A';
        $billingEmail = isset($callbackData['billing_email_address']) ? $callbackData['billing_email_address'] : 'N/A';

        // Format card expiry if available
        $cardExpiry = '';
        if (isset($callbackData['expire_month']) && isset($callbackData['expire_year'])) {
            $cardExpiry = ' (expires ' . $callbackData['expire_month'] . '/' . $callbackData['expire_year'] . ')';
        }

        return '
        <div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, Helvetica, Arial, sans-serif; max-width: 700px; margin: 0 auto; background: #ffffff;">

            <!-- Header -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
                <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 600;">💰 Payment Received</h1>
                <p style="color: #e8f0fe; margin: 10px 0 0 0; font-size: 16px;">CSG Forte payment processed successfully</p>
            </div>

            <!-- PAYMENT DETAILS SECTION -->
            <div style="background: #f8fafc; margin: 0; padding: 25px; border-left: 4px solid #10b981;">
                <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 20px; font-weight: 600; border-bottom: 2px solid #e5e7eb; padding-bottom: 8px;">💳 PAYMENT DETAILS</h2>

                <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                    <tr style="border-bottom: 1px solid #e5e7eb;">
                        <td style="padding: 8px 0; color: #6b7280; font-weight: 500; width: 180px;">Payment Schedule:</td>
                        <td style="padding: 8px 0; color: #1f2937; font-weight: 600;">' . $paymentScheduleInfo . '</td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e5e7eb;">
                        <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Amount:</td>
                        <td style="padding: 8px 0; color: #1f2937; font-weight: 600;">$' . $paymentAmount . '</td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e5e7eb;">
                        <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Fees:</td>
                        <td style="padding: 8px 0; color: #1f2937; font-weight: 600;">$' . $paymentFees . '</td>
                    </tr>
                    <tr style="border-bottom: 2px solid #10b981;">
                        <td style="padding: 12px 0; color: #1f2937; font-weight: 700; font-size: 16px;">Total:</td>
                        <td style="padding: 12px 0; color: #10b981; font-weight: 700; font-size: 18px;">$' . $paymentTotal . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">View in Bento:</td>
                        <td style="padding: 8px 0;"><a href="' . $projectLink . '" style="color: #6366f1; text-decoration: none; font-weight: 600;">View Event</a></td>
                    </tr>
                </table>
            </div>

            <!-- CSG FORTE TRANSACTION SECTION -->
            <div style="background: #ffffff; padding: 25px; border-left: 4px solid #6366f1;">
                <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 20px; font-weight: 600; border-bottom: 2px solid #e5e7eb; padding-bottom: 8px;">🏦 CSG FORTE TRANSACTION</h2>

                <table style="width: 100%; border-collapse: collapse;">
                    <tr style="border-bottom: 1px solid #e5e7eb;">
                        <td style="padding: 8px 0; color: #6b7280; font-weight: 500; width: 180px;">Transaction ID:</td>
                        <td style="padding: 8px 0; color: #059669; font-family: monospace; font-size: 14px; background: #ecfdf5; padding: 4px 8px; border-radius: 4px;">' . $confirmationNumber . '</td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e5e7eb;">
                        <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Authorization Code:</td>
                        <td style="padding: 8px 0; color: #1f2937; font-weight: 600;">' . (isset($callbackData['authorization_code']) ? $callbackData['authorization_code'] : 'N/A') . '</td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e5e7eb;">
                        <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Total Amount Processed:</td>
                        <td style="padding: 8px 0; color: #10b981; font-weight: 700;">$' . $paymentTotal . '</td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e5e7eb;">
                        <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Payment Succeeded:</td>
                        <td style="padding: 8px 0; color: #1f2937; font-weight: 600;">' . $date . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Response Code:</td>
                        <td style="padding: 8px 0; color: #059669; font-weight: 600;">' . (isset($callbackData['response_code']) ? $callbackData['response_code'] : 'N/A') . ' - ' . (isset($callbackData['response_description']) ? $callbackData['response_description'] : 'N/A') . '</td>
                    </tr>
                </table>
            </div>

            <!-- CUSTOMER DETAILS SECTION -->
            <div style="background: #f8fafc; padding: 25px; border-left: 4px solid #f59e0b;">
                <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 20px; font-weight: 600; border-bottom: 2px solid #e5e7eb; padding-bottom: 8px;">👤 CUSTOMER DETAILS</h2>

                <table style="width: 100%; border-collapse: collapse;">
                    <tr style="border-bottom: 1px solid #e5e7eb;">
                        <td style="padding: 8px 0; color: #6b7280; font-weight: 500; width: 180px;">Billing Name:</td>
                        <td style="padding: 8px 0; color: #1f2937; font-weight: 600;">' . (isset($callbackData['billing_name']) ? $callbackData['billing_name'] : 'N/A') . '</td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e5e7eb;">
                        <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Payment Method:</td>
                        <td style="padding: 8px 0; color: #1f2937; font-weight: 600;">' . ucfirst($paymentMethod) . $cardExpiry . '</td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e5e7eb;">
                        <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Email:</td>
                        <td style="padding: 8px 0; color: #1f2937; font-weight: 600;">' . $billingEmail . '</td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e5e7eb;">
                        <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Event:</td>
                        <td style="padding: 8px 0; color: #1f2937; font-weight: 600;">#' . $eventId . ' ' . $projectName . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Event Date:</td>
                        <td style="padding: 8px 0; color: #1f2937; font-weight: 600;">' . date('m/d/y, g:i A', strtotime($startDate)) . '</td>
                    </tr>
                </table>
            </div>

            <!-- Footer -->
            <div style="background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; border-top: 1px solid #e5e7eb;">
                <p style="color: #6b7280; margin: 0; font-size: 14px;">
                    This is an automated notification from your Bento payment system.<br>
                    <strong>' . $companyName . '</strong> • CSG Forte Integration
                </p>
            </div>
        </div>';
    }

    /**
     * Build professional HTML email for customer receipts
     * MODERN PROFESSIONAL STYLING FOR $15M COMPANY WITH INVOICE PORTAL LINK
     */
    private function buildCustomerReceiptHTML($projectName, $proposalId, $paymentMethod, $date, $paymentAmount, $paymentFees, $paymentTotal, $confirmationNumber, $project) {
        // Build invoice portal URL based on environment
        $baseUrl = $this->sb->getUrl();
        if (strpos($baseUrl, 'localhost') !== false) {
            $portalUrl = 'http://localhost:8080';
        } elseif (strpos($baseUrl, 'bento-dev') !== false) {
            $portalUrl = 'https://bento-dev.infinityhospitality.net';
        } else {
            $portalUrl = 'https://bento.infinityhospitality.net';
        }

        // CORRECT URL: i=instance_name, pid=project_id_number
        $invoicePortalLink = $portalUrl . '/app/invoices#?&i=' . $project['instance'] . '&pid=' . $project['id'];

        // Get company branding based on instance - PROPER MAPPING
        $companyName = 'Bento Systems';
        $companyTagline = 'Professional Event Management';
        $companyLogo = '';

        if ($project['instance'] == 'infinity' || $project['instance'] == 'nlp') {
            $companyName = 'Infinity Hospitality';
            $companyTagline = 'Professional Event Management';
            $companyLogo = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/' . $project['instance'] . '/logo.png';
        } elseif ($project['instance'] == 'rickyvoltz') {
            $companyName = 'Ricky Voltz';
            $companyTagline = 'Professional Event Management';
            $companyLogo = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/rickyvoltz/logo.png';
        } elseif ($project['instance'] == 'dreamcatering') {
            $companyName = 'Dream Events & Catering';
            $companyTagline = 'Professional Catering Services';
            $companyLogo = 'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_instances/dreamcatering/logo.png';
        }
        return '
        <div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff;">

            <!-- Header with Company Branding -->
            <div style="background: linear-gradient(135deg, #10b981 0%, #**********%); padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
                <div style="margin-bottom: 15px;">
                    <h2 style="color: #ffffff; margin: 0; font-size: 24px; font-weight: 700;">' . $companyName . '</h2>
                    <p style="color: #d1fae5; margin: 5px 0 0 0; font-size: 14px;">' . $companyTagline . '</p>
                </div>
                <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 600;">✅ Payment Confirmed</h1>
                <p style="color: #d1fae5; margin: 10px 0 0 0; font-size: 16px;">Thank you for your payment</p>
            </div>

            <!-- Receipt Details -->
            <div style="padding: 30px; background: #ffffff;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h2 style="color: #1f2937; margin: 0 0 10px 0; font-size: 32px; font-weight: 700;">$' . $paymentTotal . '</h2>
                    <p style="color: #6b7280; margin: 0; font-size: 16px;">Payment successfully processed</p>
                </div>

                <!-- Payment Breakdown -->
                <div style="background: #f8fafc; padding: 25px; border-radius: 8px; border: 1px solid #e5e7eb; margin-bottom: 25px;">
                    <h3 style="color: #1f2937; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">💳 Payment Details</h3>

                    <table style="width: 100%; border-collapse: collapse;">
                        <tr style="border-bottom: 1px solid #e5e7eb;">
                            <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Payment Amount:</td>
                            <td style="padding: 8px 0; color: #1f2937; font-weight: 600; text-align: right;">$' . $paymentAmount . '</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e5e7eb;">
                            <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Processing Fees:</td>
                            <td style="padding: 8px 0; color: #1f2937; font-weight: 600; text-align: right;">$' . $paymentFees . '</td>
                        </tr>
                        <tr style="border-bottom: 2px solid #10b981;">
                            <td style="padding: 12px 0; color: #1f2937; font-weight: 700; font-size: 16px;">Total Paid:</td>
                            <td style="padding: 12px 0; color: #10b981; font-weight: 700; font-size: 18px; text-align: right;">$' . $paymentTotal . '</td>
                        </tr>
                    </table>
                </div>

                <!-- Transaction Info -->
                <div style="margin-bottom: 25px;">
                    <h3 style="color: #1f2937; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">📄 Transaction Information</h3>

                    <table style="width: 100%; border-collapse: collapse;">
                        <tr style="border-bottom: 1px solid #e5e7eb;">
                            <td style="padding: 8px 0; color: #6b7280; font-weight: 500; width: 140px;">Event:</td>
                            <td style="padding: 8px 0; color: #1f2937; font-weight: 500;">' . $projectName . '</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e5e7eb;">
                            <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Proposal ID:</td>
                            <td style="padding: 8px 0; color: #6366f1; font-weight: 500;">#' . $proposalId . '</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e5e7eb;">
                            <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Payment Method:</td>
                            <td style="padding: 8px 0; color: #1f2937; font-weight: 500;">' . ucfirst($paymentMethod) . '</td>
                        </tr>
                        <tr style="border-bottom: 1px solid #e5e7eb;">
                            <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Date & Time:</td>
                            <td style="padding: 8px 0; color: #1f2937; font-weight: 500;">' . $date . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Confirmation:</td>
                            <td style="padding: 8px 0;"><span style="color: #059669; font-family: monospace; font-size: 14px; background: #ecfdf5; padding: 4px 8px; border-radius: 4px;">' . $confirmationNumber . '</span></td>
                        </tr>
                    </table>
                </div>

                <!-- Invoice Portal Link -->
                <div style="text-align: center; margin-bottom: 25px;">
                    <a href="' . $invoicePortalLink . '" style="display: inline-block; background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); color: #ffffff; text-decoration: none; padding: 12px 24px; border-radius: 8px; font-weight: 600; font-size: 16px;">
                        📄 View All Invoices
                    </a>
                    <p style="color: #6b7280; margin: 10px 0 0 0; font-size: 14px;">Access your complete invoice history and payment details</p>
                </div>

                <!-- Important Notice -->
                <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
                    <h4 style="color: #92400e; margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">📋 Keep This Receipt</h4>
                    <p style="color: #92400e; margin: 0; font-size: 14px; line-height: 1.5;">
                        Please save this email as your payment receipt. If you have any questions about this transaction or your event, please contact your event coordinator.
                    </p>
                </div>
            </div>

            <!-- Footer -->
            <div style="background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; border-top: 1px solid #e5e7eb;">
                <p style="color: #6b7280; margin: 0 0 10px 0; font-size: 14px;">
                    <strong>Thank you for choosing ' . $companyName . '</strong>
                </p>
                <p style="color: #9ca3af; margin: 0; font-size: 12px;">
                    This receipt was generated automatically. Please do not reply to this email.
                </p>
            </div>
        </div>';
    }
}
